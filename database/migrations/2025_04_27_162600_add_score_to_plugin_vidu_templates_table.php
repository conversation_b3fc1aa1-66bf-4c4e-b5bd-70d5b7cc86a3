<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plugin_vidu_templates', function (Blueprint $table) {
            $table->integer('score')->default(0)
                ->comment('消耗积分')
                ->index()
                ->after('scene');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plugin_vidu_templates', function (Blueprint $table) {
            $table->dropColumn('score');
        });
    }
};
