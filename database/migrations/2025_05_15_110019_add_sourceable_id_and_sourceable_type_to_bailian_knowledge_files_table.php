<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_knowledge_files', function (Blueprint $table) {
            $table->unsignedBigInteger('knowledge_id')->index()->after('id');
            $table->unsignedBigInteger('sourceable_id')->nullable()->index()->after('knowledge_id');
            $table->string('sourceable_type')->nullable()->after('sourceable_id');
            $table->index(['sourceable_id', 'sourceable_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_knowledge_files', function (Blueprint $table) {
            $table->dropColumn(['sourceable_id', 'sourceable_type', 'knowledge_id']);
        });
    }
};
