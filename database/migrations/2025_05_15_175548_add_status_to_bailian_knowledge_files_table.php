<?php

use App\Models\BailianKnowledgeFile;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_knowledge_files', function (Blueprint $table) {
            $table->string('status')->default(BailianKnowledgeFile::STATUS_INIT)->after('parser');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_knowledge_files', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
