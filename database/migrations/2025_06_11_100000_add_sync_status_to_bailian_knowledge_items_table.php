<?php

use App\Enums\Bailian\ItemSyncStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_knowledge_items', function (Blueprint $table) {
            $table->enum('sync_status', ItemSyncStatusEnum::values())
                ->default(ItemSyncStatusEnum::PENDING)
                ->after('order')
                ->comment('同步状态：pending-待同步，syncing-同步中，synced-已同步，failed-失败');

            $table->text('sync_error')->nullable()->after('sync_status')->comment('同步错误信息');
            $table->timestamp('synced_at')->nullable()->after('sync_error')->comment('同步完成时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_knowledge_items', function (Blueprint $table) {
            $table->dropColumn(['sync_status', 'sync_error', 'synced_at']);
        });
    }
};
