<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('publish_media', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID')->index();
            $table->morphs('item');
            $table->string('type')->comment('类型')->index();
            $table->string('title')->comment('标题')->index();
            $table->unsignedInteger('category_id')->comment('分类ID')->index();
            $table->json('tags')->nullable()->comment('标签');
            $table->text('description')->nullable()->comment('描述');
            $table->string('cover')->nullable()->comment('封面');
            $table->json('pictures')->nullable()->comment('图片集锦');
            $table->string('audio')->nullable()->comment('音频');
            $table->string('video')->nullable()->comment('视频');
            $table->json('ext')->nullable()->comment('拓展信息');
            $table->boolean('status')->default(0)->comment('状态')->index();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('publish_media');
    }
};
