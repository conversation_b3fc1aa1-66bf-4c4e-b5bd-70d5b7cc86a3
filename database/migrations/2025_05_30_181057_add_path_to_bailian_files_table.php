<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_files', function (Blueprint $table) {
            $table->string('path')->nullable()->comment('文件路径');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_files', function (Blueprint $table) {
            $table->dropColumn('path');
        });
    }
};
