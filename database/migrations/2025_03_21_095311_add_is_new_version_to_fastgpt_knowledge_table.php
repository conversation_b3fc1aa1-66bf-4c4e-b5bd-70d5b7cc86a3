<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fastgpt_knowledge', function (Blueprint $table) {
            $table->boolean('is_new_version')->default(false)->comment('是否使用新版fastgpt')->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fastgpt_knowledge', function (Blueprint $table) {
            $table->dropColumn('is_new_version');
        });
    }
};
