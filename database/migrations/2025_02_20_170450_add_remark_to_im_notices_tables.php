<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('im_notices', function (Blueprint $table) {
            $table->string('remark')->nullable()->comment('返回结果')->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('im_notices_tables', function (Blueprint $table) {
            $table->dropColumn('remark');
        });
    }
};
