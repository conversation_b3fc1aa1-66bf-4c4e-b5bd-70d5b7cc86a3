<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_assistant_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('chat_assistant_id');
            $table->decimal('lat', 12, 8)->nullable();
            $table->decimal('lng', 12, 8)->nullable();
            $table->json('intent')->nullable()->comment('意图');
            $table->json('tools')->nullable()->comment('工具');
            $table->string('message')->nullable()->comment('消息');
            $table->string('file_url')->nullable()->comment('文件地址');
            $table->json('intent_token')->nullable()->comment('意图使用token');
            $table->json('tools_token')->nullable()->comment('工具分析使用token');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_assistant_logs');
    }
};
