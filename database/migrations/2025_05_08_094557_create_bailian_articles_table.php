<?php

use App\Enums\Bailian\BailianArticleTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_articles', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->nullable()->index();
            $table->unsignedInteger('parent_id')->nullable()->index();
            $table->string('title')->comment('标题');
            $table->text('content');
            $table->integer('status')->default(1);
            $table->integer('browse_count')->default(0);
            $table->enum('type', BailianArticleTypeEnum::values())
                ->default(BailianArticleTypeEnum::ARTICLE->value);
            $table->timestamps();
            $table->comment('百炼知识库文章');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_articles');
    }
};
