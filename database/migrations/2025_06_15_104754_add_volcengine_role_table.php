<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ai_chat_engines', function (Blueprint $table) {
            $table->tinyInteger('is_websocket')->default(0)->comment('是否使用端到端模型');
            $table->string('default_role_audio')->default('')->comment('角色音频');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('is_websocket');
        Schema::dropIfExists('default_role_audio');
    }
};
