<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plugin_vidu_templates', function (Blueprint $table) {
            $table->text('prompt_zh')->nullable()->comment('中文提示词')->after('prompt');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plugin_vidu_templates', function (Blueprint $table) {
            $table->dropColumn('prompt_zh');
        });
    }
};
