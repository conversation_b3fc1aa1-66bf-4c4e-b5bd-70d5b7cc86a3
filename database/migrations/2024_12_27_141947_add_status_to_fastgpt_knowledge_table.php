<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fastgpt_knowledge', function (Blueprint $table) {
            $table->boolean('status')->default(1)->after('level');
            $table->softDeletes()->after('updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fastgpt_knowledge', function (Blueprint $table) {
            $table->dropColumn(['status', 'deleted_at', 'updated_at']);
        });
    }
};
