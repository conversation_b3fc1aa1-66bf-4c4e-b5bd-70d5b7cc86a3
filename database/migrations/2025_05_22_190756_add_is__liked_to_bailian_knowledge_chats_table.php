<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_knowledge_chats', function (Blueprint $table) {
            $table->tinyInteger('is_liked')->nullable()->index()->after('output_message');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_knowledge_chats', function (Blueprint $table) {
            $table->dropColumn('is_liked');
        });
    }
};
