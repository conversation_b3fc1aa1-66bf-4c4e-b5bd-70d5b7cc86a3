<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fastgpt_chats', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('fastgpt_app_id')->comment('关联应用')->index();
            $table->unsignedBigInteger('user_id')->nullable()->comment('关联用户')->index();
            $table->string('chat_id');
            $table->string('title')->comment('聊天标题');
            $table->string('custom_title')->nullable()->comment('自定义标题');
            $table->string('message')->comment('聊天内容');
            $table->string('user_data_id')->nullable();
            $table->string('ai_data_id')->nullable();
            $table->boolean('is_top')->default(0)->comment('是否置顶');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fastgpt_chats');
    }
};
