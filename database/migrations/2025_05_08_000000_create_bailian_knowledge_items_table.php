<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_knowledge_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('knowledge_id')->index();
            $table->unsignedBigInteger('user_id')->index();
            $table->unsignedBigInteger('parent_id')->nullable()->index();
            $table->morphs('itemable');
            $table->string('title');
            $table->string('description')->nullable();
            $table->integer('order')->default(0)->comment('排序顺序');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_knowledge_items');
    }
};
