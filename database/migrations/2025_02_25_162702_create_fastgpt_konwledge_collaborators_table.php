<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fastgpt_konwledge_collaborators', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('knowledge_id')->index();
            $table->unsignedBigInteger('user_id')->index();
            $table->unsignedBigInteger('knowledge_set_id')->nullable()->index();
            $table->boolean('is_manager')->default(0);
            $table->timestamps();
            $table->comment('协作者表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fastgpt_konwledge_collaborators');
    }
};
