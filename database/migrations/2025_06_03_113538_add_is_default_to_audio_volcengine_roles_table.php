<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('audio_volcengine_roles', function (Blueprint $table) {
            $table->boolean('is_default')->default(false)->after('prologue');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('audio_volcengine_roles', function (Blueprint $table) {
            //
        });
    }
};
