<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plugin_ai_ppt_outlines', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->string('title')->index();
            $table->json('outline');
            $table->string('audience', 50)->nullable();
            $table->string('scene', 50)->nullable();
            $table->string('tone', 50)->nullable();
            $table->string('language', 50)->nullable();
            $table->json('content')->nullable();
            $table->string('theme', 20)->nullable();
            $table->string('file_url')->nullable();
            $table->boolean('status')->default(0)->index();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plugin_ai_ppt_outlines');
    }
};
