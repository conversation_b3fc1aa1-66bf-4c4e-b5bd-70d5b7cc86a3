<?php

use App\Models\BailianKnowledgeFile;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_assistant_files', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index()->comment('用户id');
            $table->unsignedBigInteger('log_id')->index()->nullable()->comment('记录ID');
            $table->unsignedBigInteger('storage_id')->index()->comment('存储id');
            $table->string('bailian_category_id')->default('default')->comment('百炼分类ID');
            $table->string('name')->comment('文件名称');
            $table->string('workspace_id')->nullable()->comment('类目所属的业务空间 ID');
            $table->string('lease_id')->nullable()->comment('文档上传租约 ID');
            $table->string('file_id')->nullable()->index()->comment('阿里云文件ID');
            $table->string('parser')->nullable()->comment('解析器类型');
            $table->string('category_type')->nullable()->comment('类目类型');
            $table->string('status')->default(BailianKnowledgeFile::STATUS_INIT)->comment('状态');
            $table->timestamps();
            $table->comment('智能体文件');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_assistant_files');
    }
};
