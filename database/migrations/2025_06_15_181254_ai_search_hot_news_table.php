<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_search_hot_news', function (Blueprint $table) {
            $table->id();
            $table->string('category')->nullable()->index();
            $table->string('title')->nullable()->index();
            $table->string('icon_url')->nullable();
            $table->tinyInteger('sort')->default(0)->nullable()->index();
            $table->string('desc')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_search_hot_news');
    }
};
