<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audio_volcengine_tts', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('音色名称');
            $table->string('scene')->comment('推荐场景');
            $table->string('language')->comment('语种');
            $table->string('voice_type')->comment('Voice_type');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audio_volcengine_tts');
    }
};
