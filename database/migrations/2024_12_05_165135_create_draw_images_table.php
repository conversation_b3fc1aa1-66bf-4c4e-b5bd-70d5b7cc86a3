<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('draw_images', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->string('no');
            $table->unsignedInteger('score')->default(0);
            $table->string('prompt');
            $table->string('image_url')->nullable();
            $table->unsignedInteger('size_id');
            $table->unsignedInteger('style_id');
            $table->string('cover')->nullable();
            $table->string('server_image')->nullable();
            $table->boolean('status')->default(0)->index();
            $table->timestamp('over_at')->nullable()->index();
            $table->string('error_message')->nullable();
            $table->json('source')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('draw_images');
    }
};
