<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('adverts', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('标题');
            $table->text('description')->nullable()->comment('描述');
            $table->string('cover')->comment('图片');
            $table->string('jump_target')->nullable()->comment('跳转的地址');
            $table->string('mini_program_id')->nullable();   // 小程序ID
            $table->string('position')->nullable()->comment('使用的位置')->index();
            $table->string('type')->comment('类型')->index();
            $table->tinyInteger('status')->default(1)->comment('状态 1：正常 2：禁用');
            $table->json('params')->nullable()->comment('附加参数');
            $table->integer('order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('adverts');
    }
};
