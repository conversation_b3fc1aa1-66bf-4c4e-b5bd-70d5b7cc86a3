<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plugin_jm_tts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('no');
            $table->string('text');
            $table->string('name');
            $table->unsignedDecimal('speed', 3, 1)->default(0);
            $table->unsignedBigInteger('category_id')->default(0);
            $table->string('category_key')->default('all');
            $table->string('tone_id');
            $table->string('duration')->nullable();
            $table->string('vid')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plugin_jm_tts');
    }
};
