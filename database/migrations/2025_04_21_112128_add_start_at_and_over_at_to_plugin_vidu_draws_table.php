<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plugin_vidu_draws', function (Blueprint $table) {
            $table->timestamp('start_at')->nullable();
            $table->timestamp('over_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plugin_vidu_draws', function (Blueprint $table) {
            //
        });
    }
};
