<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plugin_mj_draws', function (Blueprint $table) {
            $table->id();
            $table->string('type')->index();
            $table->unsignedBigInteger('user_id')->index();
            $table->string('no')->index();
            $table->string('prompt')->nullable();
            $table->string('task_id')->nullable();
            $table->string('serial_no')->nullable();
            $table->string('cover')->nullable();
            $table->json('inputs')->nullable();
            $table->json('params')->nullable();
            $table->boolean('status')->default(0)->index();
            $table->string('error_message')->nullable();
            $table->timestamp('start_at')->nullable();
            $table->timestamp('over_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plugin_mj_draws');
    }
};
