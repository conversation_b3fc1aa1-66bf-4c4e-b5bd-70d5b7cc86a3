<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_unify_assets', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->string('type')->index();
            $table->string('no');
            $table->morphs('assetable');
            $table->unsignedInteger('category_id')->nullable()->index();
            $table->text('description')->nullable();
            $table->boolean('status')->default(1)->index();
            $table->boolean('platform')->default(0)->index();
            $table->json('ext')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_unify_assets');
    }
};
