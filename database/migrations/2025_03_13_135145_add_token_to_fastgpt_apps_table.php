<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fastgpt_apps', function (Blueprint $table) {
            $table->string('api_token')->nullable()->comment('应用api token')->after('app_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fastgpt_apps', function (Blueprint $table) {
            $table->dropColumn('api_token');
        });
    }
};
