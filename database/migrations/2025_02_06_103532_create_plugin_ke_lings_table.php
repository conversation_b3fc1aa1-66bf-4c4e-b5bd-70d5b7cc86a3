<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plugin_ke_lings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->string('no');
            $table->string('prompt');
            $table->string('type');
            $table->string('image_url')->nullable();
            $table->string('task_id')->nullable();
            $table->json('params')->nullable();
            $table->string('cover')->nullable();
            $table->string('video_url')->nullable();
            $table->boolean('status')->default(0);
            $table->timestamp('over_at')->nullable();
            $table->timestamp('start_at')->nullable();
            $table->string('error_message')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plugin_ke_lings');
    }
};
