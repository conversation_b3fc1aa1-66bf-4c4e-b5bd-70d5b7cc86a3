<?php

use App\Enums\Bailian\ArticleProcessingStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_articles', function (Blueprint $table) {
            $table->enum('processing_status', ArticleProcessingStatusEnum::values())
                ->after('type')
                ->default(ArticleProcessingStatusEnum::COMPLETED->value);

            $table->string('xfyun_task_id')->nullable()->after('processing_status')->comment('讯飞CKM任务ID');
            $table->text('processing_error')->nullable()->after('xfyun_task_id')->comment('处理错误信息');
            $table->timestamp('processing_started_at')->nullable()->after('processing_error')->comment('开始处理时间');
            $table->timestamp('processing_completed_at')->nullable()->after('processing_started_at')
                ->comment('完成处理时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_articles', function (Blueprint $table) {
            $table->dropColumn([
                'processing_status',
                'xfyun_task_id',
                'processing_error',
                'processing_started_at',
                'processing_completed_at'
            ]);
        });
    }
};
