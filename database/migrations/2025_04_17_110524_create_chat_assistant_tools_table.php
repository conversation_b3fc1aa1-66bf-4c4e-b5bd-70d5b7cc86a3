<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_assistant_tools', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('log_id');
            $table->unsignedInteger('step')->default(1);
            $table->string('key')->nullable();
            $table->string('name')->nullable();
            $table->json('params')->nullable();
            $table->json('results')->nullable();
            $table->longText('response')->nullable();
            $table->longText('reasoning')->nullable();
            $table->json('token')->nullable()->comment('工具使用的token');
            $table->timestamps();
            $table->foreign('log_id')
                ->references('id')
                ->on('chat_assistant_logs')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_assistant_tools');
    }
};
