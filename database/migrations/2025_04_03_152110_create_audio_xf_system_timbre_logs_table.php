<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audio_xf_timbre_logs', function (Blueprint $table) {
            $table->id();
            $table->string('task_id')->index();
            $table->string('text_id')->index();
            $table->string('seg_id');
            $table->string('audio_url');
            $table->boolean('flag')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audio_xf_system_timbre_logs');
    }
};
