<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->unsignedBigInteger('group_id')->comment('群组ID');
            $table->text('system')->nullable()->comment('系统前置信息');
            $table->text('message')->nullable()->comment('用户内容');
            $table->json('images')->nullable()->comment('图片');
            $table->string('audio')->nullable()->comment('音频');
            $table->string('video')->nullable()->comment('视频');
            $table->longText('response')->nullable()->comment('回答内容');
            $table->longText('reasoning')->nullable()->comment('深度思考');
            $table->unsignedBigInteger('tokens')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_logs');
    }
};
