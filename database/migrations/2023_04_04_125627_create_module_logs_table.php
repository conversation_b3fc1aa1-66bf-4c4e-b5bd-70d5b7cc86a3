<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('module_logs', function (Blueprint $table) {
            $table->id();
            $table->string('name')
                ->comment('模块名称');
            $table->morphs('user');
            $table->string('action', 16)
                ->comment('操作');
            $table->string('version', 16)
                ->nullable()
                ->comment('版本');
            $table->json('config')
                ->nullable()
                ->comment('初始化配置');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('module_logs');
    }
};
