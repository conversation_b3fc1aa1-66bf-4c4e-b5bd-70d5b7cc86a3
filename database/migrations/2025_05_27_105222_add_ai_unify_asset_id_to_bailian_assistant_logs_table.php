<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_assistant_logs', function (Blueprint $table) {
            $table->unsignedBigInteger('ai_unify_asset_id')->nullable()->after('id'); // 假设 'id' 是主键
            $table->foreign('ai_unify_asset_id')
                ->references('id')->on('ai_unify_assets')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_assistant_logs', function (Blueprint $table) {
            $table->dropForeign(['ai_unify_asset_id']);
            $table->dropColumn('ai_unify_asset_id');
        });
    }
};
