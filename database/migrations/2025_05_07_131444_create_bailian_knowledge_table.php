<?php

use App\Enums\Bailian\BailianLevelEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_knowledge', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->index()->comment('用户ID');
            $table->string('workspace_id')->index()->comment('业务空间 ID');
            $table->string('knowledge_id')->nullable()->index()->comment('阿里云知识库ID');
            $table->string('bailian_name')->comment('阿里云知识库名称');
            $table->string('name')->comment('知识库名称');
            $table->string('description')->nullable()->comment('知识库描述');
            $table->string('structure_type')->comment('知识库的数据类型:默认unstructured');
            $table->string('source_type')->comment('知识库来源类型:默认DATA_CENTER_CATEGORY');
            $table->string('sink_type')->comment('知识库的向量存储类型:默认BUILT_IN');
            $table->boolean('status')->default(1)->comment('状态');
            $table->string('error_message')->nullable()->comment('错误信息');
            $table->json('config')->nullable()->comment('配置信息');
            //成员是否自动加入
            $table->boolean('auto_join')->default(0)->comment('成员是否自动加入');
            //是否精选
            $table->boolean('is_featured')->default(0)->comment('是否精选');
            $table->enum('level', BailianLevelEnum::values())
                ->default(BailianLevelEnum::PRIVATE->value);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_knowledge');
    }
};
