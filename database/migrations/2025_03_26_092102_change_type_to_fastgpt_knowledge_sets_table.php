<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE fastgpt_knowledge_sets MODIFY COLUMN type ENUM('empty', 'text', 'link', 'file', 'note', 'directory','article')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fastgpt_knowledge_sets', function (Blueprint $table) {
            //
        });
    }
};
