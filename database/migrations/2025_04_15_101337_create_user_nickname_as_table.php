<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_nickname_as', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->unsignedBigInteger('target_id')->comment('目标用户ID');
            $table->string('nickname')->nullable()->comment('昵称');
            $table->boolean('black')->default(0)->comment('是否黑名单');
            $table->boolean('dont_show')->default(0)->comment('是否不显示');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_nickname_as');
    }
};
