<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cards', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('batch_id')->index();
            $table->unsignedInteger('user_id')->nullable()->index();
            $table->unsignedInteger('score')->default(0);
            $table->string('no')->index();
            $table->string('secret');
            $table->boolean('status')->default(1)->index();
            $table->timestamp('used_at')->nullable()->index();
            $table->timestamp('failure_at')->nullable()->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cards');
    }
};
