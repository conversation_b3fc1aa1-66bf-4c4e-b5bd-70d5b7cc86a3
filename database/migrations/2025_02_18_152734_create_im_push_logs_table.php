<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('im_push_logs', function (Blueprint $table) {
            $table->id();
            $table->text('url')->comment('地址');
            $table->json('query')->nullable()->comment('url参数');
            $table->json('params')->comment('提交的数据');
            $table->json('result')->comment('返回结果');
            $table->boolean('status')->default(0)->comment('状态');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('im_push_logs');
    }
};
