<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_assistant_log_actions', function (Blueprint $table) {
            $table->json('observation')->nullable()->after('arguments');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_assistant_log_actions', function (Blueprint $table) {
            //
        });
    }
};
