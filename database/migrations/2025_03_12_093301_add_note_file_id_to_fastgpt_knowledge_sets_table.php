<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fastgpt_knowledge_sets', function (Blueprint $table) {
            $table->unsignedBigInteger('note_file_id')->nullable()->after('note_id')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fastgpt_knowledge_sets', function (Blueprint $table) {
            $table->dropColumn('note_file_id');
        });
    }
};
