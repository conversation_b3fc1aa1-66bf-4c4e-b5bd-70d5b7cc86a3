<?php

use App\Enums\Bailian\BailianArticleTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 修改enum类型，添加meeting选项
        DB::statement("ALTER TABLE bailian_articles MODIFY COLUMN type ENUM('article', 'record', 'meeting', 'link', 'image') DEFAULT 'article'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 回滚时移除meeting类型，但需要先将meeting类型的记录改为record
        DB::statement("UPDATE bailian_articles SET type = 'record' WHERE type = 'meeting'");
        DB::statement("ALTER TABLE bailian_articles MODIFY COLUMN type ENUM('article', 'record', 'link', 'image') DEFAULT 'article'");
    }
};
