<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_tools', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('category_id')->comment('关联分类')->index();
            $table->string('name');
            $table->string('cover')->nullable();
            $table->string('description')->nullable();
            $table->boolean('status')->default(1);
            $table->integer('times')->comment('使用次数')->default(0);
            $table->integer('price')->comment('价格')->default(0);
            $table->integer('order')->comment('排序')->default(0);
            $table->string('url')->nullable()->comment('链接');
            $table->timestamps();
            $table->comment('AI工具表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_tools');
    }
};
