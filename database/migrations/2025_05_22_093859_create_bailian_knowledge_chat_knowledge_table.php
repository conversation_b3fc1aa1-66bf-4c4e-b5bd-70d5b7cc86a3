<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_knowledge_chat_knowledge', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('bailian_chat_id')->index();
            $table->unsignedBigInteger('knowledge_id')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_knowledge_chat_knowledge');
    }
};
