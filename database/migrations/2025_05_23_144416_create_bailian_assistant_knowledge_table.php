<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_assistant_knowledge', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('log_id')->comment('对话ID');
            $table->string('knowledge_id')->comment('知识库ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_assistant_knowledge');
    }
};
