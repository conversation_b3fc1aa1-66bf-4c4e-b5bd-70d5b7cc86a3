<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_identity_scores', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedInteger('score');
            $table->unsignedInteger('period')->nullable();
            $table->boolean('status')->default(0)->comment('状态');
            $table->timestamp('send_at')->comment('预计发放时间');
            $table->timestamp('over_at')->nullable()->comment('实际发放时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_identity_scores');
    }
};
