<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('note_taggable', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('note_tag_id')
                ->index();
            $table->unsignedBigInteger('note_id')
                ->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('note_taggables');
    }
};
