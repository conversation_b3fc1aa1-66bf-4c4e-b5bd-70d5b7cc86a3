<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('audio_volcengine_tts', function (Blueprint $table) {
            $table->boolean('voiceclone')->default(false)->comment('是否是克隆声音');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('audio_volcengine_tts', function (Blueprint $table) {
            //
        });
    }
};
