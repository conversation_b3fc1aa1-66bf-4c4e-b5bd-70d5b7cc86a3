<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audio_singers', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->index();
            $table->string('no')->nullable();
            $table->string('name')->nullable();
            $table->string('cover')->nullable();
            $table->string('audio')->nullable();
            $table->string('job_id')->nullable();
            $table->boolean('status')->default(0)->index();
            $table->json('source')->nullable();
            $table->string('error_message')->nullable();
            $table->timestamp('over_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audio_singers');
    }
};
