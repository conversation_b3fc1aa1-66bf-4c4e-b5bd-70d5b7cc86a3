<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recharge_packages', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('充值包名称');
            $table->string('hint')->nullable()->comment('提示');
            $table->string('remark')->nullable()->comment('描述');
            $table->decimal('price')->default(0)->comment('金额');
            $table->unsignedInteger('score')->default(0)->comment('积分');
            $table->unsignedInteger('effective')->default(0)->comment('有效期,单位天');
            $table->boolean('is_default')->default(0)->comment('是否默认');
            $table->boolean('status')->default(true)->comment('状态')->index();
            $table->integer('order')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recharge_packages');
    }
};
