<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE fastgpt_knowledge_sets MODIFY COLUMN type ENUM('empty', 'text', 'link', 'file', 'note', 'directory')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
