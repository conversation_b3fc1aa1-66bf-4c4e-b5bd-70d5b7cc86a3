<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plugin_vidu_templates', function (Blueprint $table) {
            $table->json('instructions')
                ->nullable()
                ->comment('模板说明')
                ->after('input_instruction');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plugin_vidu_templates', function (Blueprint $table) {
            $table->dropColumn('instructions');
        });
    }
};
