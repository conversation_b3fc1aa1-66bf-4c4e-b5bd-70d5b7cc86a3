<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_assistant_logs', function (Blueprint $table) {
            $table->boolean('is_push')
                ->default(false)
                ->comment('是否推送给大模型');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_assistant_logs', function (Blueprint $table) {
            //
        });
    }
};
