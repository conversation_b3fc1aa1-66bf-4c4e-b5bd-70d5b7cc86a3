<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('draw_image_sizes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedInteger('width')->default(1024);
            $table->unsignedInteger('height')->default(1024);
            $table->string('bigmodel')->nullable();
            $table->boolean('status')->default(true);
            $table->boolean('is_default')->default(true);
            $table->integer('order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('draw_image_sizes');
    }
};
