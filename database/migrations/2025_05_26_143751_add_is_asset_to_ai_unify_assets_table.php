<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ai_unify_assets', function (Blueprint $table) {
            $table->boolean('is_asset')
                ->default(false)
                ->comment('是否来自助理');
            $table->unsignedBigInteger('asset_log_id')
                ->nullable()
                ->comment('已填充到记录');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ai_unify_assets', function (Blueprint $table) {
            $table->dropColumn('is_asset');
            $table->dropColumn('asset_log_id');
        });
    }
};
