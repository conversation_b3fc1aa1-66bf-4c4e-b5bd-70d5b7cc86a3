<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plugin_jm_check_resources', function (Blueprint $table) {
            $table->id();
            $table->string('no');
            $table->string('type');
            $table->string('fileurl');
            $table->json('resource');
            $table->boolean('status')->default(0);
            $table->string('message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plugin_jm_check_resources');
    }
};
