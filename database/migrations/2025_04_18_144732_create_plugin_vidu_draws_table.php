<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('plugin_vidu_draws', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->string('no')->comment('订单号');
            $table->string('task_id')->nullable()->comment('任务ID');
            $table->string('template')->comment('模板参数');
            $table->json('inputs')->nullable()->comment('输入参数');
            $table->json('res')->nullable()->comment('响应结果');
            $table->tinyInteger('status')->default(0)->comment('状态');
            $table->string('error_message')->nullable()->comment('错误信息');
            $table->integer('score')->default(0)->comment('消耗积分');
            $table->string('cover')->nullable()->comment('图片地址');
            $table->string('video')->nullable()->comment('视频地址');
            $table->string('hd_video')->nullable()->comment('视频地址');
            $table->timestamps();
            $table->softDeletes();

            $table->index('no');
            $table->index('template');
            $table->index('task_id');
            $table->index('user_id');
            $table->index('status');
        });
    }

    public function down()
    {
        Schema::dropIfExists('plugin_vidu_draws');
    }
};