<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audio_volcengine_roles', function (Blueprint $table) {
            $table->id();
            $table->string('role_name', 128)->comment('角色名称');
            $table->string('logo', 128)->comment('logo');
            $table->string('description', 256)->comment('描述');
            $table->tinyInteger('status')->comment('角色状态,0:禁用,1:启用');
            $table->integer('user_id')->comment('用户ID')->default(0)->index();
            $table->string('voice_id', 64)->comment('音色');
            $table->float('pitch_rate', 3,1)->comment('语调');
            $table->float('speech_rate', 3,1)->comment('语速');
            $table->text('prompt')->comment('PROMPT');
            $table->text('prologue')->comment('开场白');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audio_volcengine_roles');
    }
};
