<?php

use App\Enums\Bailian\MemberStatusEnum;
use App\Models\BailianKnowledgeMember;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_knowledge_members', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->unsignedBigInteger('knowledge_id')->index();
            $table->string('position')->default(BailianKnowledgeMember::POSITION_OWNER);
            $table->enum('status', MemberStatusEnum::values())->comment('状态');
            $table->string('reason')->nullable()->comment('拒绝原因');
            $table->timestamps();
            $table->comment('百炼知识库成员');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_knowledge_members');
    }
};
