<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_assistants', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')
                ->comment('用户ID')
                ->index();
            $table->string('log_id')
                ->nullable()
                ->comment('三方记录ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_assistants');
    }
};
