<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_knowledge_categories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('knowledge_id')->comment('知识库ID');
            $table->unsignedBigInteger('category_id')->comment('分类ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_knowledge_categories');
    }
};
