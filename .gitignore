/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/vendor
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode
modules_statuses.json
modules_versions.json
composer.lock
/app/Admin/Controllers/TestController.php
/app/Admin/Controllers/LinYiManController.php
/app/Admin/Controllers/SkyxuController.php
/app/Admin/LinYiManController.php
/_ide_helper.php
.phpstorm.meta.php
/config/debugbar.php
DOC.md
/app/Notifications/TestNotification.php
.DS_Store
/docker-compose.yml
/packages/
/storage/logs

**/caddy
frankenphp
frankenphp-worker.php
