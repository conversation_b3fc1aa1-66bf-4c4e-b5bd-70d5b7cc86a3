body {
    background-color: #111;
    color: #fff;
    font-family: 'Arial', sans-serif;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 20px;
}

.container {
    max-width: 800px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.record-container {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
}

.vinyl-record {
    width: 280px;
    height: 280px;
    border-radius: 50%;
    background: radial-gradient(circle, #333 0%, #000 70%);
    position: relative;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.5s ease;
}

.vinyl-record:before {
    content: '';
    position: absolute;
    width: 100px;
    height: 100px;
    background: #000;
    border-radius: 50%;
    z-index: 2;
    border: 1px solid #444;
}

.record-grooves {
    position: absolute;
    width: 260px;
    height: 260px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.record-grooves:nth-child(2) {
    width: 240px;
    height: 240px;
}

.record-grooves:nth-child(3) {
    width: 220px;
    height: 220px;
}

.record-grooves:nth-child(4) {
    width: 200px;
    height: 200px;
}

.record-grooves:nth-child(5) {
    width: 180px;
    height: 180px;
}

.record-grooves:nth-child(6) {
    width: 160px;
    height: 160px;
}

.record-grooves:nth-child(7) {
    width: 140px;
    height: 140px;
}

.record-grooves:nth-child(8) {
    width: 120px;
    height: 120px;
}

.record-cover {
    position: absolute;
    width: 220px;
    height: 220px;
    border-radius: 50%;
    z-index: 1;
    background-size: cover;
    background-position: center;
    overflow: hidden;
}

.play-button {
    position: absolute;
    z-index: 3;
    width: 60px;
    height: 60px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border: 2px solid #fff;
    transition: all 0.2s ease;
}

.play-button:hover {
    transform: scale(1.1);
    background-color: rgba(0, 0, 0, 0.8);
}

.play-icon, .pause-icon {
    color: #fff;
    font-size: 24px;
}

.pause-icon {
    display: none;
}

.music-info {
    height: 50vh;
    margin-top: 20px;
    text-align: center;
    display: flex;
    flex-direction: column;
}

.song-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.artist-info {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.artist-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    background-size: cover;
    background-position: center;
}

.artist-name {
    font-size: 18px;
}

.date {
    font-size: 14px;
    color: #999;
    margin-bottom: 10px;
}

.description {
    font-size: 14px;
    color: #bbb;
    margin-bottom: 20px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.lyrics-container {
    overflow-y: auto;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: left;
    flex: 1;
}

.lyrics {
    white-space: pre-line;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .vinyl-record {
        width: 220px;
        height: 220px;
    }

    .record-cover {
        width: 170px;
        height: 170px;
    }

    .record-grooves:nth-child(2) {
        width: 200px;
        height: 200px;
    }

    .record-grooves:nth-child(3) {
        width: 180px;
        height: 180px;
    }

    .record-grooves:nth-child(4) {
        width: 160px;
        height: 160px;
    }

    .record-grooves:nth-child(5) {
        width: 140px;
        height: 140px;
    }

    .record-grooves:nth-child(6) {
        width: 120px;
        height: 120px;
    }

    .record-grooves:nth-child(7) {
        display: none;
    }

    .record-grooves:nth-child(8) {
        display: none;
    }

    .play-button {
        width: 50px;
        height: 50px;
    }

}

.rotating {
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}