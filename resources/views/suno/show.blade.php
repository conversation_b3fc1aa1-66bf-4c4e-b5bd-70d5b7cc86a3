<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分享:{{$audio->name}}</title>
    <link href="{{asset('css/bootstrap.min.css')}}" rel="stylesheet">
    <link rel="stylesheet" href="{{asset('css/audio.css')}}?ver=1">
</head>
<body>
<div class="container">
    <div class="record-container">
        <div class="vinyl-record" id="record">
            <div class="record-grooves"></div>
            <div class="record-grooves"></div>
            <div class="record-grooves"></div>
            <div class="record-grooves"></div>
            <div class="record-grooves"></div>
            <div class="record-grooves"></div>
            <div class="record-grooves"></div>
            <div class="record-grooves"></div>
            <div class="record-cover" id="albumCover"></div>
            <div class="play-button" id="playButton">
                <i class="play-icon">▶</i>
                <i class="pause-icon">❚❚</i>
            </div>
        </div>
    </div>

    <div class="music-info">
        <button class="footer-button" onclick="location.href='wateai://app/audio/{{$audio->id}}'">打开看看</button>

        <div class="song-title" id="songTitle"></div>
        <div class="artist-info">
            <div class="artist-avatar" id="artistAvatar"></div>
            <div class="artist-name" id="artistName"></div>
        </div>
        <div class="date" id="releaseDate"></div>
        <div class="description" id="songDescription">
        </div>
        <div class="lyrics-container">
            <div class="lyrics" id="lyrics">

            </div>
        </div>
    </div>
</div>

<!-- 音频元素 -->
<audio id="audioPlayer" style="display: none;">
    <source src="" type="audio/mpeg">
    您的浏览器不支持音频播放
</audio>

<!-- Bootstrap JS 和依赖 -->
<script src="{{asset('js/bootstrap.bundle.min.js')}}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 可配置的音乐信息
        const musicData = {
            title: "{{$audio->name}}",
            artist: "{{$audio->user->info->nickname}}",
            artistAvatar: "{{$audio->user->info->avatar_url}}", // 替换为真实头像URL
            albumCover: "{{$audio->coverUrl}}", // 替换为真实封面URL
            audioFile: "{{$audio->audioUrl}}", // 替换为真实音频URL
            releaseDate: "{{$audio->created_at->toDateString()}}",
            description: "{{$audio->tags}} {{$audio->prompt}}",
            lyrics: `{!! $audio->lyric !!}`
        };

        // 获取DOM元素
        const audioPlayer = document.getElementById('audioPlayer');
        const playButton = document.getElementById('playButton');
        const record = document.getElementById('record');
        const playIcon = document.querySelector('.play-icon');
        const pauseIcon = document.querySelector('.pause-icon');

        // 设置音乐信息
        document.getElementById('songTitle').textContent = musicData.title;
        document.getElementById('artistName').textContent = musicData.artist;
        document.getElementById('releaseDate').textContent = musicData.releaseDate;
        document.getElementById('songDescription').textContent = musicData.description;
        document.getElementById('lyrics').textContent = musicData.lyrics;

        // 设置封面和头像
        document.getElementById('albumCover').style.backgroundImage = `url(${musicData.albumCover})`;
        document.getElementById('artistAvatar').style.backgroundImage = `url(${musicData.artistAvatar})`;

        // 设置音频源
        if (musicData.audioFile) {
            audioPlayer.src = musicData.audioFile;
        }

        // 播放/暂停功能
        playButton.addEventListener('click', function () {
            if (audioPlayer.paused) {
                audioPlayer.play();
                playIcon.style.display = 'none';
                pauseIcon.style.display = 'block';
                record.classList.add('rotating');
            } else {
                audioPlayer.pause();
                playIcon.style.display = 'block';
                pauseIcon.style.display = 'none';
                record.classList.remove('rotating');
            }
        });

        // 当音频结束时
        audioPlayer.addEventListener('ended', function () {
            playIcon.style.display = 'block';
            pauseIcon.style.display = 'none';
            record.classList.remove('rotating');
        });
    });
</script>
</body>
</html>
