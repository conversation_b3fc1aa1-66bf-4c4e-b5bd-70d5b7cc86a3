<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>微信授权登录失败</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #fff;
        }

        .container {
            text-align: center;
        }

        .icon {
            width: 60px;
            height: 60px;
            background-color: #ff1c1c;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 10px;
        }

        .icon::after {
            color: #fff;
            font-size: 40px;
            content: "×";
        }

        h1 {
            color: #000;
            font-size: 24px;
            margin-bottom: 50px;
        }

        .button-container {
            margin-top: 250px;
        }

        button {
            padding: 15px 50px;
            background-color: #f2f2f2;
            border: none;
            border-radius: 5px;
            color: #00b300;
            font-size: 18px;
            cursor: pointer;
        }
    </style>
</head>

<body>
<div class="container">
    <div class="icon"></div>
    <h1>授权登录失败</h1>
    <div class="button-container">
        <div>
            <button id="closeBtn">我知道了</button>
        </div>
    </div>

</div>

<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
<script>
    wx.config({!! json_encode($config, JSON_UNESCAPED_UNICODE) !!});
    wx.ready(function () {
        // 微信授权登录成功后，调用微信的JS接口
        document.getElementById('closeBtn').addEventListener('click', function () {
            wx.closeWindow();
        });
    });
</script>
</body>

</html>
