<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>AtoB Test</title>
    <link rel="stylesheet" href="https://lib.sinaapp.com/js/bootstrap/4.3.1/css/bootstrap.min.css">
</head>
<body>
<style>
    .form-group label {
        color: #999999
    }

    .list-group-item {
        padding: .35rem 1.25rem;
        font-size: 14px;
        cursor: pointer
    }

    .list-group-item span {
        display: none
    }

    .list-group-item:hover span {
        display: block
    }

</style>
<div class="container-xl pb-4" style="padding:30px">
    <h1>AtoB Test<span style="color:#999;font-size:14px;margin-left:10px">1.0.0</span></h1>
    <div class="row">
        <div class="col-2">
            <div class="apilist">
                <img src="loading.gif">
            </div>
        </div>
        <div class="col-5">
            <form id="frm">
                <div class="form-row">
                    <div class="col">
                        <div class="form-group"><label for="platform">Platform(手机系统平台)</label><input type="text"
                                                                                                           name="platform"
                                                                                                           class="form-control"
                                                                                                           id="platform"
                                                                                                           value="Android">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group"><label for="device">Device(手机型号)</label><input type="text"
                                                                                                   class="form-control"
                                                                                                   name="device"
                                                                                                   id="device"
                                                                                                   value="HUAWEI META P60 PRO">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group"><label for="os">OS(软件版本)</label><input type="text"
                                                                                           class="form-control" id="os"
                                                                                           name="os" value="9.0"></div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="col">
                        <div class="form-group"><label for="imnumber">ImNumber(设备码)</label><input type="text"
                                                                                                     class="form-control"
                                                                                                     name="imnumber"
                                                                                                     id="imnumber"
                                                                                                     VALUE="2ECFC8FFC90B469AAA7027776527537A">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group"><label for="appversion">AppVersion</label><input type="text"
                                                                                                 class="form-control"
                                                                                                 name="appversion"
                                                                                                 id="appversion"
                                                                                                 value="1.0"></div>
                    </div>
                    <div class="col">
                        <div class="form-group"><label for="appversion">Debug</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="flexCheckChecked" checked>
                                <label class="form-check-label" for="flexCheckChecked">明文</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="col">
                        <div class="form-group"><label for="token">Token</label><input type="text" class="form-control"
                                                                                       name="token" id="token" value="">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group"><label for="cmd">CMD(执行路径)</label><input type="text"
                                                                                             class="form-control"
                                                                                             name="cmd" id="cmd"
                                                                                             value=""></div>
                    </div>
                </div>
                <div class="form-group mt-3">
                    <label for="bodys">Upload parameters</label>
                    <textarea class="form-control" id="uploadparam" rows="5" name="uploadparam" placeholder='{
    "newsid":1,
    "category_id":3,
    "key":"china"
}'>
{
    "newsid":1,
    "category_id":3,
    "key":"china"
}
                    </textarea>
                </div>
                <button type="submit" class="btn btn-primary btn-mini" id="submitBtn">Generate Request</button>
                <button type="button" class="btn btn-success" id="requestBtn">Generate Request</button>
                <div class="form-group mt-3">
                    <label for="bodys">Request Body</label>
                    <textarea class="form-control" id="bodys" rows="9" name="bodys"></textarea>
                </div>

            </form>
        </div>
        <div class="col-5">
            <button type="submit" class="btn btn-info  btn-sm" id="saveBtn">Save this request from</button>
            <button type="button" class="btn btn-success  btn-sm" id="saveToken">Save this Token from</button>
            <div class="form-group mt-3" id="responseHtml">
                <label for="bodys">Response Body</label>
                <textarea class="form-control" id="respanse" rows="28" name="respanse"></textarea>
            </div>
        </div>
    </div>
</div>
<script src="https://lib.sinaapp.com/js/jquery/2.0.3/jquery-2.0.3.min.js"></script>
<script>
    var formatJson = function (json, options) {
        var reg = null,
            formatted = '',
            pad = 0,
            PADDING = '    '; // one can also use '\t' or a different number of spaces
        // optional settings
        options = options || {};
        // remove newline where '{' or '[' follows ':'
        options.newlineAfterColonIfBeforeBraceOrBracket = (options.newlineAfterColonIfBeforeBraceOrBracket === true) ? true : false;
        // use a space after a colon
        options.spaceAfterColon = (options.spaceAfterColon === false) ? false : true;

        // begin formatting...

        // make sure we start with the JSON as a string
        if (typeof json !== 'string') {
            json = JSON.stringify(json);
        }
        // parse and stringify in order to remove extra whitespace
        json = JSON.parse(json);
        json = JSON.stringify(json);

        // add newline before and after curly braces
        reg = /([\{\}])/g;
        json = json.replace(reg, '\r\n$1\r\n');

        // add newline before and after square brackets
        reg = /([\[\]])/g;
        json = json.replace(reg, '\r\n$1\r\n');

        // add newline after comma
        reg = /(\,)/g;
        json = json.replace(reg, '$1\r\n');

        // remove multiple newlines
        reg = /(\r\n\r\n)/g;
        json = json.replace(reg, '\r\n');

        // remove newlines before commas
        reg = /\r\n\,/g;
        json = json.replace(reg, ',');

        // optional formatting...
        if (!options.newlineAfterColonIfBeforeBraceOrBracket) {
            reg = /\:\r\n\{/g;
            json = json.replace(reg, ':{');
            reg = /\:\r\n\[/g;
            json = json.replace(reg, ':[');
        }
        if (options.spaceAfterColon) {
            reg = /\:/g;
            json = json.replace(reg, ': ');
        }

        $.each(json.split('\r\n'), function (index, node) {
            var i = 0,
                indent = 0,
                padding = '';

            if (node.match(/\{$/) || node.match(/\[$/)) {
                indent = 1;
            } else if (node.match(/\}/) || node.match(/\]/)) {
                if (pad !== 0) {
                    pad -= 1;
                }
            } else {
                indent = 0;
            }

            for (i = 0; i < pad; i++) {
                padding += PADDING;
            }

            formatted += padding + node + '\r\n';
            pad += indent;
        });

        return formatted;
    };
    $(function () {
        let json = "";
        let token = "";
        $("#frm").submit(function () {
            let Q = $("#frm").serializeArray();
            token = Q[5].value
            console.log(token)
            $.ajaxSetup({
                headers: {'authorization': 'Bearer ' + token}
            });
            let obj = $("#submitBtn");
            let btnText = obj.text();
            obj.text(btnText + '.....');
            obj.attr("disabled", true);
            $.post("/api/wapi/generate", Q, function (e) {
                e = JSON.parse(e);
                console.log(e);
                if (e.code == 0) {
                    $("#bodys").val((formatJson(JSON.stringify(e.data))).replace(/(^\s*)|(\s*$)/g, ""));
                    json = e.data;
                } else {
                    alert(e.message);
                }
                obj.text(btnText);
                obj.attr("disabled", false);
            })
            return false;
        })
        $("#saveBtn").click(function () {
            $("#saveModal").modal("show");
        })

        $("#saveModal #ModalSave").click(function () {
            if ($("#apiName").val() == "") {
                alert("The api name is required");
                return false;
            }
            let Q = $("#frm").serializeArray();
            Q.push({
                "name": "apiname",
                "value": $("#apiName").val()
            });
            console.log(Q);
            let obj = $(this);
            $.post("/api/wapi/save_generate", Q, function (e) {
                getlist();
                $("#saveModal").modal("hide");
            });
            console.log(Q);
        });

        function getlist() {
            $(".apilist").html("<img src=\"loging.gif\">");
            $.post("/api/wapi/get_generate", function (e) {
                e = JSON.parse(e);
                if (e.data.length < 1) {
                    $(".apilist").html("Not API list!");
                    return false;
                }
                let html = "<ul class=\"list-group\">";
                $.each(e.data, function (i, item) {
                    html += "<li class=\"list-group-item d-flex justify-content-between align-items-center\"><a href='javascript:;' data-id='" + item.id + "'>" + item.name + "</a><span id='delete' data-id='" + item.id + "' class=\"badge btn-light badge-pill\">x</span></li>";
                })
                html += "</ul>";
                $(".apilist").html(html);
            });
        }

        $("#saveToken").click(function () {
            var token = $("#token").val();
            localStorage.setItem("token", token);
        })
        $(document).on("click", ".list-group-item #delete", function (e) {
            e.stopPropagation();
            let that = $(this);
            if (confirm("Are you sure you want to delete this API? It cannot be repaired after deletion")) {
                let id = that.attr("data-id");
                $.post("/api/wapi/delete_generate", {id: id}, function (e) {
                    getlist();
                })
            }
        })
        $(document).on("click", ".list-group-item", function () {
            let id = $(this).find("a").attr("data-id");
            $.post("/api/wapi/get_generate_detail", {id: id}, function (e) {
                e= JSON.parse(e);
                console.log(e);
                if (e.code == 0) {
                    var token = localStorage.getItem("token");

                    $("#platform").val(e.data.platform);
                    $("#device").val(e.data.device);
                    $("#os").val(e.data.os);
                    $("#imnumber").val(e.data.imnumber);
                    $("#appversion").val(e.data.appversion);
                    $("#token").val(token || e.data.token);
                    $("#cmd").val(e.data.cmd);
                    $("#uploadparam").val(e.data.uploadparam);

                }
                console.log()
            });
        })
        getlist();
        $("#requestBtn").click(function (e) {
            let url = "/api/main";
            if ($('#flexCheckChecked').is(':checked')) {
                url = url + "?debug=yes"
            }

            $.post(url, json, function (e) {
                $("#respanse").val((formatJson(JSON.stringify(e))).replace(/(^\s*)|(\s*$)/g, ""));
            }).setRequestHeader('authorization', 'Bearer ' + token)
        })
    })
</script>
<script src="https://lib.sinaapp.com/js/bootstrap/4.3.1/js/bootstrap.min.js"></script>
<div class="modal" id="saveModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Please enter save name</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="apiName">Give the API a name</label>
                    <input type="email" class="form-control" id="apiName" aria-describedby="emailHelp">
                    <small id="emailHelp" class="form-text text-muted">The API can only be saved after it is named. It
                        will be displayed on the left after saving.</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="ModalSave">Save ...</button>
            </div>
        </div>
    </div>
</div>
</body>
</html>
