<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watestar AI</title>
    <link rel="stylesheet" href="{{asset('css/video.css')}}?ver={{time()}}">
</head>
<body>
<div id="mark">
    <div class="mark"></div>
    <div class="mark_img">
        <img src="/img/5736cf4941d3f_610.png">
    </div>
    <div class="mark_text">
        请使用浏览器打开
    </div>
</div>

<div class="container">
    <video
            class="video"
            src="{{$publish->assetable->videoUrlAttr}}"
            poster="{{$publish->assetable->coverUrlAttr}}"
            loop
            playsinline
            preload="auto"
            webkit-playsinline="true"
            x5-video-player-type="h5"
            x5-video-player-fullscreen="portraint"
    >您的浏览器不支持 HTML5 视频。
    </video>
    <div class="video-content" onclick="onPlay()">
        <img class="video-icon" src="/img/video-play-icon.png"/>
    </div>
    <div class="video-footer">

        <div class="video-user">
            <h4>{{$publish->user->info->nickname}}</h4>
            <p>{{$publish->description}}</p>
            <p>{{$publish->created_at->toDateTimeString()}}</p>
        </div>
        <div class="video-action">
            <img class="author-avatar" src="{{$publish->user->info->avatar_url}}" alt="作者头像">
            <div class="action-item">
                <div class="action-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M7 10v12"></path>
                        <path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"></path>
                    </svg>
                </div>
                <span class="action-count">{{$publish->likeable_count}}</span>
            </div>
            <div class="action-item">
                <div class="action-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                </div>
                <span class="action-count">{{$publish->commentable_count}}</span>
            </div>
            <div class="action-item">
                <div class="action-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                    </svg>
                </div>
                <span class="action-count">{{$publish->favoriteable_count}}</span>
            </div>
        </div>
    </div>
</div>

<div class="footer">
    <div class="footer-title">
        <img class="footer-logo" src="/img/sharelogo.png"/>
        瓦特AI
    </div>
    <wx-open-launch-app id="launch-btn" appid="wxf3b29d7e2e5ab03a" extinfo="app/asset/{{$publish->id}}">
        <script type="text/wxtag-template">
            <style>
                .btn {
                    background: linear-gradient(40deg, #4d51e8, #1d21b7);
                    border: none;
                    color: white;
                    line-height: 30px;
                    padding: 0 15px;
                    border-radius: 15px;
                    font-size: 10px;
                }
            </style>
            <button class="btn">打开看看</button>
        </script>
    </wx-open-launch-app>
    <button class="footer-button" id="openApp" onclick="location.href='wateai://app/asset/{{$publish->id}}'">打开APP
    </button>
</div>
<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
<script type="text/javascript">
    function onPlay() {
        let video = document.getElementsByClassName('video')[0]
        let playIcon = document.getElementsByClassName('video-icon')[0]
        if (video.paused) {
            video.play()
            playIcon.style.display = 'none'
        } else {
            video.pause()
            playIcon.style.display = 'block'
        }
    }

    wx.config({!! json_encode($config, JSON_UNESCAPED_UNICODE) !!});
    wx.ready(function () {
        var btn = document.getElementById('launch-btn');
        btn.addEventListener('ready', function (e) {
            document.getElementById('openApp').style.display = 'none';
        });
        btn.addEventListener('launch', function (e) {
        });
        btn.addEventListener('error', function (e) {
            document.getElementById('mark').style.display = 'block';
        });
    });

</script>
</body>
</html>
