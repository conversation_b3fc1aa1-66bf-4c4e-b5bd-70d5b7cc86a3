{{-- 自定义样式 --}}
<style>
    /* 基础样式 */
    .fb-section {
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.05);
        margin-bottom: 22px;
        padding: 20px 24px;
        border: 1px solid #eef0f5;
        transition: all 0.3s ease;
    }

    .fb-section:hover {
        box-shadow: 0 5px 15px rgba(68, 96, 247, 0.08);
        transform: translateY(-2px);
    }

    .fb-title {
        font-size: 16px;
        font-weight: 600;
        color: #4460f7;
        margin-bottom: 15px;
        letter-spacing: 1px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eef0f5;
        padding-bottom: 10px;
    }

    .fb-title i {
        margin-right: 8px;
        font-size: 18px;
    }

    .fb-meta {
        color: #888;
        font-size: 13px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .fb-meta i {
        margin-right: 5px;
        font-size: 14px;
    }

    .fb-content {
        font-size: 15px;
        color: #333;
        margin-bottom: 15px;
        line-height: 1.8;
        word-break: break-all;
        padding: 12px 15px;
        background: #f9fafc;
        border-radius: 6px;
        border-left: 3px solid #4460f7;
    }

    .fb-empty {
        text-align: center;
        color: #bbb;
        font-size: 14px;
        padding: 20px 0;
        background: #f9fafc;
        border-radius: 6px;
        margin: 20px 0;
    }

    .fb-empty i {
        font-size: 22px;
        margin-right: 5px;
        vertical-align: middle;
    }

    /* 图片展示样式 */
    .fb-pictures {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }

    .fb-picture-item {
        width: 120px;
        height: 120px;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        cursor: pointer;
    }

    .fb-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
    }

    .fb-picture-item:hover .fb-image {
        transform: scale(1.05);
    }

    .image-preview-modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
    }

    .image-preview-content {
        position: relative;
        margin: auto;
        padding: 0;
        width: 80%;
        max-width: 1200px;
        height: 90%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .image-preview-close {
        position: absolute;
        top: 15px;
        right: 25px;
        color: #f1f1f1;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
    }

    #image-preview-large {
        max-width: 100%;
        max-height: 90vh;
        object-fit: contain;
    }

    /* 聊天气泡样式 */
    .chat-container {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 10px 0;
    }

    .chat-message {
        display: flex;
        margin-bottom: 5px;
    }

    .chat-left {
        align-self: flex-start;
    }

    .chat-right {
        align-self: flex-end;
        flex-direction: row-reverse;
    }

    .chat-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #4460f7;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin: 0 10px;
    }

    .chat-right .chat-avatar {
        background-color: #5cb85c;
    }

    .chat-bubble {
        max-width: 90%;
        padding: 10px 15px;
        border-radius: 10px;
        background-color: #f0f2ff;
        position: relative;
    }

    .chat-left .chat-bubble {
        border-top-left-radius: 0;
    }

    .chat-right .chat-bubble {
        background-color: #e8f5e9;
        border-top-right-radius: 0;
    }

    .chat-info {
        margin-bottom: 5px;
    }

    .chat-time {
        font-size: 12px;
        color: #888;
    }

    .chat-time i {
        margin-right: 3px;
    }

    .chat-text {
        font-size: 15px;
        line-height: 1.6;
        word-break: break-all;
    }

    /* 快捷回复样式 */
    .fb-quick-replies {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;
    }

    .quick-reply-btn {
        border-radius: 20px;
        padding: 6px 15px;
        font-size: 13px;
        border-color: #4460f7;
        color: #4460f7;
        transition: all 0.2s;
        background-color: white;
    }

    .quick-reply-btn:hover {
        background-color: #4460f7;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 3px 8px rgba(68, 96, 247, 0.25);
    }

    /* 表单样式 */
    textarea.form-control {
        border-radius: 8px;
        font-size: 15px;
        min-height: 100px;
        border: 1px solid #e0e0e0;
        padding: 12px 15px;
        transition: all 0.3s;
        resize: vertical;
    }

    textarea.form-control:focus {
        border-color: #4460f7;
        box-shadow: 0 0 0 2px rgba(68, 96, 247, 0.2);
    }

    .btn-primary {
        background-color: #4460f7 !important;
        border-color: #4460f7 !important;
        box-shadow: 0 2px 6px rgba(68, 96, 247, 0.35);
        border-radius: 6px;
        padding: 8px 20px;
        font-weight: 500;
    }

    .btn-primary:hover {
        background-color: #3a51d6 !important;
        border-color: #3a51d6 !important;
        transform: translateY(-1px);
    }

    #char-counter {
        font-size: 12px;
        margin-top: 5px;
    }

    /* 响应式样式 */
    @media (max-width: 768px) {
        .fb-section {
            padding: 15px;
            margin-bottom: 15px;
        }

        .fb-title {
            font-size: 15px;
        }

        .fb-content, .chat-text {
            font-size: 14px;
            padding: 10px;
        }

        .fb-quick-replies {
            flex-direction: column;
            align-items: stretch;
        }

        .quick-reply-btn {
            margin-bottom: 5px;
        }

        .chat-bubble {
            max-width: 85%;
        }

        .fb-pictures {
            justify-content: center;
        }
    }
</style>

{{-- 反馈内容 --}}
<div class="fb-section">
    <div class="fb-title"><i class="fa fa-comment-dots"></i> 反馈内容</div>
    <div class="fb-meta"><i class="fa fa-clock"></i>
        提交时间：{{ $feedback->created_at ? $feedback->created_at->format('Y-m-d H:i:s') : '未知' }}</div>
    <div class="fb-content">{!! nl2br(e($feedback->content)) !!}</div>

    {{-- 添加图片展示区域 --}}
    @if(!empty($feedback->pictures) && is_array($feedback->pictures))
        <div class="fb-pictures">
            @foreach($feedback->picture_urls as $picture)
                <div class="fb-picture-item">
                    <img src="{{ $picture }}" alt="反馈图片" class="fb-image">
                </div>
            @endforeach
        </div>
    @endif
</div>

{{-- 历史回复 --}}
<div class="fb-section">
    <div class="fb-title"><i class="fa fa-history"></i> 历史回复记录</div>

    {{-- 如果没有历史回复 --}}
    @if($children->isEmpty())
        <div class="fb-empty"><i class="fa fa-info-circle"></i> 暂无历史回复</div>
    @else
        {{-- 聊天气泡样式的历史回复 --}}
        <div class="chat-container">
            @foreach($children as $reply)
                @php
                    $isAdmin = $reply->user_id != $feedback->user_id;
                    $position = $isAdmin ? 'left' : 'right';
                    $avatar = $isAdmin ? '<i class="fa fa-user-secret"></i>' : '<i class="fa fa-user"></i>';
                @endphp

                <div class="chat-message chat-{{ $position }}">
                    <div class="chat-avatar">{!! $avatar !!}</div>
                    <div class="chat-bubble">
                        <div class="chat-info">
                            <span class="chat-time"><i class="fa fa-clock"></i> {{ $reply->created_at ? $reply->created_at->format('Y-m-d H:i:s') : '未知时间' }}</span>
                        </div>
                        <div class="chat-text">{!! nl2br(e($reply->content)) !!}</div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
</div>

{{-- 快捷回复模板 --}}
<div class="fb-section">
    <div class="fb-title"><i class="fa fa-bolt"></i> 快捷回复模板</div>
    <div class="fb-quick-replies">
        <button type="button" class="btn btn-sm btn-outline-primary quick-reply-btn"
                data-content="感谢您的反馈，我们已收到并会尽快处理。">感谢反馈
        </button>
        <button type="button" class="btn btn-sm btn-outline-primary quick-reply-btn"
                data-content="您好，关于您反馈的问题，我们需要更多信息来帮助您解决，请提供更详细的描述。">需要更多信息
        </button>
        <button type="button" class="btn btn-sm btn-outline-primary quick-reply-btn"
                data-content="您好，您反馈的问题我们已经修复，请刷新页面或重新登录后尝试。">问题已修复
        </button>
        <button type="button" class="btn btn-sm btn-outline-primary quick-reply-btn"
                data-content="您好，非常抱歉给您带来不便，我们正在积极解决您反馈的问题，请耐心等待。">正在处理中
        </button>
    </div>
</div>
