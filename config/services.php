<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain'   => env('MAILGUN_DOMAIN'),
        'secret'   => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme'   => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key'    => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'baidu_translate' => [
        'app_id'  => env('BAIDU_TRANSLATE_APP_ID'),
        'app_key' => env('BAIDU_TRANSLATE_APP_KEY'),
        'url'     => env('BAIDU_TRANSLATE_URL', 'https://fanyi-api.baidu.com/api/trans/vip/translate'),
        'timeout' => env('BAIDU_TRANSLATE_TIMEOUT', 30),
    ]

];
