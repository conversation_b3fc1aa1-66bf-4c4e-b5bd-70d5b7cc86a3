<?php

use Nwidart\Modules\Activators\FileActivator;
use Nwidart\Modules\Providers\ConsoleServiceProvider;

return [

    /*
    |--------------------------------------------------------------------------
    | Module Namespace
    |--------------------------------------------------------------------------
    |
    | Default module namespace.
    |
    */

    'namespace' => 'Modules',

    /*
    |--------------------------------------------------------------------------
    | Module Stubs
    |--------------------------------------------------------------------------
    |
    | Default module stubs.
    |
    */

    'stubs'    => [
        'enabled'      => false,
        'path'         => storage_path('stubs'),
        'files'        => [
            'routes/web'            => 'Routes/admin.php',
            'routes/api'            => 'Routes/api.php',
            'routes/office'         => 'Routes/office.php',
            // 'views/index'     => 'Resources/views/index.blade.php',
            // 'views/master'    => 'Resources/views/layouts/master.blade.php',
            // 'scaffold/config' => 'Config/config.php',
            'composer'              => 'composer.json',
            // 'assets/js/app'   => 'Resources/assets/js/app.js',
            // 'assets/sass/app' => 'Resources/assets/sass/app.scss',
            // 'vite'            => 'vite.config.js',
            // 'package'         => 'package.json',
            'bootstrap'             => 'Bootstrap.php',
            'config'                => 'config.json',
            'event_provider'        => 'Providers/EventServiceProvider.php',
            'controllers/dashboard' => 'Http/Controllers/Admin/DashboardController.php',
            'controllers/api'       => 'Http/Controllers/Api/IndexController.php',
            'controllers/office'    => 'Http/Controllers/Office/IndexController.php',
        ],
        'replacements' => [
            'routes/web'            => ['LOWER_NAME', 'STUDLY_NAME', 'MODULE_NAMESPACE', 'CONTROLLER_NAMESPACE'],
            'routes/api'            => ['LOWER_NAME', 'STUDLY_NAME'],
            'vite'                  => ['LOWER_NAME'],
            'json'                  => ['LOWER_NAME', 'STUDLY_NAME', 'MODULE_NAMESPACE', 'PROVIDER_NAMESPACE'],
            'views/index'           => ['LOWER_NAME'],
            'views/master'          => ['LOWER_NAME', 'STUDLY_NAME'],
            'scaffold/config'       => ['STUDLY_NAME'],
            'composer'              => [
                'LOWER_NAME',
                'STUDLY_NAME',
                'VENDOR',
                'AUTHOR_NAME',
                'AUTHOR_EMAIL',
                'MODULE_NAMESPACE',
                'PROVIDER_NAMESPACE',
            ],
            'bootstrap'             => ['MODULE_NAMESPACE', 'STUDLY_NAME', 'LOWER_NAME'],
            'event_provider'        => ['MODULE_NAMESPACE', 'STUDLY_NAME'],
            'controllers/dashboard' => ['MODULE_NAMESPACE', 'STUDLY_NAME'],
            'controllers/api'       => ['MODULE_NAMESPACE', 'STUDLY_NAME'],
            'controllers/office'    => ['MODULE_NAMESPACE', 'STUDLY_NAME'],
        ],
        'gitkeep'      => false,
    ],
    'paths'    => [
        /*
        |--------------------------------------------------------------------------
        | Modules path
        |--------------------------------------------------------------------------
        |
        | This path is used to save the generated module.
        | This path will also be added automatically to the list of scanned folders.
        |
        */

        'modules' => base_path('modules'),
        /*
        |--------------------------------------------------------------------------
        | Modules assets path
        |--------------------------------------------------------------------------
        |
        | Here you may update the modules' assets path.
        |
        */

        'assets' => public_path('modules'),
        /*
        |--------------------------------------------------------------------------
        | The migrations' path
        |--------------------------------------------------------------------------
        |
        | Where you run the 'module:publish-migration' command, where do you publish the
        | the migration files?
        |
        */

        'migration' => base_path('database/migrations'),
        /*
        |--------------------------------------------------------------------------
        | Generator path
        |--------------------------------------------------------------------------
        | Customise the paths where the folders will be generated.
        | Setting the generate key to false will not generate that folder
        */
        'generator' => [
            'config'          => ['path' => 'config', 'generate' => true],
            'command'         => ['path' => 'App/Console', 'generate' => false],
            'channels'        => ['path' => 'App/Broadcasting', 'generate' => false],
            'migration'       => ['path' => 'Database/Migrations', 'generate' => false],
            'seeder'          => ['path' => 'Database/Seeders', 'generate' => true],
            'factory'         => ['path' => 'Database/Factories', 'generate' => false],
            'model'           => ['path' => 'App/Models', 'generate' => false],
            'observer'        => ['path' => 'App/Observers', 'generate' => false],
            'routes'          => ['path' => 'routes', 'generate' => true],
            'controller'      => ['path' => 'App/Http/Controllers', 'generate' => true],
            'filter'          => ['path' => 'App/Http/Middleware', 'generate' => false],
            'request'         => ['path' => 'App/Http/Requests', 'generate' => false],
            'provider'        => ['path' => 'App/Providers', 'generate' => true],
            'assets'          => ['path' => 'resources/assets', 'generate' => false],
            'lang'            => ['path' => 'lang', 'generate' => false],
            'views'           => ['path' => 'resources/views', 'generate' => true],
            'test'            => ['path' => 'tests/Unit', 'generate' => false],
            'test-feature'    => ['path' => 'tests/Feature', 'generate' => false],
            'repository'      => ['path' => 'App/Repositories', 'generate' => false],
            'event'           => ['path' => 'App/Events', 'generate' => false],
            'listener'        => ['path' => 'App/Listeners', 'generate' => false],
            'policies'        => ['path' => 'App/Policies', 'generate' => false],
            'rules'           => ['path' => 'App/Rules', 'generate' => false],
            'jobs'            => ['path' => 'App/Jobs', 'generate' => false],
            'emails'          => ['path' => 'App/Emails', 'generate' => false],
            'notifications'   => ['path' => 'App/Notifications', 'generate' => false],
            'resource'        => ['path' => 'App/resources', 'generate' => false],
            'component-view'  => ['path' => 'resources/views/components', 'generate' => false],
            'component-class' => ['path' => 'App/View/Components', 'generate' => false],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Package commands
    |--------------------------------------------------------------------------
    |
    | Here you can define which commands will be visible and used in your
    | application. You can add your own commands to merge section.
    |
    */
    'commands' => ConsoleServiceProvider::defaultCommands()
        ->merge([
            // New commands go here
        ])->toArray(),

    /*
    |--------------------------------------------------------------------------
    | Scan Path
    |--------------------------------------------------------------------------
    |
    | Here you define which folder will be scanned. By default will scan vendor
    | directory. This is useful if you host the package in packagist website.
    |
    */

    'scan' => [
        'enabled' => false,
        'paths'   => [
            base_path('vendor/*/*'),
        ],
    ],
    /*
    |--------------------------------------------------------------------------
    | Composer File Template
    |--------------------------------------------------------------------------
    |
    | Here is the config for the composer.json file, generated by this package
    |
    */

    'composer'   => [
        'vendor'          => 'uztech',
        'author'          => [
            'name'  => 'Jason.C',
            'email' => '<EMAIL>',
        ],
        'composer-output' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Caching
    |--------------------------------------------------------------------------
    |
    | Here is the config for setting up the caching feature.
    |
    */
    'cache'      => [
        'enabled'  => false,
        'driver'   => config('cache.default'),
        'key'      => 'laravel-modules',
        'lifetime' => 604800,
    ],
    /*
    |--------------------------------------------------------------------------
    | Choose what laravel-modules will register as custom namespaces.
    | Setting one to false will require you to register that part
    | in your own Service Provider class.
    |--------------------------------------------------------------------------
    */
    'register'   => [
        'translations' => true,
        /**
         * load files on boot or register method
         */
        'files'        => 'register',
    ],

    /*
    |--------------------------------------------------------------------------
    | Activators
    |--------------------------------------------------------------------------
    |
    | You can define new types of activators here, file, database, etc. The only
    | required parameter is 'class'.
    | The file activator will store the activation status in storage/installed_modules
    */
    'activators' => [
        'file' => [
            'class'          => FileActivator::class,
            'statuses-file'  => base_path('modules_statuses.json'),
            'cache-key'      => 'activator.installed',
            'cache-lifetime' => 604800,
        ],
    ],

    'activator' => 'file',
];
