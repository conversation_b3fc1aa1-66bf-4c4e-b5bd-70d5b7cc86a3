<?php

namespace App\Packages\Suno\Client;

use App\Models\AudioSinger;
use App\Packages\Suno\Aes;
use App\Packages\Suno\BaseClient;
use App\Packages\Suno\SunoResponse;
use App\Packages\Suno\SunoStreamResponse;

class Tools extends BaseClient
{
    public function captcha()
    {
        return $this->get('captcha');
    }

    public function createSinger(AudioSinger $singer): SunoResponse
    {
        return $this
            ->setFiles([
                [
                    'key'   => 'image_url',
                    'url'   => $singer->cover_url,
                    'scene' => 'avatar',
                ],
                [
                    'key'   => 'input',
                    'url'   => $singer->audio_url,
                    'scene' => 'origin',
                ],
            ])
            ->json('aiSinger/train', [
                'name' => $singer->name,
            ]);
    }

    public function querySinger(AudioSinger $singer): SunoResponse
    {
        return $this->get("aiSinger/{$singer->job_id}");
    }

    public function musicStyle(): SunoResponse
    {
        return $this->get("musicStyle", []);
    }

    public function audioList($classId, $page = 1, $pageSize = 10): SunoResponse
    {
        $url    = sprintf("aiSinger/public/music/%s", $classId);
        $params = [
            'page'      => $page,
            'page_size' => $pageSize,
            'styleId'   => $classId,
        ];
        return $this->get($url, $params);
    }

    public function aiSinger(): SunoResponse
    {
        return $this->get("aiSinger/list", []);
    }

    public function createLyrics(string $title): SunoStreamResponse
    {
        $data   = [
            'model'     => 0,
            'messages'  => [
                [
                    "role"    => "system",
                    "content" => $title,
                ]
            ],
            'is_lyrics' => true,
        ];
        $params = [
            'query_data' => Aes::encrypt($data),
        ];
        return $this->setUrl('https://appcloud.pcdashi.cn')
            ->setPath('api/v2')
            ->setPathQuery([
                'query_data' => Aes::encrypt([]),
            ])
            ->stream("app-community-v2", $params);
    }
}