<?php

namespace App\Packages\Suno;

class Aes
{
    public static string $key = '651cc172938d5b7799a23ac245e539a6';
    public static string $iv  = '35e5cd2d684e5c65';

    public static function encrypt(array $data): string
    {
        if (count($data) > 0) {
            $json = json_encode($data, JSON_UNESCAPED_UNICODE);
        } else {
            $json = "{}";
        }
        $string = openssl_encrypt($json, 'AES-256-CBC', self::$key,
            OPENSSL_RAW_DATA, self::$iv);
        return base64_encode($string);
    }

    public static function decrypt(string $message): array
    {
        $string = base64_decode($message);
        $json   = openssl_decrypt($string, 'AES-256-CBC', self::$key,
            OPENSSL_RAW_DATA, self::$iv);
        return json_decode($json, true);
    }
}