<?php

namespace App\Packages\AlibabaCloud\Config;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Storage;
use Ratchet\Client\Connector;
use React\EventLoop\Loop;
use React\Socket\Connector as SocketConnector;

class WssConfig
{
    protected string $appId     = '8e9b71d6';
    protected string $apiKey    = '8e04d608f016f1c7ab8d204407d3a963';
    protected string $apiSecret = 'ZGJhZmQ2NDE4NDQ4OTJmNzk0MjhiMTA1';
    protected string $wssUrl    = '';
    protected string $host      = 'cn-huabei-1.xf-yun.com';
    protected string $url       = '/v1/private/voice_clone';
    protected string $localPath = '';
    protected string $baseAudio = '';

    protected function connect(string $vcn, array $headers, array $parameter, array $payload, string $fileName)
    {
        $this->wssUrl    = $this->getAuthUrl($this->host, $this->url);
        $this->localPath = 'temp/tts/'.$fileName;
        $loop            = Loop::get();
        $socketConnector = new SocketConnector($loop, [
            'tcp' => [
                'bindto' => '0.0.0.0:0',
            ],
            'tls' => [
                'verify_peer'      => false,
                'verify_peer_name' => false,
            ],
        ]);
        $connector       = new Connector($loop, $socketConnector);

        $message = json_encode([
            "header"    => $headers,
            'parameter' => $parameter,
            'payload'   => $payload,
        ]);
        $connector($this->wssUrl, [], $headers)->then(function ($conn) use ($message) {
            $conn->send($message);
            $conn->on('error', function ($e) {
                throw new Exception($e->getMessage());
            });
            $conn->on('message', function ($msg) use ($conn) {
                $data   = json_decode($msg, true);
                $header = $data['header'];
                if ($header['code'] !== 0) {
                    throw new Exception($header['message']);
                }
                $payload = $data['payload'] ?? [];
                if (in_array($payload['audio']['status'] ?? 0, [1, 2])) {
                    $this->baseAudio                   .= base64_decode($payload['audio']['audio']);
                    $data['payload']['audio']['audio'] = '';
                    info(json_encode($data, JSON_UNESCAPED_UNICODE));
                    if ($header['status'] == 2) {
                        $conn->close();
                        Storage::put($this->localPath, $this->baseAudio);
                    }
                }
            });
            // 监听连接关闭
            $conn->on('close', function ($code = null, $reason = null) {
            });
        }, function ($e) {
            throw new Exception($e->getMessage());
        });
    }

    protected function getAuthUrl(string $host, string $path)
    {
        $time                = Carbon::now('GMT')->format('D, d M Y H:i:s \G\M\T');
        $string              = sprintf("host: %s\ndate: %s\nGET %s HTTP/1.1",
            $host,
            $time,
            $path
        );
        $stringSha           = hash_hmac('sha256', $string, $this->apiSecret, true);
        $base64              = base64_encode($stringSha);
        $authorizationOrigin = sprintf("api_key=\"{$this->apiKey}\",algorithm=\"hmac-sha256\",headers=\"host date request-line\", signature=\"{$base64}\"");
        $authorization       = base64_encode($authorizationOrigin);
        $queryParmas         = [
            'authorization' => $authorization,
            'host'          => $host,
            'date'          => $time,
        ];
        $url                 = sprintf("wss://%s%s?%s", $host, $path, http_build_query($queryParmas));

        return $url;
    }

    protected function setHost(string $host): self
    {
        $this->host = $host;
        return $this;
    }

    protected function setUrl(string $url): self
    {
        $this->url = $url;
        return $this;
    }

}