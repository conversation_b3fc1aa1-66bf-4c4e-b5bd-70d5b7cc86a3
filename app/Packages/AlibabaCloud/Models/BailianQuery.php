<?php

namespace App\Packages\AlibabaCloud\Models;

use App\Packages\AlibabaCloud\Config\BailianConfig;
use Exception;

class BailianQuery extends BailianConfig
{
    public function query(string $taskId)
    {
        $result = $this->get('tasks/'.$taskId);
        if ($result['status']) {
            return $result['data'];
        } else {
            throw new Exception($result['message']);
        }
    }
}