<?php

namespace App\Packages\Assistant\ChatTools;

use App\Packages\Assistant\ChatTools\Gen\GenImage;
use App\Packages\Assistant\ChatTools\Notification\SendEmail;
use App\Packages\Assistant\ChatTools\Question\Chat;
use App\Packages\Assistant\ChatTools\Question\Poem;
use App\Packages\Assistant\ChatTools\Weather\GetWeather;
use Illuminate\Support\Str;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;

class ChatToolsProvider
{
    const EventList = [
        GetWeather::KEY => GetWeather::class,
        GenImage::KEY   => GenImage::class,
        SendEmail::KEY  => SendEmail::class,
        Chat::KEY       => Chat::class,
        Poem::KEY       => Poem::class,
    ];

    public static function getModel(string $key)
    {
        $class = self::EventList[$key] ?? '';
        if ($class) {
            return new $class;
        } else {
            return null;
        }
    }

    public static function getToolClasses(string $type): array
    {
        $type = Str::ucfirst(trim($type));
        if (is_dir(__DIR__."\\".$type)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator(__DIR__."\\".$type, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            $classes  = [];
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $filename  = pathinfo($file->getFilename(), PATHINFO_FILENAME);
                    $classes[] = __NAMESPACE__."\\{$type}\\{$filename}";
                }
            }
            return $classes;
        }
        return [];
    }
}