<?php

namespace App\Packages\Assistant\ChatTools\Notification;

use App\Packages\Assistant\ChatTools\BaseEvent;

class SendEmail extends BaseEvent
{
    const KEY = 'send_email';
    public string   $name   = '发送邮件';
    protected array $params = [];

    public function __construct()
    {
        parent::__construct(self::KEY, $this->name);
    }

    public function getKey()
    {
        return self::KEY;
    }

    public function getPrompt(): array
    {
        return [
            'name'        => self::KEY,
            'description' => "当你想给某个人发送电子邮件时，请使用此功能。",
            'format'      => [
                'getContent' => "<tool>tool_name|getContent</tool>",
            ],
            'parameters'  => [
                'nickname' => [
                    'type'        => 'string',
                    'description' => '联系人',
                ],
                'email'    => [
                    'type'        => 'string',
                    'description' => '电子邮件，可选.只获取真正的邮件地址',
                ],
                'content'  => [
                    'type'        => 'string|getContent',
                    'description' => '邮件内容描述或依赖工具的内容,使用当前工具提示的格式{getContent}',
                ],
            ],
        ];
    }

    public function doFunction(array $params): mixed
    {
        $this->sendStepRemark('正在发送邮件，请稍等...');
        $this->sendStepData($params);
        $this->sendStepRemark('功能开发中……');
        $this->sendDone();
        return true;
    }
}