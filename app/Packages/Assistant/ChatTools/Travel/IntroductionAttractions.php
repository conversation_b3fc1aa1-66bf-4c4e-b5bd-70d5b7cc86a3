<?php

namespace App\Packages\Assistant\ChatTools\Travel;

use App\Packages\Assistant\ChatTools\BaseEvent;

class IntroductionAttractions extends BaseEvent
{
    const KEY = 'introduction_attractions';

    public string   $name   = '景点介绍';
    protected array $params = [];

    public function __construct()
    {
        parent::__construct(self::KEY, $this->name);
    }

    public function getParams()
    {
        return [
            'type'     => 'function',
            'function' => $this->getPrompt(),
        ];
    }

    public function getPrompt(): array
    {
        return [
            'name'        => self::KEY,
            'description' => <<<EOT
                                当你想知道某一个地点或景点的介绍时，请使用此功能。
                                EOT,
            'parameters'  => [
                'type'       => 'object',
                'properties' => [
                    'attractions' => [
                        'type'        => 'string',
                        'description' => '景点或地点名称',
                    ],
                ],
                'required'   => ["attractions"]
            ]
        ];
    }

    public function doFunction(string $json): mixed
    {
        return true;
    }
}