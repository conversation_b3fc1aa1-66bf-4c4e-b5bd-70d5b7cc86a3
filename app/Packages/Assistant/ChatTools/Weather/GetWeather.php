<?php

namespace App\Packages\Assistant\ChatTools\Weather;

use App\Packages\Assistant\ChatTools\BaseEvent;
use App\Packages\Assistant\Tools\Gaode;
use Exception;
use Illuminate\Support\Str;

class GetWeather extends BaseEvent
{
    const KEY = 'get_weather';
    public string   $name   = '获取天气';
    protected array $params = [];

    public function __construct()
    {
        parent::__construct(self::KEY, $this->name);
    }

    public function getKey()
    {
        return self::KEY;
    }

    public function getPrompt(): array
    {
        return [
            'name'        => self::KEY,
            'description' => "当你想了解天气时，请使用此功能。如果没有给予特定的城市名称则city为空",
            'parameters'  => [
                'city' => [
                    'type'        => 'string',
                    'description' => '需要查询的城市名称',
                ],
            ],
        ];
    }

    public function genPrompt(array $toolResults): string
    {
        $data   = $toolResults[0] ?? [];
        $city   = $data['city'] ?? '';
        $string = sprintf('"%s ', $city);
        $cast   = $data['casts'][0];
        $string .= sprintf('%s-%s摄氏度', $cast['daytemp'], $cast['nighttemp']);
        if ($cast['dayweather'] != $cast['nightweather']) {
            $string .= sprintf('%s转%s', $cast['dayweather'], $cast['nightweather']);
        } else {
            $string .= sprintf('%s', $cast['dayweather']);
        }
        $string .= sprintf('%s风%s级', $cast['daywind'], $cast['daypower']);
        $string .= sprintf('%s 星期%s"', $cast['date'],
            match ($cast['week']) {
                '1' => '一',
                '2' => '二',
                '3' => '三',
                '4' => '四',
                '5' => '五',
                '6' => '六',
                '7' => '日',
            });
        return $string;
    }

    public function doFunction(array $params): mixed
    {
        $this->sendStepRemark($this->step, "开始执行任务\n");

        $city   = $params['city'] ?? '';
        $gaode  = new Gaode();
        $adCode = '';
        $string = '';
        if ($city && ! Str::contains($city, ['今天', '明天', '当前位置'])) {
            try {
                $adCode = $gaode->getAdCodeCity($city);
            } catch (Exception $e) {
                $adCode = 'error';
                $string = $e->getMessage()."\n";
            }
        }
        if (blank($adCode) && $this->log->lat && $this->log->lng) {
            try {
                $adCode = $gaode->getAdCodeFormLL($this->log->lat, $this->log->lng);
            } catch (Exception $e) {
                $adCode = 'error';
                $string = $e->getMessage()."\n";
            }
        }
        if (blank($adCode)) {
            $string = "获取城市失败可以尝试开启定位全选或告诉我您要查询的城市。\n";
        } elseif ($adCode != 'error') {
            $data = $gaode->getWeather($adCode);
            $this->sendStepData($this->step, $data);
            $this->log->manyTools()->updateOrCreate([
                'key'  => self::KEY,
                'name' => $this->name,
            ], [
                'step'    => $this->step,
                'params'  => $params,
                'results' => $data
            ]);
            $string = '';
        }
        if (! blank($string)) {
            $this->error($string, $this->step);
        }
        return true;
    }
}