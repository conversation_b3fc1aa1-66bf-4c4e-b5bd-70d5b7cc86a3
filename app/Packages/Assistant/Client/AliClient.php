<?php

namespace App\Packages\Assistant\Client;

use App\Packages\Assistant\Response\AliResponse;
use App\Packages\Assistant\Response\BaseResponse;
use GuzzleHttp\Client;

class AliClient
{
    protected Client $client;

    public function __construct(string $baseUri, string $token)
    {
        $this->client = new Client([
            'base_uri' => $baseUri,
            'verify'   => false,
        ]);
    }

    public function post(string $path, array $params = []): BaseResponse
    {
        $response = $this->client->post($path, [
            'headers' => [
                'Authorization' => 'Bearer '.$this->token,
            ],
            'json'    => $params,
        ]);
        return new AliResponse($response);
    }
}