<?php

namespace App\Packages\Assistant\Response;

use Psr\Http\Message\ResponseInterface;

class AliResponse implements BaseResponse
{
    use ResponseTrait;

    public function __construct(protected ?ResponseInterface $response = null, string $message = '', ...$params)
    {
        if (is_null($this->response)) {
            $this->success = false;
            $this->message = $message;
        } else {
            if ($this->response->getStatusCode() != 200) {
                $this->success = false;
                $this->message = $this->response->getBody()->getContents();
            } else {
                $this->success = true;
                $this->data    = json_decode($this->response->getBody()->getContents(), true);
            }
        }
    }
}