<?php

namespace App\Packages\JiMeng\Helper;

use App\Exceptions\ValidatorException;
use GuzzleHttp\Client;
use Mockery\Exception;

class Aws
{
    public string $accessToken     = '';
    public array  $videoToken      = [];
    public array  $imagexToken     = [];
    public string $other           = '';
    public string $accessKeyId     = 'AKTPMzQ1MTI4Zjk0YmRiNGQ3Zjg3NzNhOTkwM2E1NjMyMTA';
    public string $secretAccessKey = '';
    public string $awsDate         = '';
    public string $awsRegion       = 'cn-north-1';
    public string $awsService      = 'imagex';

    public function __construct()
    {
        $this->inifConfig();
        $this->awsDate = date('Ymd');
    }

    private function inifConfig()
    {
        $client   = new Client([
            'verify'   => false,
            'base_uri' => 'https://summon.bytedance.com',
        ]);
        $response = $client->get('api/v1/resource/upload/token', [
            'query'   => [
                'uploadTerminal' => 1,
            ],
            'headers' => [
                'x-signature'  => '0ebc08f5f3bdc094efa3d7f1a6e6bf3f',
                'content-type' => 'application/json',
                'cookie'       => 'ttwid=1%7CUM80ZujLX-8aQIHHINS8M0PlAwmllOUH7MQBF5z0_T4%7C1739771805%7C85cff54cf328463ee3814efbe54b541986b45611d56e4378b474d11c1307c5b6;'
            ]
        ]);
        try {
            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                if ($data['code'] == 200) {
                    $this->accessToken         = $data['data']['accessToken'] ?? '';
                    $uploadToken               = json_decode($data['data']['uploadToken'], true);
                    $this->other               = $uploadToken['other'] ?? '';
                    $this->videoToken          = isset($uploadToken['video']) ? json_decode($uploadToken['video'],
                        true) : [];
                    $this->videoToken['token'] = isset($this->videoToken['token']) ? json_decode($this->videoToken['token'],
                        true) : [];
                    $this->imagexToken         = $uploadToken['imagex_token'] ?? [];
                    $this->secretAccessKey     = $this->videoToken['token']['SecretAccessKey'] ?? '';
                } else {
                    throw new Exception($data['message']);
                }
            } else {
                throw new Exception($response->getBody()->getContents());
            }
        } catch (\Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function getAuthorization(): string
    {
        $token = '';
        $token = 'AWS4-HMAC-SHA256 Credential='.
            $this->videoToken['token']['AccessKeyId'].
            '/'.$this->awsDate.
            '/'.$this->awsRegion.
            '/'.$this->awsService.
            '/aws4_request, SignedHeaders=x-amz-date;x-amz-security-token, Signature='.$token;
        return $token;
    }

}