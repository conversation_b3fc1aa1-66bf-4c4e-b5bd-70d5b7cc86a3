<?php

namespace App\Packages\JiMeng\Helper;

use Exception;
use GuzzleHttp\Client;

class WebId
{
    protected      $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 QuarkPC/2.0.7.222';
    private string $webId     = '';

    public function __construct()
    {
        $this->webId = $this->initWebID();
    }

    public function initWebID()
    {
        $client   = new Client([
            'verify' => false,
        ]);
        $response = $client->request('POST', 'https://mcs.zijieapi.com/webid', [
            'json'    => [
                'app_id'         => 2018,
                'referer'        => '',
                'user_agent'     => $this->userAgent,
                'url'            => 'https://jimeng.jianying.com/ai-tool/video/generate',
                'user_unique_id' => ''
            ],
            'headers' => [
                'Content-Type' => 'application/json',
                'User-Agent'   => $this->userAgent
            ],
        ]);
        try {
            if ($response->getStatusCode() == 200) {
                $result = json_decode($response->getBody()->getContents(), true);
                return $result['web_id'];
            } else {
                info($response->getBody()->getContents());
            }
        } catch (Exception $exception) {
            info($exception);
            return '';
        }
    }

    public function getWebId(): string
    {
        return $this->webId;
    }
}