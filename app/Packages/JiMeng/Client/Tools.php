<?php

namespace App\Packages\JiMeng\Client;

use App\Models\PluginJmCheckResource;
use App\Packages\JiMeng\BaseClient;
use Illuminate\Support\Str;

class Tools extends BaseClient
{
    protected string $msToken = 'xi08wETk0OhmZ8S5Him6WvjkG3pKBgSXUnmKCpd15mrINYmMmdIZT6grydL6TKZcUz2KPwxJ2IaGLpz10R9FmtcY_U3LlwTy8n2zrFYJdLvUaQMwEeeaYerlpAA1dw==';
    protected string $a_bogus = 'mJ0QkO2QMsm1GxOqMhkz9nGGTd80YW4dgZEN9D69Q0wM';

    public function getPanelInfo()
    {
        return $this->setHeader([
            'app-sdk-version' => '48.0.0',
        ])->json('mweb/v1/get_panel_info', [
            'app_id'       => 1775,
            'panel'        => 'dreamina_tone',
            'panel_source' => 'loki',
        ]);
    }

    public function getFeed(string $key)
    {
        return $this->setHeader([
            'app-sdk-version' => '48.0.0',
        ])
            ->setPathQuery([
                'aid'     => $this->aid,
                'msToken' => $this->msToken,
                'a_bogus' => $this->a_bogus,
            ])
            ->json('mweb/v1/feed', [
                'app_id'       => 1775,
                'category_key' => $key,
                'count'        => 50,
                'offset'       => 0,
                'panel'        => 'dreamina_tone',
                'panel_source' => 'loki',
            ]);
    }

    public function genTTS(string $text, string $id, string $loki_effect_id, float $speed)
    {
        $params = [
            'text'         => $text,
            'loki_info'    => json_encode([
                'effect_id'   => (int) $loki_effect_id,
                'model_names' => '',
                'effect_type' => 0,
            ], JSON_UNESCAPED_UNICODE),
            'audio_config' => [
                "format"           => "mp3",
                "sample_rate"      => 24000,
                "speech_rate"      => (int) bcmul(bcsub($speed, 1, 2), 100, 0),
                "pitch_rate"       => 0,
                "enable_timestamp" => true
            ],
            'id_info'      => [
                "id"            => $id,
                "item_platform" => 1,
            ],
        ];
        return $this->setHeader([
            'app-sdk-version' => '48.0.0',
        ])
            ->setPathQuery([
                'babi_param'      => json_encode([
                    "scenario"                => "image_video_generation",
                    "feature_key"             => "to_video-voice",
                    "feature_entrance"        => "to_video",
                    "feature_entrance_detail" => "to_video-voice"
                ]),
                'aid'             => $this->aid,
                'device_platform' => 'web',
                'region'          => 'CN',
                'web_id'          => $this->webId->getWebId(),
            ])
            ->json('mweb/v1/tts_generate', $params);
    }

    public function checkImage(PluginJmCheckResource $task)
    {
        $checkData = [];
        for ($i = 0; $i <= 2; $i++) {
            $checkData[] = [
                'image_create_avatar' => [
                    'image' => [
                        'image_uri' => $task->res_uri
                    ],
                    'mode'  => $i,
                ],
                'scene'               => 2,
                'submit_id'           => Str::uuid()->toString(),
            ];
        }
        return $this->setPathQuery([
            'aid'             => $this->aid,
            'device_platform' => 'web',
            'region'          => 'CN',
            'web_id'          => $this->webId->getWebId(),
        ])->json('mweb/v1/video_generate/pre_process', [
            'input_list' => $checkData,
        ]);
    }

    public function checkVideo(PluginJmCheckResource $task)
    {
        return $this->setPathQuery([
            'babi_param'      => json_encode([
                "scenario"                => "image_video_generation",
                "feature_key"             => "to_video-lipdetection",
                "feature_entrance"        => "to_video",
                "feature_entrance_detail" => "to_video-lipdetection"
            ], JSON_UNESCAPED_UNICODE),
            'aid'             => $this->aid,
            'device_platform' => 'web',
            'region'          => 'CN',
            'web_id'          => $this->webId->getWebId(),
        ])->json('mweb/v1/lip_sync_pre_process', [
            'scene'            => 'lip_sync_user_video',
            'video_input_info' => [
                'vid'       => $task->result_data['Vid'],
                'submit_id' => Str::uuid()->toString(),
            ],
        ]);
    }
}