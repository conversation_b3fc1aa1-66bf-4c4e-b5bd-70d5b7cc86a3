<?php

namespace App\Packages\JiMeng\Client;

use App\Exceptions\ValidatorException;
use App\Models\PluginJmCheckResource;
use App\Models\PluginJmDraw;
use App\Packages\JiMeng\BaseClient;
use App\Packages\JiMeng\Helper\UploadFile;
use App\Packages\JiMeng\JmResponse;
use Illuminate\Support\Str;

class Task extends BaseClient
{
    // 定义一个字符串类型的成员变量，用于存储msToken
    protected string $msToken = 'xi08wETk0OhmZ8S5Him6WvjkG3pKBgSXUnmKCpd15mrINYmMmdIZT6grydL6TKZcUz2KPwxJ2IaGLpz10R9FmtcY_U3LlwTy8n2zrFYJdLvUaQMwEeeaYerlpAA1dw==';
    protected string $a_bogus = 'mJ0QkO2QMsm1GxOqMhkz9nGGTd80YW4dgZEN9D69Q0wM';

    public function textImage(PluginJmDraw $task)
    {
        $componentId     = Str::uuid()->toString();
        $sizeImageConfig = collect($this->getConfigFormJsonFile('jm_image_size'));
        $sizeImage       = $sizeImageConfig->where('type', $task->params['ratio'])->first();
        $params          = [
            "extend"           => [
                "root_model"  => "high_aes_general_v21_L:general_v2.1_L",
                "template_id" => ""
            ],
            'submit_id'        => Str::uuid()->toString(),
            'metrics_extra'    => json_encode([
                "promptSource"     => "custom",
                "originSubmitId"   => Str::uuid()->toString(),
                "isDefaultSeed"    => 1,
                "originTemplateId" => "",
                "imageNameMapping" => [],
                "isUseAiGenPrompt" => false,
                "batchNumber"      => 1
            ]),
            'draft_content'    => json_encode([
                "type"              => "draft",
                "id"                => Str::uuid()->toString(),
                "min_version"       => "3.0.2",
                "min_features"      => [],
                "is_from_tsn"       => true,
                "version"           => "3.1.1",
                "main_component_id" => $componentId,
                'component_list'    => [
                    [
                        "type"          => "image_base_component",
                        "id"            => $componentId,
                        "min_version"   => "3.0.2",
                        "generate_type" => "generate",
                        "aigc_mode"     => "workbench",
                        'abilities'     => [
                            "type"     => "",
                            "id"       => Str::uuid()->toString(),
                            'generate' => [
                                "type"           => "",
                                "id"             => Str::uuid()->toString(),
                                'core_param'     => [
                                    "type"             => "",
                                    "id"               => Str::uuid()->toString(),
                                    "model"            => "high_aes_general_v21_L:general_v2.1_L",
                                    "prompt"           => $task->prompt,
                                    "negative_prompt"  => '',
                                    'seed'             => mt_rand(1000000000, 9999999999),
                                    "sample_strength"  => (float) bcdiv($task->params['sample_strength'], 10, 1),
                                    "image_ratio"      => (int) ($sizeImage['type'] ?? 1),
                                    'large_image_info' => [
                                        "type"   => "",
                                        "id"     => Str::uuid()->toString(),
                                        "height" => (int) ($sizeImage['ext']['height'] ?? 1024),
                                        "width"  => (int) ($sizeImage['ext']['width'] ?? 1024),
                                    ],
                                ],
                                'history_option' => [
                                    "type" => "",
                                    "id"   => Str::uuid()->toString(),
                                ]
                            ]
                        ]
                    ]
                ]
            ]),
            'http_common_info' => ['aid' => $this->aid]
        ];
        $task->params    = $params;
        $task->save();
        return $this->genImage($params);
    }

    private function getConfigFormJsonFile(string $type): array
    {
        $filePath = base_path("public/ai_style/".$type.".json");
        if (! file_exists($filePath)) {
            throw new ValidatorException('配置文件不存在');
        }
        $file = fopen($filePath, 'r');
        return json_decode(fread($file, filesize($filePath)), true);
    }

    protected function genImage(array $params)
    {
        return $this->setHeader([
            'appid'       => $this->aid,
            'appvr'       => '5.8.0',
            'device-time' => time(),
            'lan'         => 'zh-Hans',
            'loc'         => 'cn',
            'origin'      => 'https://jimeng.jianying.com',
            'pf'          => '7',
            'sign-ver'    => '1',
            'sign'        => 'c7c6962061acc58a993673cf68bfbcfb',
        ])->queryParams([
            'aid'             => $this->aid,
            'babi_param'      => json_encode([
                "scenario"                => "image_video_generation",
                "feature_key"             => "to_image_referenceimage_generate",
                "feature_entrance"        => "to_video",
                "feature_entrance_detail" => "to_video-referenceimage-byte_edit"
            ]),
            'device_platform' => 'web',
            'region'          => 'CN',
            'web_id'          => $this->webId->getWebId(),
        ])->json('mweb/v1/aigc_draft/generate', $params);
    }

    public function imageImage(PluginJmDraw $task)
    {
        $componentId = Str::uuid()->toString();
        $uploadFile  = new UploadFile($task->inputs[0]['url']);
        list($imageUri) = $uploadFile->upload();
        $params       = [
            'extend'           => [
                'root_model'  => 'high_aes_general_v20_L:general_v2.0_L',
                'template_id' => ''
            ],
            'submit_id'        => Str::uuid()->toString(),
            'draft_content'    => json_encode([
                "type"              => "draft",
                "id"                => Str::uuid()->toString(),
                "min_version"       => "3.0.2",
                "min_features"      => [],
                "is_from_tsn"       => true,
                "version"           => "3.1.1",
                "main_component_id" => $componentId,
                'component_list'    => [
                    [
                        "type"          => "image_base_component",
                        "id"            => $componentId,
                        "min_version"   => "3.0.2",
                        "generate_type" => "blend",
                        "aigc_mode"     => "workbench",
                        'abilities'     => [
                            "type"  => "",
                            "id"    => Str::uuid()->toString(),
                            'blend' => [
                                "type"                         => "",
                                "id"                           => Str::uuid()->toString(),
                                'core_param'                   => [
                                    "type"             => "",
                                    "id"               => Str::uuid()->toString(),
                                    "model"            => "high_aes_general_v20_L:general_v2.0_L",
                                    "prompt"           => "##​".$task->prompt,
                                    "sample_strength"  => (float) bcdiv($task->params['sample_strength'], 10, 1),
                                    "image_ratio"      => 1,
                                    'large_image_info' => [
                                        "type"   => "",
                                        "id"     => Str::uuid()->toString(),
                                        "height" => 1024,
                                        "width"  => 1024
                                    ]
                                ],
                                'ability_list'                 => [
                                    [
                                        "type"           => "",
                                        "id"             => Str::uuid()->toString(),
                                        "name"           => "byte_edit",
                                        'image_uri_list' => [$imageUri],
                                        'image_list'     => [
                                            [
                                                "type"          => "image",
                                                "id"            => Str::uuid()->toString(),
                                                "source_from"   => "upload",
                                                "platform_type" => 1,
                                                "name"          => "",
                                                "image_uri"     => $imageUri,
                                                "width"         => 0,
                                                "height"        => 0,
                                                "format"        => "",
                                                "uri"           => $imageUri
                                            ]
                                        ],
                                        "strength"       => (float) bcdiv($task->params['sample_strength'], 10, 1),
                                    ]
                                ],
                                'history_option'               => [
                                    "type" => "",
                                    "id"   => Str::uuid()->toString(),
                                ],
                                'prompt_placeholder_info_list' => [
                                    [
                                        "type"          => "",
                                        "id"            => Str::uuid()->toString(),
                                        "ability_index" => 0
                                    ]
                                ],
                                'postedit_param'               => [
                                    "type"          => "",
                                    "id"            => Str::uuid()->toString(),
                                    "generate_type" => 0
                                ],
                            ]
                        ]
                    ]
                ]
            ],
                JSON_UNESCAPED_UNICODE),
            'http_common_info' => ['aid' => $this->aid]
        ];
        $task->params = $params;
        $task->save();
        return $this->genImage($params);
    }
    // 创建任务的函数，返回JmResponse类型

    /**
     * 创建一个任务
     *
     * 此方法用于创建一个视频生成任务，可以是文本到视频或图像到视频根据任务类型的不同，调整相应的参数
     * 它构造了一个包含所有必要信息的请求，然后发送到相应的API端点
     *
     * @param  PluginJmDraw  $task  任务对象，包含任务的相关参数和类型
     * @return JmResponse 返回一个JmResponse对象，包含API响应的结果
     */
    public function createTask(PluginJmDraw $task): JmResponse
    {
        // 定义一个数组，用于存储任务参数
        $params = [
            // 生成一个随机的UUID作为任务ID
            'submit_id'         => Str::uuid()->toString(),
            // 任务额外信息，包括提示来源、原始任务ID等
            'task_extra'        => json_encode([
                "promptSource"     => "custom",
                "originSubmitId"   => Str::uuid()->toString(),
                "isDefaultSeed"    => 1,
                "originTemplateId" => "",
                "imageNameMapping" => [],
                "isUseAiGenPrompt" => false,
                "batchNumber"      => 1
            ]),
            // 公共HTTP信息，包含请求ID
            'http_common_info'  => [
                'aid' => $this->aid,
            ],
            // 输入参数，包括视频宽高比、种子等
            'input'             => [
                'video_aspect_ratio' => $task->params['video_aspect_ratio'],
                'seed'               => mt_rand(1000000000, 9999999999),
                'video_gen_inputs'   => $task->params['video_gen_inputs'],
                "priority"           => 0,
                "model_req_key"      => $task->params['model']
            ],
            // 模式为工作台模式
            "mode"              => "workbench",
            // 历史选项为空数组
            'history_option'    => [],
            // 商业信息，包括资源ID、资源子类型等
            'commerce_info'     => [
                "resource_id"       => "generate_video",
                "resource_id_type"  => "str",
                "resource_sub_type" => "aigc",
                "benefit_type"      => $task->params['benefit_type']
            ],
            // 客户端跟踪数据为空数组
            'client_trace_data' => [],
        ];
        // 定义一个数组，用于存储特定的业务参数
        $babiParam = [
            "scenario"                => "image_video_generation",
            "feature_key"             => "text_to_video",
            "feature_entrance"        => "to_video",
            "feature_entrance_detail" => "to_video-text_to_video"
        ];
        // 根据任务类型调整参数
        if ($task->type == PluginJmDraw::TYPE_IMAGE_VIDEO) {
            unset($params['input']['video_aspect_ratio']);
            $babiParam = [
                "scenario"                => "image_video_generation",
                "feature_key"             => "image_to_video",
                "feature_entrance"        => "to_video",
                "feature_entrance_detail" => "to_video-image_to_video"
            ];
        }

        // 返回一个JmResponse类型的响应
        return $this->setHeader([
            'appid'       => $this->aid,
            'appvr'       => '5.8.0',
            'device-time' => time(),
            'lan'         => 'zh-Hans',
            'loc'         => 'cn',
            'origin'      => 'https://jimeng.jianying.com',
            'pf'          => '7',
            'sign-ver'    => '1',
            'sign'        => 'c7c6962061acc58a993673cf68bfbcfb',
        ])->queryParams([
            'aid'             => $this->aid,
            'babi_param'      => json_encode($babiParam),
            'device_platform' => 'web',
            'region'          => 'CN',
            'web_id'          => $this->webId->getWebId(),
            'msToken'         => $this->msToken,
            'a_bogus'         => $this->a_bogus
        ])->json('mweb/v1/generate_video', $params);
    }

    public function syncLip(PluginJmDraw $task)
    {
        $params = [
            'submit_id'         => '',
            'task_extra'        => json_encode($task->params['task_extra']),
            'http_common_info'  => [
                'aid' => $this->aid,
            ],
            'input'             => [
                'seed'             => mt_rand(1000000000, 9999999999),
                'video_gen_inputs' => $task->params['video_gen_inputs'],
            ],
            "mode"              => "workbench",
            'history_option'    => [],
            'commerce_info'     => [
                "resource_id"       => "generate_video",
                "resource_id_type"  => "str",
                "resource_sub_type" => "aigc",
                "benefit_type"      => $task->params['benefit_type']
            ],
            "scene"             => $task->params['scene'],
            "client_trace_data" => [],
            "submit_id_list"    => [
                Str::uuid()->toString(),
            ],
        ];

        return $this->setHeader([
            'appid'       => $this->aid,
            'appvr'       => '5.8.0',
            'device-time' => time(),
            'lan'         => 'zh-Hans',
            'loc'         => 'cn',
            'origin'      => 'https://jimeng.jianying.com',
            'pf'          => '7',
            'sign-ver'    => '1',
            'sign'        => 'c7c6962061acc58a993673cf68bfbcfb',
        ])->queryParams([
            'aid'             => $this->aid,
            'babi_param'      => json_encode($task->params['babi_param']),
            'device_platform' => 'web',
            'region'          => 'CN',
            'web_id'          => $this->webId->getWebId(),
        ])->json('mweb/v1/batch_generate_video', $params);
    }

    /**
     * 查询生成任务的状态
     *
     * 该方法用于查询特定生成任务的状态它通过发送一个JSON请求来实现这一点
     * 请求中包含了应用ID、设备平台信息、地区和web ID，以及需要查询的任务ID
     *
     * @param  PluginJmDraw  $task  代表一个生成任务的插件对象，包含任务ID等信息
     * @return mixed 查询结果，通常是一个包含任务状态响应的JSON对象
     */
    public function query(PluginJmDraw|PluginJmCheckResource $task)
    {
        // 设置查询路径和基础查询参数
        return $this->setPathQuery([
            'aid'             => $this->aid,
            'device_platform' => 'web',
            'region'          => 'CN',
            'web_id'          => $this->webId->getWebId(), // web ID
        ])->json('mweb/v1/mget_generate_task', [
            'task_id_list' => is_array($task->task_id) ? $task->task_id : [
                $task->task_id,
            ]
        ]);
    }

    public function queryHistory(PluginJmDraw $task)
    {
        return $this->setPathQuery([
            'aid'             => $this->aid,
            'device_platform' => 'web',
            'region'          => 'CN',
            'web_id'          => $this->webId->getWebId(), // web ID
        ])->json('mweb/v1/get_history_by_ids', [
            'history_ids'      => [
                $task->serial_no,
            ],
            "http_common_info" => [
                "aid" => $this->aid,
            ]
        ]);
    }

    public function mgetPreProcessResult(PluginJmCheckResource $task)
    {
        return $this->setPathQuery([
            'aid'             => $this->aid,
            'device_platform' => 'web',
            'region'          => 'CN',
            'web_id'          => $this->webId->getWebId(), // web ID
        ])->json('mweb/v1/video_generate/mget_pre_process_result', [
            'submit_id_list' => $task->tasks
        ]);
    }

    /**
     * 删除生成任务
     *
     * 本函数负责向服务器发送删除特定生成任务的请求通过构建必要的请求参数和配置信息，
     * 它调用了一个内部方法来设置请求路径和参数，然后发送请求以删除任务
     *
     * @param  PluginJmDraw  $task  要删除的生成任务对象，包含任务的必要信息如序列号
     * @return mixed 删除任务请求的响应结果，具体类型取决于服务器的响应和处理
     */
    public function delete(PluginJmDraw $task)
    {
        // 设置请求参数数组，包含删除任务所需的固定参数和配置信息
        return $this->setPathQuery([
            'aid'             => $this->aid,
            'device_platform' => 'web',
            'region'          => 'CN',
            'web_id'          => $this->webId->getWebId(), // web ID
        ])->json('mweb/v1/delete_generate_task', [
            'id_list' => [$task->serial_no] // 将任务的序列号作为数组传递，以便指定要删除的任务
        ]);
    }

}