<?php

namespace App\Packages\DeepSeek;

use Psr\Http\Message\ResponseInterface;

class DeepSeekResponse
{
    protected bool   $success;
    protected string $message;
    protected array  $data;

    public function __construct(
        ResponseInterface|string $response,
        protected array $params = []
    ) {
        if ($response instanceof ResponseInterface) {
            if ($response->getStatusCode() == 200) {
                $result        = json_decode($response->getBody()
                    ->getContents(),
                    true);
                $this->success = true;
                $this->data    = $result['data'];
            } else {
                $this->message = $response->getBody()->getContents();
            }
        } else {
            $this->success = false;
            $this->message = $response;
        }
    }

    /**
     * @param  string  $message
     * @param  array  $params
     * @return \App\Packages\DeepSeek\DeepSeekResponse
     */
    public static function error(
        string $message,
        array $params
    ): DeepSeekResponse {
        return new static($message, $params);
    }

    public function isError(): bool
    {
        return ! $this->isSuccess();
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function toArray(): array
    {
        return $this->data;
    }

    public function __get(string $name)
    {
        return $this->data[$name] ?? null;
    }
}