<?php

namespace App\Packages\DeepSeek;

use App\Models\SystemConfig;
use Exception;
use GuzzleHttp\Client;

class BaseClient
{
    protected string $baseUrl     = 'https://api.deepseek.com';
    protected string $apiKey;
    protected string $pathVer     = 'v1';
    protected Client $client;
    protected string $temperature = '0.0';

    public function __construct()
    {
        $this->apiKey = SystemConfig::getValue('DeepSeekAPIKEY');
        $this->client = new Client([
            'base_uri' => $this->baseUrl,
        ]);
    }

    public function get(
        string $path,
        array $params = []
    ): DeepSeekResponse|DeepSeekStreamResponse {
        return $this->request('GET', $path, $params, 'query');
    }

    protected function request(
        string $method,
        string $path,
        array $params = [],
        string $patamsType = 'form-param',
        bool $stream = false,
    ): DeepSeekResponse|DeepSeekStreamResponse {
        try {
            $response = $this->client->request($method, $path, [
                'headers'   => $this->getHeaders(),
                $patamsType => $params,
                'stream'    => $stream,
            ]);
            if ($stream) {
                return new DeepSeekStreamResponse($response, $params);
            } else {
                return new DeepSeekResponse($response, $params);
            }
        } catch (Exception $e) {
            return DeepSeekResponse::error($e->getMessage(), $params);
        }
    }

    private function getHeaders(): array
    {
        return [
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer '.$this->apiKey,
        ];
    }

    public function json(
        string $path,
        array $params = [],
        bool $stream = false
    ): DeepSeekResponse|DeepSeekStreamResponse {
        return $this->request('POST', $path, $params, 'json', $stream);
    }
}