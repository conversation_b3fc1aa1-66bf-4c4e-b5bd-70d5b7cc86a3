<?php

namespace App\Packages\DeepSeek\Client;

use App\Packages\DeepSeek\BaseClient;

class Chat extends BaseClient
{
    public function chat(array $content)
    {
        return $this->json('chat/completions', [
            'messages'    => array_merge([
                [
                    'role'    => 'system',
                    'content' => '你是一个教学专家，通过传入的图片地址分析图片上的题目做出判断和解释.',
                ]
            ], $content),
            'model'       => 'deepseek-chat',
            'max_tokens'  => 8192,
            'temperature' => 0,
            'stream'      => true,
        ], true);
    }
}