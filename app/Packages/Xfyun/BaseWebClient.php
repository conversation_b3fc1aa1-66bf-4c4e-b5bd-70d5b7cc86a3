<?php

namespace App\Packages\Xfyun;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;

class BaseWebClient
{
    const MD5_TABLE = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'];
    protected string $appId  = '8e9b71d6';
    protected string $secret = 'ZGJhZmQ2NDE4NDQ4OTJmNzk0MjhiMTA1';
    protected int    $timeStamp;
    protected Client $client;

    public function __construct()
    {
        $this->appId     = env('XUNFEI_APPID');
        $this->secret    = env('XUNFEI_SECRET');
        $this->timeStamp = time();
        $this->client    = new Client([
            'verify' => false,
        ]);
    }

    protected function json(string $url, array $params)
    {
        return $this->request('POST', $url, $params, 'json');
    }

    protected function request(string $method, string $url, array $params, string $paramsType = 'form_params')
    {
        try {
            $response = $this->client->request($method, $url, [
                'headers'   => $this->getHeaders(),
                $paramsType => $params,
            ]);
            if ($response->getStatusCode() == 200) {
                return [
                    'status'  => true,
                    'message' => 'success',
                    'data'    => json_decode($response->getBody()->getContents(), true),
                ];
            } else {
                return [
                    'status'  => false,
                    'message' => 'error',
                    'data'    => json_decode($response->getBody()->getContents(), true),
                ];
            }
        } catch (RequestException $exception) {
            $content = $exception->getResponse()->getBody()->getContents();
            if (Str::isJson($content)) {
                return [
                    'status'  => false,
                    'message' => $exception->getMessage(),
                    'data'    => json_decode($content, true),
                ];
            } else {
                return [
                    'status'  => false,
                    'message' => $exception->getMessage(),
                    'data'    => [],
                ];
            }
        } catch (Exception $exception) {
            return [
                'status'  => false,
                'message' => $exception->getMessage(),
                'data'    => [],
            ];
        }
    }

    protected function getHeaders()
    {
        return [
            'appId'     => $this->appId,
            'timestamp' => (string) $this->timeStamp,
            'signature' => $this->getSignature(),
        ];
    }

    protected function getSignature(): string
    {
        $auth = $this->md5(sprintf("%s%s", $this->appId, $this->timeStamp));
        return base64_encode(hash_hmac('sha1', $auth, $this->secret, true));
    }

    private function md5(string $cipherText): string
    {
        $md     = md5($cipherText, true); // 二进制输出
        $length = strlen($md);
        $str    = '';

        for ($i = 0; $i < $length; $i++) {
            $byte = ord($md[$i]);
            $str  .= self::MD5_TABLE[($byte >> 4) & 0xf];
            $str  .= self::MD5_TABLE[$byte & 0xf];
        }

        return $str;
    }

    protected function post(string $url, array $params)
    {
        return $this->request('POST', $url, $params);
    }

    protected function multipart(string $url, array $params)
    {
        $data = [];
        foreach ($params as $key => $param) {
            if (is_array($param)) {
                $data[] = $param;
            } else {
                $data[] = [
                    'name'     => $key,
                    'contents' => $param
                ];
            }
        }
        return $this->request('POST', $url, $data, 'multipart');
    }
}