<?php

namespace App\Packages\FastGpt;

use App\Exceptions\ValidatorException;
use App\Models\SystemConfig;
use Illuminate\Support\Facades\Cache;

class Token extends BaseClient
{
    protected string $type          = 'Bearer';
    protected string $authorization = '';
    protected int    $ttl           = 3600;
    private string   $path          = '';
    private string   $cacheKey;
    private string   $password;
    private string   $username;

    public function __construct()
    {
        parent::__construct();
        if ($this->fastgpt_is_new) {
            $this->cacheKey = 'new_fastgpt_token';
            $this->username = SystemConfig::getValue('new_fastgpt_username');
            $this->password = SystemConfig::getValue('new_fastgpt_password');
        } else {
            $this->cacheKey = 'fastgpt_token';
            $this->username = SystemConfig::getValue('FASTGPT_USERNAME');
            $this->password = SystemConfig::getValue('FASTGPT_PASSWORD');
        }

        $this->path = '/api/support/user/account/loginByPassword';
        $this->ttl  = SystemConfig::getValue('FastGptLoginTtl', 7200);
    }

    /**
     * 获取Auth
     *
     * @return string
     */
    public function getToken(): string
    {
        return $this->getAuthorization();
    }

    protected function getAuthorization(): string
    {
        return Cache::has($this->cacheKey) ? Cache::get($this->cacheKey) : $this->login();
    }

    protected function login(): string
    {
        $result = $this->setParams([
            'username' => $this->username,
            'password' => hash('sha256', $this->password),
        ])->post($this->path);

        if (($result['code'] ?? false) && $result['code'] == 200) {
            $token = $result['data']['token'];
            Cache::set($this->cacheKey, $token, $this->ttl);
            return $token;
        } else {
            throw new ValidatorException('请上传参数'.$result['params']);
        }
    }
}