<?php

namespace App\Packages\FastGpt\Data;

use App\Packages\FastGpt\BaseModuleClient;

class Client extends BaseModuleClient
{
    /**
     * Notes: 添加数据
     *
     * @Author: 玄尘
     * @Date: 2024/12/18 09:17
     * @param $collectionId
     * @param $training_mode
     * @param $q
     * @param $a
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function create($collectionId, $training_mode, $q, $a)
    {
        return $this->sendRequest('post', '/core/dataset/data/pushData', [
            'collectionId' => $collectionId,
            'trainingMode' => $training_mode,
            'data'         => [
                [
                    'q' => $q,
                    'a' => $a ?? ''
                ]
            ]
        ]);
    }

    public function list($collectionId, $content, $page, $pageSize)
    {
        $path = '/core/dataset/data/list';
        if ($this->fastgpt_is_new) {
            $path = '/core/dataset/data/v2/list';
        }
        return $this->sendRequest('post', $path, [
            'collectionId' => $collectionId,
            'pageNum'      => $page,
            'pageSize'     => $pageSize,
            'searchText'   => $content ?? ''
        ]);
    }

    public function detail($dataId)
    {
        return $this->sendRequest('get', '/core/dataset/data/detail', [
            'id' => $dataId
        ]);
    }

    public function update($dataId, $q, $a)
    {
        $idKey = 'id';
        if ($this->fastgpt_is_new) {
            $idKey = 'dataId';
        }
        return $this->sendRequest('put', '/core/dataset/data/update', [
            $idKey => $dataId,
            'q'    => $q,
            'a'    => $a ?? ''
        ]);
    }

    public function dataDelete($dataId)
    {
        return $this->sendRequest('delete', '/core/dataset/data/delete', [
            'id' => $dataId
        ]);
    }

}