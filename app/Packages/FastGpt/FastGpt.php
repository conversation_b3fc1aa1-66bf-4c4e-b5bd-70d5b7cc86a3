<?php

namespace App\Packages\FastGpt;

use App\Packages\FastGpt\App\Client as AppClient;
use App\Packages\FastGpt\Chat\Client as ChatClient;
use App\Packages\FastGpt\Knowledge\Client as KnowledgeClient;
use App\Packages\FastGpt\Data\Client as DataClient;
use App\Packages\FastGpt\Set\Client as SetClient;

class FastGpt
{
    public static function knowledge(): KnowledgeClient
    {
        return new KnowledgeClient();
    }

    public static function chat(): ChatClient
    {
        return new ChatClient();
    }

    public static function app(): AppClient
    {
        return new AppClient();
    }

    public static function data(): DataClient
    {
        return new DataClient();
    }

    public static function set(): SetClient
    {
        return new SetClient();
    }

}