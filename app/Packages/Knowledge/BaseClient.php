<?php

namespace App\Packages\Knowledge;

use App\Exceptions\ValidatorException;
use App\Models\SystemConfig;

class BaseClient
{
    protected string $baseUri;
    protected array  $headers = [];
    protected array  $params  = [];
    protected bool   $hasFile = false;
    protected        $file;
    protected bool   $debug   = false;

    public function __construct()
    {
        $this->baseUri                 = SystemConfig::getValue('KnowledgeUrl');
        $this->headers['Content-Type'] = 'application/json';
    }

    public function debug()
    {
        $this->debug = true;
        return $this;
    }

    /**
     * @return $this
     */
    protected function setEnvAuthorization(): self
    {
        $this->headers['Authorization'] = 'Bearer '.SystemConfig::getValue('Authorization');
        return $this;
    }

    protected function setEnvStream(): self
    {
        $this->headers['Accept'] = 'text/event-stream';
        return $this;
    }

    protected function setApiKeyAuthorization($authorization): self
    {
        $this->headers['Authorization'] = 'Bearer '.$authorization;
        return $this;
    }

    /**
     * @return $this
     */
    protected function setAuthorization(): self
    {
        $token                          = new Token();
        $this->headers['Authorization'] = $token->getToken();
        return $this;
    }

    protected function setParams(array $params)
    {
        $this->params = $params;
        return $this;
    }

    protected function setFile($file)
    {
        $this->file    = $file;
        $this->hasFile = true;
        return $this;
    }

    protected function get(string $path): array
    {
        return $this->request($path, 'GET');
    }

    /**
     * 数据包发送
     *
     * @param  string  $method
     * @param  string  $url
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function request(string $url, string $method, string $type = ''): array
    {
        try {
            if (! $this->baseUri) {
                throw new ValidatorException("缺少接口地址");
            }

            $paramsName = 'json';
            if ($method == 'GET') {
                $paramsName = 'query';
            }

            $client = new \GuzzleHttp\Client();
            if ($type == 'MULTIPART') {
                unset($this->headers['Content-Type']);
                $response = $client->request($method, $this->baseUri.$url, [
                    'headers'   => $this->headers,
                    'multipart' => $this->params,
                ]);
            } elseif ($type == 'stream') {
                // 发送请求
                $response = $client->request($method, $this->baseUri.$url, [
                    'stream'    => true,  // 启用流式响应
                    'headers'   => $this->headers,
                    $paramsName => $this->params,
                ]);
            } else {
                $response = $client->request($method, $this->baseUri.$url, [
                    'headers'   => $this->headers,
                    $paramsName => $this->params,
                ]);
            }

            if ($this->debug) {
                dump($method);
                dump($this->baseUri.$url);
                dump($this->headers);
                dump($this->params);
                die;
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode == 200 || $statusCode == 201 || $statusCode == 0) {
                if ($type == 'stream') {
                    $body    = $response->getBody();
                    $content = $body->getContents();

                    $dataStream = trim($content, '"""');
                    // 按换行符分割每个数据块
                    $chunks = explode("\n\n", $dataStream);
                    $data   = [];
                    foreach ($chunks as $chunk) {
                        $data[] = json_decode(substr($chunk, 5), true);;
                    }
                    return $data;
                }
                $data = json_decode($response->getBody()->getContents(), true);
                return $data;
            } elseif ($statusCode == 400) {
                $error = json_decode($response->getBody()->getContents(), true);
                if ($error['message'] ?? false) {
                    throw new KnowledgeException($error['message']);
                }
                throw new \Exception("返回内容错误【Un】");
            } else {
                throw new \Exception("接口服务器访问异常【{$response->getStatusCode()}】");
            }
        } catch (\Exception $exception) {
            $statusCode = $exception->getResponse()?->getStatusCode();
            if ($statusCode == 401) {
                throw new \Exception("接口服务器访问异常【{$statusCode}】");
            }
            throw new \Exception($exception->getMessage());
        }
    }

    protected function patch(string $path): array
    {
        return $this->request($path, 'PATCH');
    }

    protected function multipart(string $path): array
    {
        return $this->request($path, 'POST', 'MULTIPART');
    }

    protected function post(string $path): array
    {
        return $this->request($path, 'POST');
    }

    protected function delete(string $path): array
    {
        return $this->request($path, 'DELETE');
    }

    protected function stream(string $path): array
    {
        return $this->request($path, 'POST', 'stream');
    }

}