<?php

namespace App\Packages\Knowledge;

use App\Exceptions\ValidatorException;
use App\Models\SystemConfig;
use Illuminate\Support\Facades\Cache;

class Token extends BaseClient
{
    protected string $type          = 'Bearer';
    protected string $authorization = '';
    protected int    $ttl           = 3600;//7200会导致token过期的问题
    private string   $path          = '';
    private string   $cacheKey      = 'knowpia_authorization';

    public function __construct()
    {
        parent::__construct();
        $this->path = '/console/api/login';
        $this->ttl  = SystemConfig::getValue('KnowledgeLoginTtl', 7200);
    }

    /**
     * 获取Auth
     *
     * @return string
     */
    public function getToken(): string
    {
        $authorization = $this->getAuthorization();
        return $this->type.' '.$authorization['access_token'];
    }

    protected function getAuthorization(): array
    {
        return Cache::has($this->cacheKey) ? Cache::get($this->cacheKey) : $this->login();
    }

    protected function login(): array
    {
        $result = $this->setParams([
            'email'    => SystemConfig::getValue('KNOWPIA_USERNAME'),
            'password' => SystemConfig::getValue('KNOWPIA_PASSWORD'),
        ])->post($this->path);
        if (($result['result'] ?? false) && $result['result'] == 'success') {
            $authorization = $result['data'];
            Cache::set($this->cacheKey, $authorization, $this->ttl);
            return $authorization;
        } else {
            throw new ValidatorException('请上传参数'.$result['params']);
        }
    }
}