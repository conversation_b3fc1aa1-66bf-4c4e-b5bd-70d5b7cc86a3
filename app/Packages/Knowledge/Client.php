<?php

namespace App\Packages\Knowledge;

use App\Exceptions\ValidatorException;
use Illuminate\Support\Facades\Log;

class Client extends BaseClient
{
    protected string $path = '/v1';

    public function modelConfig(string $app_id, array $query)
    {
        $url = '/console/api/apps/'.$app_id.'/model-config';

        try {
            return $this->setAuthorization()
                ->setParams($query)
                ->post($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 创建智能体
     *
     * @Author: 玄尘
     * @Date: 2024/12/2 18:06
     * @param  array  $query
     * @return array
     * @throws \Exception
     */
    public function apps(array $query)
    {
        $url = '/console/api/apps';

        try {
            return $this->setAuthorization()
                ->setParams($query)
                ->post($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 获取智能助手api-key
     *
     * @Author: 玄尘
     * @Date: 2024/12/3 13:16
     * @param  string  $app_id
     * @return array
     * @throws \Exception
     */
    public function getApiKey(string $app_id)
    {
        $url = '/console/api/apps/'.$app_id.'/api-keys';

        try {
            return $this->setAuthorization()->post($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * 知识库文档列表
     *
     * @param $kb_id
     * @param  int  $page
     * @param  int  $pageSize
     * @return array
     */
    public function documents($kb_id, string $keyWord = '', int $page = 1, int $pageSize = 5): array
    {
        $query = [
            'page'  => $page,
            'limit' => $pageSize,
        ];
        if (! empty($keyWord)) {
            $query['keyword'] = $keyWord;
        }
        try {
            return $this->setEnvAuthorization()
                ->setParams($query)
                ->get($this->path."/datasets/{$kb_id}/documents");
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * 创建知识库
     *
     * @param  string  $name  知识库名称
     * @param  string  $permission  权限
     * @return array
     * @throws \Exception
     */
    public function create(string $name, string $permission = ''): array
    {
        $query = [
            'name' => $name
        ];
        if (! empty($permission)) {
            $query['permission'] = $permission;
        }

        try {
            return $this->setEnvAuthorization()
                ->setParams($query)
                ->post($this->path.'/datasets', $query);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function reName(string $datasetId, array $query)
    {
        $url = '/console/api/datasets/'.$datasetId;
        try {
            return $this->setAuthorization()
                ->setParams($query)
                ->patch($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function addFile(string $datasetId, array $query)
    {
        $url = $this->path."/datasets/".$datasetId."/document/create_by_file";
        try {
            return $this->setEnvAuthorization()
                ->setParams($query)
                ->multipart($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function fileDetail(string $datasetId, string $fileId, ?string $keyword = '')
    {
        $url = $this->path."/datasets/".$datasetId."/documents/".$fileId."/segments";
        try {
            return $this->setEnvAuthorization()
                ->setParams([
                    'keyword' => $keyword,
                ])
                ->get($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 新增分段
     *
     * @Author: 玄尘
     * @Date: 2024/12/2 16:04
     * @return array
     * @throws \Exception
     */
    public function addSegments(
        string $datasetId,
        string $fileId,
        string $content,
        ?string $answer = '',
        ?string $keyword = ''
    ) {
        $url = $this->path."/datasets/".$datasetId."/documents/".$fileId."/segments";
        try {
            return $this->setEnvAuthorization()
                ->setParams([
                    'segments' => [
                        [
                            'content'  => $content,
                            'answer'   => $answer ?? '',
                            'keywords' => $keyword ? explode(',', $keyword) : '',
                        ]
                    ]
                ])
                ->post($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 删除分段
     *
     * @Author: 玄尘
     * @Date: 2024/12/2 17:26
     * @param  string  $datasetId
     * @param  string  $fileId
     * @param  string  $segmentId
     * @return array
     * @throws \Exception
     */
    public function delSegments(string $datasetId, string $fileId, string $segmentId)
    {
        $url = $this->path.'/datasets/'.$datasetId.'/documents/'.$fileId.'/segments/'.$segmentId;

        try {
            return $this->setEnvAuthorization()->delete($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function updateSegments(
        string $datasetId,
        string $fileId,
        string $segmentId,
        string $content,
        string $answer = '',
        ?string $keyword = ''
    ) {
        $url = $this->path.'/datasets/'.$datasetId.'/documents/'.$fileId.'/segments/'.$segmentId;

        try {
            return $this->setEnvAuthorization()
                ->setParams([
                    'segment' => [
                        'content'  => $content,
                        'answer'   => $answer ?? '',
                        'keywords' => $keyword ? explode(',', $keyword) : '',
                        'enabled'  => true
                    ]
                ])
                ->post($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function enableSegments(
        string $datasetId,
        string $segmentId,
        string $name,
    ) {
        $url = "/console/api/datasets/".$datasetId."/segments/".$segmentId."/".$name;

        try {
            return $this->setAuthorization()->patch($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function reFileName(string $datasetId, string $fileId, array $query)
    {
        $url = "/console/api/datasets/".$datasetId."/documents/".$fileId."/rename";
        try {
            return $this->setAuthorization()
                ->setParams($query)
                ->post($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function delFile(string $datasetId, string $fileId)
    {
        $url = $this->path.'/datasets/'.$datasetId.'/documents/'.$fileId;
        try {
            return $this->setEnvAuthorization()->delete($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function queries(string $datasetId, array $query)
    {
        $url = "/console/api/datasets/".$datasetId."/queries";
        try {
            return $this->setAuthorization()
                ->setParams($query)
                ->get($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function hitTesting(string $datasetId, array $query)
    {
        $url = "/console/api/datasets/".$datasetId."/hit-testing";
        try {
            return $this->setAuthorization()
                ->setParams($query)
                ->post($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 消息发送
     *
     * @Author: 玄尘
     * @Date: 2024/12/3 14:49
     * @param  string  $api_key
     * @param  array  $chatMessage
     * @return array
     * @throws \Exception
     */
    public function chat(string $api_key, array $chatMessage)
    {
        $url = $this->path."/chat-messages";

        try {
            $res = $this->setApiKeyAuthorization($api_key)
                ->setParams($chatMessage)
                ->stream($url);
            return $res;
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function getMessage(string $app_id, $params)
    {
        $url = '/console/api/apps/'.$app_id.'/chat-messages';

        try {
            return $this->setAuthorization()
                ->setParams($params)
                ->get($url);
        } catch (KnowledgeException $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            throw new ValidatorException($e->getMessage());
        }
    }

    public function getAuthorization()
    {
        $this->setAuthorization();
        return $this->headers['Authorization'];
    }

}