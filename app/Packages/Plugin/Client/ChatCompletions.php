<?php
namespace App\Packages\Plugin\Client;

use App\Exceptions\ValidatorException;
use App\Packages\Plugin\BaseClient;
use Exception;
use Illuminate\Support\Facades\Log;

class ChatCompletions extends BaseClient
{

    public function create(array $query)
    {
        $url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
        try {
            $this->curlStreamRequest($url, $query, $message = '解释这张图片');
            die;
        } catch (Exception $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        }
    }
    function curlStreamRequest(string $url, $postData, string $messageT)
    {
        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 0); // 无限超时
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER,['Content-Type: application/json', 'Accept: text/event-stream', 'Authorization: Bearer '. env('ZHIPU_APPKEY')]);
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $data) {
            echo $data;
            ob_flush();
            flush();
            usleep(20000);
            if (strpos($data, '[DONE]')) {
                //超
//                $this->defaultData['text'] = $this->tool;
//                $item                      = AppAiSearch::create($this->defaultData);
//                $map['type']               = empty($data['type']) ?? '';
//                $map['uid']                = $this->defaultData['user_id'];
//                $map['froms']              = "数字名片";
//                $map['source']             = "AI搜索";
//                $map['tokens']             = 0;
//                $map['mid']                = $item->id;
//                $map['app_id']             = $this->defaultData['conversation_id'];
//                $map['kb_id']              = 0;
//                $map['content']            = json_encode([
//                    'question' => $this->defaultData['title'],
//                    'answer'   => $this->defaultData['content']
//                ]);
            } else {
//                $this->add($data);
            }
            return strlen($data);
        });
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300); // 增加连接超时时间
        curl_setopt($ch, CURLOPT_BUFFERSIZE, 0);       // 调整缓冲区大小
        $response = curl_exec($ch);
        if ($response === false) {
            die('Curl error: '.curl_error($ch));
        }
        curl_close($ch);
    }

}
