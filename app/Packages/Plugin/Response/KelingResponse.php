<?php

namespace App\Packages\Plugin\Response;

use App\Packages\Plugin\PluginResponse;
use Psr\Http\Message\ResponseInterface;

class KelingResponse extends PluginResponse
{
    public function __construct(string|ResponseInterface $response, array $params = [])
    {
        if ($response instanceof ResponseInterface) {
            if ($response->getStatusCode() == 200) {
                $res    = json_decode($response->getBody()->getContents(), true);
                $status = $res['status'] ?? 200;
                if (($res['result'] ?? false) && $status == 200) {
                    parent::__construct(true, $res['message'], $res['data'] ?: [], $params);
                } else {
                    parent::__construct(false, $res['message'], [], $params);
                }
            } else {
                $message = $response->getBody()->getContents();
                parent::__construct(false, $message, [], $params);
            }
        } else {
            parent::__construct(false, $response, [], $params);
        }
    }

    public function getTaskId()
    {
        return $this->data['task']['id'] ?? '';
    }
}