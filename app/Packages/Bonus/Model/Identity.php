<?php

namespace App\Packages\Bonus\Model;

use Modules\User\Models\IdentityOrder;

class Identity
{
    public function __construct(protected IdentityOrder $order)
    {
    }

    public function bonus()
    {
        $user  = $this->order->user;
        $total = $this->order->amount;
        if ($user->relation->parent_id != 0) {
            $parent         = $user->relation->parent;
            $parentIdentity = $parent->identities()->first();
            $rate           = $parentIdentity->getRules('recharge_one', 0);
            if ($rate > 0 && $parent->account->logs()
                    ->where('source->order_class',
                        $this->order->getMorphClass())
                    ->where('source->order_id', $this->order->getKey())
                    ->doesntExist()) {
                $cash = bcmul($total, bcdiv($rate, 100, 4), 2);
                if ($cash > 0) {
                    $parent->account->exec('bonus', $cash, null, [
                        'remark'      => '邀请用户开通VIP',
                        'layer'       => 1,
                        'order_class' => $this->order->getMorphClass(),
                        'order_id'    => $this->order->getKey(),
                    ]);
                }
            }
            if ($parent->relation->parent_id != 0) {
                $parent         = $parent->relation->parent;
                $parentIdentity = $parent->identities()->first();
                $rate           = $parentIdentity->getRules('recharge_two', 0);
                if ($rate > 0) {
                    $cash = bcmul($total, bcdiv($rate, 100, 4), 2);
                    if ($cash > 0 && $parent->account->logs()
                            ->where('source->order_class',
                                $this->order->getMorphClass())
                            ->where('source->order_id', $this->order->getKey())
                            ->doesntExist()) {
                        $parent->account->exec('bonus', $cash, null, [
                            'remark'      => '邀请用户开通VIP',
                            'layer'       => 2,
                            'order_class' => $this->order->getMorphClass(),
                            'order_id'    => $this->order->getKey(),
                        ]);
                    }
                }
            }
        }
    }
}