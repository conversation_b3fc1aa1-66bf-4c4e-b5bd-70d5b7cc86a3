<?php

namespace App\Packages\DrawImage;

use GuzzleHttp\Client;

class BaseDriver
{
    protected string $baseUri;
    protected array  $params;
    protected array  $headers;

    public function setUrl(string $url): void
    {
        $this->baseUri = $url;
    }

    public function setHeaders(array $headers): void
    {
        $this->headers = $headers;
    }

    public function setParams(array $params): void
    {
        $this->params = $params;
    }

    public function post(): DrawResponse
    {
        return $this->request('POST', 'form_data');
    }

    protected function request(string $method, string $paramsType): DrawResponse
    {
        $client   = new Client([
            'verify' => false,
        ]);
        $response = $client->request($method, $this->baseUri, [
            'headers'   => $this->headers,
            $paramsType => $this->params,
        ]);
        return new DrawResponse($response, $this->params);
    }

    public function get(): DrawResponse
    {
        return $this->request('GET', 'query');
    }

    public function json(): DrawResponse
    {
        return $this->request('POST', 'json');
    }
}