<?php

namespace App\Packages\DrawImage;

use App\Models\DrawImageStyle;
use App\Packages\DrawImage\Drivers\BigModel;
use App\Packages\DrawImage\Drivers\BigModelVideo;
use App\Packages\DrawImage\Drivers\DouBao;

class DrawImage
{
    /**
     * @param  \App\Models\DrawImageStyle  $style
     * @return \App\Packages\DrawImage\Drivers\BigModel
     */
    public static function BigModel(DrawImageStyle $style): BigModel
    {
        return new BigModel($style);
    }

    /**
     * 智普生成视频
     *
     * @return \App\Packages\DrawImage\Drivers\BigModelVideo
     */
    public static function BigModelVideo(): BigModelVideo
    {
        return new BigModelVideo();
    }

    /**
     * @param  \App\Models\DrawImageStyle  $style
     * @return \App\Packages\DrawImage\Drivers\DouBao
     */
    public static function DouBao(DrawImageStyle $style): DouBao
    {
        return new DouBao($style);
    }
}