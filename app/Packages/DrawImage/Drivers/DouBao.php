<?php

namespace App\Packages\DrawImage\Drivers;

use App\Models\DrawImage;
use App\Models\DrawImageSize;
use App\Models\DrawImageStyle;
use App\Packages\DrawImage\BaseDriver;
use App\Packages\DrawImage\ContentResponse;
use Exception;
use Volc\Service\Visual;

class DouBao extends BaseDriver
{
    protected DrawImageSize $size;
    protected Visual        $visual;
    private                 $api_key = "ec0976fa7cc33b9656f6f40e6fbbf3ba.3qYzkJwahyVFoFmz";
    private                 $url     = "https://visual.volcengineapi.com";
    private array           $query   = [];
    private array           $header  = [];

    public function __construct(protected DrawImageStyle $style)
    {
        $this->visual = Visual::getInstance('cn-north-1');
        $this->visual->setAccessKey(env('DOUBAO_APPID', ''));
        $this->visual->setSecretKey(env('DOUBAO_SECRET', ''));
    }

    public function setSize(DrawImageSize $size): self
    {
        $this->size = $size;
        return $this;
    }

    public function draw(DrawImage $drawImage)
    {
        $prompt = $drawImage->prompt;
        if ($this->style->params['prompt'] ?? '') {
            $prompt = $this->style->params['prompt'].','.$prompt;
        }
        $data = [
            'prompt' => $prompt,
            'width'  => (int) $this->size->width,
            'height' => (int) $this->size->height,
        ];
        if ($drawImage->image_url) {
            $data['image_urls'] = [
                $drawImage->image_url,
            ];
        }
        
        $params = array_merge($this->style->params, $data);
        $result = match ($this->style->model_type) {
            DrawImageStyle::DOUBAO_TEXT, DrawImageStyle::DOUBAO_TEXT_IMAGE => $this->visual->CVProcess(['json' => $params]),
            DrawImageStyle::DOUBAO_IMAGE => $this->visual->Img2ImgXLSft(['json' => $params]),
            default => throw new Exception('没有匹配模型'),
        };
        return new ContentResponse($result);
    }

    public function query(array $params)
    {
    }
}