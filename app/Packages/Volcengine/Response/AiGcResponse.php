<?php

namespace App\Packages\Volcengine\Response;

use Illuminate\Support\Str;
use Psr\Http\Message\ResponseInterface;

class AiGcResponse
{
    protected bool   $success;
    protected string $message;
    protected array  $data;
    protected array  $result;
    protected array  $metaData = [];

    public function __construct(ResponseInterface|string $response)
    {
        if ($response instanceof ResponseInterface) {
            if ($response->getStatusCode() == 200) {
                $this->result = json_decode($response->getBody()->getContents(), true);
                $this->checkJson();
            } else {
                $this->success = false;
                $this->message = $response->getBody()->getContents();
            }
        } else {
            if (Str::isJson($response)) {
                $this->result = json_decode($response, true);
                $this->checkJson();
            } else {
                $this->success = false;
                $this->message = $response;
            }
        }
    }

    protected function checkJson()
    {
        $this->metaData = $this->result['ResponseMetadata'];
        $this->result   = $this->result['Result'];
        if ($this->result['code'] === 10000) {
            $this->success = true;
            $this->data    = $this->result['data'];
        } else {
            $this->success = false;
            $this->message = $this->result['message'];
        }
    }

    /**
     * @param  string  $message
     * @return \App\Packages\Volcengine\Response\AiGcResponse
     */
    public static function error(string $message): AiGcResponse
    {
        return new static($message);
    }

    public function isError(): bool
    {
        return ! $this->isSuccess();
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function toArray(): array
    {
        return $this->data;
    }

    public function __get(string $name)
    {
        return $this->data[$name] ?? null;
    }
}