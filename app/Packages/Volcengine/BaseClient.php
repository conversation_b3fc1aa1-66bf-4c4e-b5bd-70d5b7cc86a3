<?php

namespace App\Packages\Volcengine;

use App\Models\SystemConfig;
use App\Packages\Volcengine\Response\VolcengineResponse;
use App\Packages\Volcengine\Response\VolcengineStreamResponse;
use Exception;
use GuzzleHttp\Client;

class BaseClient
{
    protected string $apiKey;
    protected string $baseUrl = 'https://ark.cn-beijing.volces.com';
    protected Client $client;

    public function __construct()
    {
        $this->apiKey = SystemConfig::getValue('DouBaoAPIKEY');
        $this->client = new Client([
            'base_uri' => $this->baseUrl,
        ]);
    }

    protected function stream(array $params
    ): VolcengineStreamResponse|VolcengineResponse {
        return $this->request('POST', 'api/v3/chat/completions', $params,
            'json', true);
    }

    private function request(
        string $method,
        string $path,
        array $params = [],
        string $patamsType = 'form-param',
        bool $stream = false,
    ) {
        try {
            $response = $this->client->request($method, $path, [
                'headers'   => $this->getHeaders(),
                $patamsType => $params,
                'stream'    => $stream,
            ]);
            if ($stream) {
                return new VolcengineStreamResponse($response, $params);
            } else {
                return new VolcengineResponse($response, $params);
            }
        } catch (Exception $e) {
            return VolcengineResponse::error($e->getMessage(), $params);
        }
    }

    private function getHeaders(): array
    {
        return [
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer '.$this->apiKey,
        ];
    }
}