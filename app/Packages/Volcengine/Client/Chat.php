<?php

namespace App\Packages\Volcengine\Client;

use App\Models\SystemConfig;
use App\Packages\Volcengine\BaseClient;
use App\Packages\Volcengine\Response\VolcengineStreamResponse;

class Chat extends BaseClient
{
    public function sendMessage(string $message, array $images = []): VolcengineStreamResponse
    {
        $model   = SystemConfig::getValue('DouBaoCompletions');
        $message = [
            [
                'type' => 'text',
                'text' => $message,
            ]
        ];
        
        $images = array_filter($images);
        if (count($images) > 0) {
            foreach ($images as $image) {
                $message[] = [
                    'type'      => 'image_url',
                    'image_url' => [
                        'url' => $image,
                    ],
                ];
            }
        }
        $content = [
            [
                'role'    => 'user',
                'content' => $message,
            ]
        ];
        $params  = [
            'model'       => $model,
            'messages'    => $content,
            'stream'      => true,
            'max_tokens'  => 4096,
            'temperature' => 0.3,
        ];
        return $this->stream($params);
    }
}