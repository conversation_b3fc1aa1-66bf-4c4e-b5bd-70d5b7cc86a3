<?php

namespace App\Packages\Volcengine\Client;

use App\Packages\Volcengine\Request\BaseRequest;
use App\Packages\Volcengine\Response\AiGcResponse;
use App\Packages\Volcengine\VisionBase;

class Vision extends VisionBase
{
    public function AIGCFaceSwap(string $faceImage, string $modalImage)
    {
        $request = new BaseRequest();
        $request->setAction('AIGCFaceSwapV1')
            ->setVersion('2024-06-06')
            ->setParams([
                'req'        => 'aigc_face_swap_v1',
                'image_urls' => [
                    $faceImage,
                    $modalImage
                ],
                'return_url' => true,
            ]);
        return $this->run($request);
    }

    public function Img2ImgOutpainting(array $image, array $padding)
    {
        $request = new BaseRequest();
        $request->setAction('Img2ImgOutpainting')
            ->setVersion('2024-06-06')
            ->setParams([
                'req_key'       => 'i2i_outpainting',
                'custom_prompt' => <<<EOT
                            ### 智能比例扩图功能说明  
                            
                            依据原图比例智能拓展画面。通过深度分析原图的色彩体系、纹理细节及构图逻辑，使拓展区域与原图边界自然融合：  
                            - **内容延续**：新增部分提取原图元素特征，如景物、线条、光影等，确保主题风格一致；  
                            - **过渡自然**：边缘采用渐进式融合算法，避免色彩断层或构图突兀，实现从原图到拓展区域的平滑衔接；  
                            - **整体协调**：基于比例动态调整拓展范围，保持画面平衡，最终生成浑然一体的扩图效果，仿佛原生内容自然延伸。
                            EOT,
                'image_urls'    => $image,
                'left'          => $padding[0],
                'top'           => $padding[1],
                'right'         => $padding[2],
                'bottom'        => $padding[3],
                'return_url'    => true,
                'seed'          => -1,
            ]);
        return $this->run($request);
    }

    public function AIGCStylizeImage(array $image, array $style)
    {
        $request = new BaseRequest();
        $request->setAction('AIGCStylizeImage')
            ->setVersion('2024-06-06')
            ->setParams(array_filter([
                'image_urls'  => $image,
                'req_key'     => $style['req_key'],
                'sub_req_key' => $style['sub_req_key'],
                'return_url'  => true,
            ]));
        return $this->run($request);
    }

    /**
     * AI 商品
     *
     * @param  array  $params
     * @return \App\Packages\Volcengine\Response\AiGcResponse
     */
    public function Img2imgECommerceStyle(array $params)
    {
        $request = new BaseRequest();
        $request->setAction('Img2imgECommerceStyle')
            ->setVersion('2024-06-06')
            ->setParams($params);
        return $this->run($request);
    }

    /**
     * Notes: 画质增强
     *
     * @Author: 玄尘
     * @Date: 2025/6/5 09:48
     * @param  array  $inputs
     * @param  array  $params
     * @return \App\Packages\Volcengine\Response\AiGcResponse
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function AILensLqirImage(array $inputs, array $params)
    {
        $request = new BaseRequest();
        $request->setAction('LensLqir')
            ->setVersion('2024-06-06')
            ->setParams([
                'image_urls'          => [
                    $inputs[0]['url'],
                ],
                'req_key'             => 'lens_lqir',
                'resolution_boundary' => $params['resolution_boundary'],
                'jpg_quality'         => $params['jpg_quality'],
            ]);

        return $this->run($request);
    }

    /**
     * Notes: 老照片修复
     *
     * @Author: 玄尘
     * @Date: 2025/6/5 14:19
     * @param  array  $inputs
     * @param  array  $params
     * @return \App\Packages\Volcengine\Response\AiGcResponse
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function AILensOprImage(array $inputs, array $params): AiGcResponse
    {
        $request = new BaseRequest();
        $request->setAction('LensOpr')
            ->setVersion('2024-06-06')
            ->setParams([
                'image_urls' => [
                    $inputs[0]['url'],
                ],
                'req_key'    => 'lens_opr',
                'if_color'   => 1,
            ]);

        return $this->run($request);
    }

    public function __call(string $name, array $arguments)
    {
        $request = new BaseRequest();
        $request->setAction($name)
            ->setVersion($arguments[0])
            ->setParams($arguments[1]);
        return $this->run($request);
    }

}