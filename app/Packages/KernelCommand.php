<?php

namespace App\Packages;

use App\Exceptions\ParamsException;
use App\Exceptions\ValidatorException;
use App\Jobs\User\AutoSign;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Modules\User\Models\UserSignLog;
use Stringable;
use Tinywan\Jwt\Exception\JwtTokenException;
use Tinywan\Jwt\JwtToken;

class KernelCommand implements Stringable
{
    protected array $jwtToken = [];
    protected array $body;
    protected array $data     = [];
    private string  $appKey;
    private string  $iv;

    public function __construct(
        protected Request $request,
        protected bool $debug = false
    ) {
        $this->checkJwtToken();
        $this->setParams();
        $this->checkSignAuto();
        $this->iv = env('APP_KEY_IV', '');
    }

    /**
     * 解析头部jwt 获取key
     *
     * @return void
     * @throws \App\Exceptions\JwtException
     */
    protected function checkJwtToken(): void
    {
        $token        = $this->request->header('authorization', '');
        $this->appKey = env('APP_DEFAULT_KEY', '');
        if (! blank($token)) {
            if (Str::startsWith($token, 'Bearer ') && Str::length($token) > 7) {
                try {
                    $this->jwtToken = JwtToken::getExtend();
                    $this->appKey   = $this->jwtToken['key'] ?? env('APP_DEFAULT_KEY',
                        '');
                } catch (Exception $exception) {
                }
            } else {
                $this->appKey = env('APP_DEFAULT_KEY', '');
            }
        }
    }

    /**
     * 解析入参
     *
     * @return void
     * @throws \App\Exceptions\ParamsException
     */
    protected function setParams(): void
    {
        try {
            $data = $this->decrypt($this->request->post('Request'));
        } catch (Exception $exception) {
            throw new ValidatorException('解密错误');
        }
        if ($data) {
            $this->body = json_decode($data, true)['RequestBody'];
            if (is_string($this->body['DA'])) {
                $this->data = json_decode($this->body['DA'] ?: '{}', true);
            } elseif (is_array($this->body['DA'])) {
                $this->data = $this->body['DA'];
            }
        } else {
            throw new ParamsException('数据解密错误');
        }
    }

    /**
     * @param  string  $string
     * @return string|false
     */
    protected function decrypt(string $string): string|false
    {
        return openssl_decrypt(base64_decode($string), 'AES-256-ECB', $this->appKey,
            OPENSSL_RAW_DATA);
    }

    public function checkSignAuto()
    {
        if (filled($this->jwtToken['id'] ?? '') && UserSignLog::query()->ofUser($this->jwtToken['id'])->ofDay(now())
                ->doesntExist()) {
            $user = $this->user();
            AutoSign::dispatch($user);
        }
    }

    /**
     * 获取授权用户Model
     *
     * @return \App\Models\User|null
     */
    public function user(): User|null
    {
        $user = User::find($this->id());
        if (! $user) {
            throw new JwtTokenException('登录状态过期或用户不存在');
        }
        return $user;
    }

    /**
     * 获取授权用户id
     *
     * @return string
     */
    public function id(): string
    {
        return $this->jwtToken['id'] ?? '';
    }

    /**
     * 输出入参参数
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->data;
    }

    /**
     * 强制判断JWT
     *
     * @return void
     */
    public function jwtEx()
    {
        try {
            $this->jwtToken = JwtToken::getExtend();
            $this->appKey   = $this->jwtToken['key'] ?? env('APP_DEFAULT_KEY',
                '');
            if (! $this->jwtToken['id']) {
                throw new JwtTokenException('登录状态过期');
            }
        } catch (Exception $exception) {
            throw new JwtTokenException($exception->getMessage());
        }
    }

    /**
     * Retrieves the guest user information based on the current user ID.
     *
     * This method is used to get the user information of a guest from the database.
     * It assumes that there is a valid user model and method to query the database by user ID.
     * The specific user information returned depends on the implementation of the User model and the data in the database.
     *
     * @return User|null The guest user object if found, otherwise null.
     */
    public function guestUser()
    {
        return User::find($this->id());
    }

    /**
     * 开启dubug模式
     *
     * @return void
     */
    public function debug()
    {
        $this->debug = true;
    }

    public function validate(array $rules, array $messages = [])
    {
        $validator = Validator::make($this->data, $rules, $messages);
        if ($validator->fails()) {
            throw new ValidatorException($validator->errors()->first(), 400,
                $this->appKey);
        }
    }

    public function __toString(): string
    {
        return json_encode([
            'jwtToken' => $this->jwtToken,
            'params'   => $this->params,
            'appKey'   => $this->appKey,
            'iv'       => $this->iv,
            'sign'     => $this->sign,
        ], JSON_UNESCAPED_UNICODE);
    }

    public function toCollect(): Collection
    {
        return collect($this->data);
    }

    /**
     * 获取jwtToken数组内容
     *
     * @return array
     */
    public function getJwtToken(): array
    {
        return $this->jwtToken;
    }

    public function setKey(string $key): self
    {
        $this->appKey = $key;
        return $this;
    }

    public function setIV(string $iv): self
    {
        $this->iv = $iv;
        return $this;
    }

    public function getBody(string $key = ''): mixed
    {
        if (blank($key)) {
            return $this->body;
        }
        return $this->body[$key] ?? null;
    }

    public function success(
        mixed $data = '',
        string $message = 'SUCCESS'
    ): JsonResponse {
        return $this->respond(
            data: $data,
            code: 0,
            message: $message
        );
    }

    protected function respond(
        mixed $data = '',
        int $code = 200,
        string $message = '',
        array $header = []
    ): JsonResponse {
        $response = [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
        if (! $this->debug) {
            $response['RD'] = $this->encrypt(json_encode($response,
                JSON_INVALID_UTF8_IGNORE));
            unset($response['data']);
        }
        return Response::json($response, 200, $header);
    }

    /**
     *
     * @param  string  $string  需要加密的字符串
     * @return string
     */
    protected function encrypt(string $string): string
    {
        // openssl_encrypt
        return base64_encode(openssl_encrypt($string, 'AES-256-ECB',
            $this->appKey, OPENSSL_RAW_DATA));
    }

    public function error(
        string $message = '',
        int $code = 500
    ): JsonResponse {
        return $this->respond(
            code: $code,
            message: $message
        );
    }

    public function getConfigFormJsonFile(string $type)
    {
        $filePath = base_path("public/ai_style/".$type.".json");
        if (! file_exists($filePath)) {
            throw new ValidatorException('配置文件不存在');
        }
        $file = fopen($filePath, 'r');
        return json_decode(fread($file, filesize($filePath)), true);
    }

    public function sseError(string $message)
    {
        echo 'data: '.json_encode([
                'type' => 'error',
                'msg'  => $message,
                'data' => '',
            ], JSON_UNESCAPED_UNICODE)."\n\n";
        ob_flush();
        flush();
        usleep(2000);
    }

    public function sseEcho(string $message, string $type = 'msg', array $data = [])
    {
        echo 'data: '.json_encode([
                'type' => $type,
                'msg'  => $message,
                'data' => $data,
            ], JSON_UNESCAPED_UNICODE)."\n\n";
        ob_flush();
        flush();
        usleep(2000);
    }
}