<?php

namespace App\Packages\ImPush\User;

use App\Models\ImUser;
use App\Models\User;
use App\Packages\ImPush\BaseClient;

class Client extends BaseClient
{
    public string $userIdPrefix = 'w';

    /**
     * Notes: 导入用户
     *
     * @Author: 玄尘
     * @Date: 2025/2/18 14:35
     */
    public function importUser(User $user)
    {
        try {
            $url = '/v4/im_open_login_svc/account_import';

            $this->setUrl($url);

            $this->params = [
                'UserID'  => $this->userIdPrefix.$user->getKey(),
                'Nick'    => $user->info->nickname,
                'FaceUrl' => $user->info->avatar_url ?? 'http://www.qq.com',
            ];

            $res = $this->request($this->url, 'POST');
            ImUser::create([
                'user_id'    => $user->getKey(),
                'im_user_id' => $this->params['UserID'],
            ]);
            return $res;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * Notes: 批量导入用户
     *
     * @Author: 玄尘
     * @Date: 2025/2/19 10:18
     * @param $users
     * @return array
     * @throws \Exception
     */
    public function importUsers($users)
    {
        try {
            $url = '/v4/im_open_login_svc/multiaccount_import';

            $this->setUrl($url);
            $accountList = [];
            foreach ($users as $user) {
                $accountList[] = [
                    'UserID'  => $this->userIdPrefix.$user->getKey(),
                    'Nick'    => $user->info->nickname,
                    'FaceUrl' => $user->info->avatar_url ?? 'http://www.qq.com',
                ];
            }

            $accounts = [
                'AccountList' => $accountList
            ];

            $this->params = $accounts;
            $res          = $this->request($this->url, 'POST');
            foreach ($users as $user) {
                ImUser::updateOrCreate([
                    'user_id' => $user->getKey(),
                ], [
                    'im_user_id' => $this->userIdPrefix.$user->getKey(),
                ]);
            }
            return $res;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * Notes: 检查用户
     *
     * @Author: 玄尘
     * @Date: 2025/2/19 10:18
     * @param  array  $userIds
     * @return array
     * @throws \Exception
     */
    public function checkUser(array $userIds)
    {
        try {
            $url = '/v4/im_open_login_svc/account_check';

            $this->setUrl($url);

            $this->params = [
                'CheckItem' => $userIds
            ];

            return $this->request($this->url, 'POST');
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * Notes: 删除用户
     *
     * @Author: 玄尘
     * @Date: 2025/2/20 08:52
     * @param $im_user_id
     * @return array
     * @throws \Exception
     */
    public function delUser($im_user_ids)
    {
        try {
            $url = '/v4/im_open_login_svc/account_delete';
            $this->setUrl($url);
            dd($this);
            $deleteItem = [];
            if (is_array($im_user_ids)) {
                $deleteItem = collect($im_user_ids)
                    ->map(function ($item) {
                        return [
                            'UserID' => $item
                        ];
                    })->toArray();
            } else {
                $deleteItem = [
                    [
                        'UserID' => $im_user_ids
                    ]
                ];
            }
            $this->params = [
                'DeleteItem' => $deleteItem
            ];

            return $this->request($this->url, 'POST');
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

}