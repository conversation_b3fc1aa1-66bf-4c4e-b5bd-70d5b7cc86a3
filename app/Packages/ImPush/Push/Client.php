<?php

namespace App\Packages\ImPush\Push;

use App\Packages\ImPush\BaseClient;

class Client extends BaseClient
{

    /**
     * Notes: 全部发送
     *
     * @Author: 玄尘
     * @Date: 2025/2/18 14:13
     * @param  string  $content
     * @param  string  $title
     * @param  int  $pushFlag
     * @return array
     * @throws \Exception
     */
    public function push(string $content, string $title = '', int $pushFlag = 0): array
    {
        try {
            $url = '/v4/timpush/push';

            $this->setUrl($url);

            $this->params = [
                'From_Account'    => $this->identifier,
                'MsgRandom'       => $this->generateRandom(),
                'OfflinePushInfo' => [
                    'PushFlag' => $pushFlag,  // 0表示进行离线推送
                    'Title'    => $title ?: '新消息提醒',
                    'Desc'     => $content
                ]
            ];
            return $this->request($this->url, 'POST');
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * Notes: 局部发送
     *
     * @Author: 玄尘
     * @Date: 2025/2/18 14:12
     * @param  array  $userIds  接收用户ids
     * @param  string  $content  内容
     * @param  string  $title  标题
     * @param  int  $pushFlag  0表示进行离线推送 1 表示不离线推送
     * @return array
     * @throws \Exception
     */
    public function batchPush(array $userIds, string $content, string $title = '', int $pushFlag = 0)
    {
        try {
            $url = '/v4/timpush/batch';

            $this->setUrl($url);

            $this->params = [
                'From_Account'    => $this->identifier,
                'To_Account'      => $userIds,
                'MsgRandom'       => $this->generateRandom(),
                'OfflinePushInfo' => [
                    'PushFlag' => $pushFlag,  // 0表示进行离线推送
                    'Title'    => $title ?: '新消息提醒',
                    'Desc'     => $content
                ]
            ];

            return $this->request($this->url, 'POST');
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

}