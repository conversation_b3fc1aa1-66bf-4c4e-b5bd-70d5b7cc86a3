<?php

namespace App\Packages\Mj;

use App\Models\SystemConfig;
use App\Packages\Mj\Helper\QiniuUpload;
use App\Packages\Mj\Helper\UploadKey;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Str;

class BaseClient
{
    protected       $soft_id     = 'mj123_web';
    protected       $ekey        = '';
    protected array $files       = [];
    protected array $uploadFiles = [];
    private string  $baseUrl;
    private string  $basePath;
    private string  $token;
    private array   $pathQuery   = [];
    private bool    $stream      = false;
    private bool    $debug       = false;

    public function __construct()
    {
        $this->baseUrl  = SystemConfig::getValue('MjApiUrl');
        $this->basePath = SystemConfig::getValue('MjPath');
        $this->token    = SystemConfig::getValue('MjToken');
        $this->ekey     = Str::random();
    }

    protected function debug(): self
    {
        $this->debug = true;
        return $this;
    }

    protected function setPathQuery(array $pathQuery): self
    {
        $this->pathQuery = $pathQuery;
        return $this;
    }

    protected function setFiles(array $files, string $channel = 'alioss'): self
    {
        $this->files = $files;
        if (count($files) > 0) {
            $uploadKey = match ($channel) {
                'qiniu' => new QiniuUpload(),
                default => new UploadKey(),
            };
            foreach ($files as $file) {
                $this->uploadFiles[$file['key']] = $uploadKey->upload($file['url'],
                    $file['scene']);
            }
        }
        return $this;
    }

    protected function get(string $path, array $params = []): MjResponse
    {
        return $this->request($path, $params);
    }

    public function request(
        string $path,
        array $params = [],
        string $method = 'GET',
        string $format = 'query',
    ): MjResponse|MjStreamResponse {
        $client = new Client([
            'base_uri' => $this->baseUrl,
        ]);
        $query  = [
            'soft_id' => $this->soft_id,
            'ekey'    => $this->ekey,
        ];
        if (count($this->uploadFiles) > 0) {
            $params = array_merge($params, $this->uploadFiles);
        }
        if ($format == 'query') {
            $params = array_merge($params, $query);
        } else {
            $path .= '?'.http_build_query(array_merge($query, $this->pathQuery));
        }
        try {
            $headers = $this->stream ? $this->getStreamHeaders() : $this->getHeaders();
            if ($this->debug) {
                dd($headers, $this->files, $this->uploadFiles, $params, $this->basePath.'/'.$path);
            }
            $response = $client->request($method, $this->basePath.'/'.$path, [
                'headers' => $headers,
                'verify'  => false,
                $format   => $params,
            ]);
            return $this->stream ? new MjStreamResponse($response, $params) : new MjResponse($response, $params);
        } catch (Exception $e) {
            return MjResponse::error($e->getMessage(), $params);
        }
    }

    protected function getStreamHeaders(): array
    {
        return [
            'authorization' => $this->token,
        ];
    }

    protected function getHeaders()
    {
        return [
            'origin'        => 'https://www.midjourny.cn/',
            'referer'       => 'https://www.midjourny.cn/',
            'authorization' => $this->token,
            'Content-Type'  => 'application/json;charset=utf-8',
            'User-Agent'    => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 QuarkPC/1.10.5.187'
        ];
    }

    protected function delete(string $path, array $params = []): MjResponse
    {
        return $this->request($path, $params, 'DELETE');
    }

    protected function setUrl(string $baseUrl): self
    {
        $this->baseUrl = $baseUrl;
        return $this;
    }

    protected function setPath(string $basePath): self
    {
        $this->basePath = $basePath;
        return $this;
    }

    protected function post(string $path, array $params = []): MjResponse
    {
        return $this->request($path, $params, 'POST', 'form_params');
    }

    protected function json(string $path, array $params = []): MjResponse
    {
        return $this->request($path, $params, 'POST', 'json');
    }

    protected function stream($path, array $params = []): MJStreamResponse
    {
        $this->setStream();
        return $this->request($path, $params, 'POST', 'json');
    }

    private function setStream()
    {
        $this->stream = true;
    }
}