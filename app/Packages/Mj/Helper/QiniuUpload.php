<?php

namespace App\Packages\Mj\Helper;

use App\Packages\Mj\BaseClient;
use App\Packages\Mj\GuzzleCustomization\MultipartStream;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Str;

class QiniuUpload extends BaseClient
{
    protected array $config  = [];
    private string  $baseUrl = 'https://up-z2.qiniup.com';

    public function upload(string|array $url, string $scene): string|array
    {
        if (is_array($url)) {
            $urlRes = [];
            foreach ($url as $imagePath) {
                $config   = $this->getConfig();
                $fix      = Str::afterLast($imagePath, '.');
                $urlRes[] = $this->doUpload($imagePath, $fix, $config);
            }
        } else {
            $config = $this->getConfig($scene);
            $fix    = Str::afterLast($url, '.');
            $urlRes = $this->doUpload($url, $fix, $config);
        }
        return $urlRes;
    }

    protected function getConfig(): array
    {
        $url      = 'files/getQUploadToken';
        $response = $this->get($url);
        if ($response->isSuccess()) {
            return $response->toArray();
        } else {
            throw new Exception($response->getMessage());
        }
    }

    protected function doUpload(
        string $path,
        string $fix,
        array $config = []
    ): string {
        $path = getOssPrivateUrl($path);
        $name = Str::uuid()->toString().".".$fix;
        $key  = 'ref_imgs/'.now()->toDateString().'/'.$name;
        try {
            $boundary  = '----WebKitFormBoundary'.Str::random(); //分割符号
            $multipart = [
                [
                    'name'     => 'file',
                    'filename' => $path,
                    'contents' => file_get_contents($path),
                ],
                [
                    'name'     => 'token',
                    'contents' => $config['token'],
                ],
                [
                    'name'     => 'key',
                    'contents' => $key,
                ],
                [
                    'name'     => 'fname',
                    'contents' => $path,
                ],
            ];
            $body      = new MultipartStream($multipart, $boundary);

            $request = new Request('POST', $this->baseUrl, [
                'Accept'       => '*/*',
                'Connection'   => 'keep-alive',
                'Content-Type' => 'multipart/form-data; boundary='.$boundary
            ], $body);

            $client   = new Client([
                'verify' => false
            ]);
            $response = $client->sendRequest($request);
            if ($response->getStatusCode() == 200) {
                return $key;
            } else {
                throw new Exception($response->getBody()->getContents());
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}