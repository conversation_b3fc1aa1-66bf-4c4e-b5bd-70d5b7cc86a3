<?php

namespace App\Packages\Mj\Helper;

use App\Packages\Mj\BaseClient;
use App\Packages\Mj\GuzzleCustomization\MultipartStream;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Str;

class UploadKey extends BaseClient
{
    protected array $config = [];

    public function upload(string|array $url, string $scene): string
    {
        if (is_array($url)) {
            $urlArray = [];
            foreach ($url as $imagePath) {
                $config     = $this->getConfig($scene);
                $fix        = Str::afterLast($imagePath, '.');
                $urlArray[] = $this->doUpload($imagePath, $fix, $config['config']);
            }
            $url = implode('|', $urlArray);
        } else {
            $config = $this->getConfig($scene);
            $fix    = Str::afterLast($url, '.');
            $url    = $this->doUpload($url, $fix, $config['config']);
        }
        return $url;
    }

    protected function getConfig(string $scene, int $keyType = 1): array
    {
        $url      = 'files/getAliUploadToken';
        $response = $this->get($url, [
            'scene'    => $scene,
            'key_type' => $keyType,
        ]);
        if ($response->isSuccess()) {
            return $response->toArray();
        } else {
            throw new Exception($response->getMessage());
        }
    }

    protected function doUpload(
        string $path,
        string $fix,
        array $config = []
    ): string {
        $path = getOssPrivateUrl($path);
        $name = rand(10000000, 99999999).".".$fix;
        try {
            $boundary  = '----WebKitFormBoundary'.Str::random(); //分割符号
            $multipart = [
                [
                    'name'     => 'name',
                    'contents' => $name,
                ],
                [
                    'name'     => 'policy',
                    'contents' => $config['policy'],
                ],
                [
                    'name'     => 'OSSAccessKeyId',
                    'contents' => $config['accessid'],
                ],
                [
                    'name'     => 'success_action_status',
                    'contents' => '200',
                ],
                [
                    'name'     => 'signature',
                    'contents' => $config['signature'],
                ],
                [
                    'name'     => 'key',
                    'contents' => $config['dir']."/".$name,
                ],
                [
                    'name'     => 'file',
                    'filename' => $path,
                    'contents' => file_get_contents($path),
                ],
            ];
            $body      = new MultipartStream($multipart, $boundary);

            $request = new Request('POST', $config['host'], [
                'Accept'       => '*/*',
                'Connection'   => 'keep-alive',
                'Content-Type' => 'multipart/form-data; boundary='.$boundary
            ], $body);

            $client   = new Client([
                'verify' => false
            ]);
            $response = $client->sendRequest($request);
            if ($response->getStatusCode() == 200) {
                return $config['dir']."/".$name;
            } else {
                throw new Exception($response->getBody()->getContents());
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}