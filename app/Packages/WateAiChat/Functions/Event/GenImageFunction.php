<?php

namespace App\Packages\WateAiChat\Functions\Event;

use App\Jobs\Plugin\JmDrawJob;
use App\Models\PluginJmDraw;
use App\Packages\WateAiChat\Functions\BaseFunction;
use App\Packages\WateAiChat\Functions\FunctionInterface;

class GenImageFunction extends BaseFunction implements FunctionInterface
{
    public string         $key      = 'gen_image';
    public string         $name     = '图片生成';
    protected array       $params   = [];
    protected string|null $asset_no = null;

    public function getTaskParams(): array
    {
        return [
            'task'  => array_merge($this->params, [
                'function_name' => $this->key,
            ]),
            'asset' => $this->asset_no,
        ];
    }

    public function loadParams(): array
    {
        return [
            'type'     => 'function',
            'function' => [
                'name'        => $this->key,
                'description' => <<<EOT
                                当用户有明确的制作图片意图时调用此函数,要排除用户对当前图片的提问，对话中要包含类似生成、制作、绘制等明确字样。之前的对话不触发此内容。
                                1、用户对图片有描述时使用用户描述prompt，描述简单进行优化。
                                2、用户没有描述AI帮助生成。
                                3、最终产生的描述控制在100-150个字。
                                4、如果用户给图片了，请传参 image 传入yes。
                                EOT,
                'parameters'  => [
                    'type'       => 'object',
                    'properties' => [
                        'prompt' => [
                            'type'        => 'string',
                            'description' => '用户对图片的描述或者AI帮助生成的图片描述',
                        ],
                        'image'  => [
                            'type'        => 'string',
                            'description' => '如果有对应的图片,请传入yes',
                        ]
                    ],
                ]
            ]
        ];
    }

    public function getParams(): array
    {
        return $this->params;
    }

    public function doFunction(string $json): mixed
    {
        //        $this->client->sendText('正在生成图片，请稍等...');
        preg_match_all('/\{.*?\}/s', $json, $matches);
        $last_json = end($matches[0]);
        $params    = json_decode($last_json, true);
        if (! is_array($params)) {
            return false;
        }
        $this->params = $params;
        $prompt       = $params['prompt'] ?? '';
        $image        = $params['image'] ?? '';
        if (blank($prompt)) {
            $this->client->error('参数错误');
            return false;
        }
        $type    = PluginJmDraw::TYPE_NORMAL_IMAGE;
        $fileUrl = '';
        if ($image == 'yes') {
            $type    = PluginJmDraw::TYPE_IMAGE_IMAGE;
            $fileUrl = $this->client->imageUrl[0];
        }
        $params = [
            'user_id' => $this->client->user->id,
            'prompt'  => $prompt,
            'type'    => $type,
            'inputs'  => trim($image) !== 'yes' ? [] : [
                [
                    'url' => $fileUrl,
                ]
            ],
            'params'  => [
                'sample_strength' => 5,
                'ratio'           => '1:1',
                'width'           => 1024,
                'height'          => 1024,
            ],
        ];
        $log    = PluginJmDraw::create($params);
        JmDrawJob::dispatch($log);
        $log->refresh();
        $data           = $log->getAssetResource();
        $this->asset_no = $data['asset_no'] ?? '';
        $this->client->sendTask($data);
        return true;
    }
}