<?php

namespace App\Packages\WateAiChat\Traits;

use App\Packages\WateAiChat\Functions\FunctionLoad;
use App\Packages\WateAiChat\StreamResponse;
use Exception;

trait OpenAiTrait
{
    protected string $functionKey          = '';
    protected string $functionParamsString = '';

    public function getParams(
        string $message,
        string $system = '',
        array $images = [],
    ): array {
        $maxTokenKey = property_exists($this, 'maxTokenKey') ? $this->maxTokenKey : 'max_tokens';
        $data        = [
            'messages'       => $this->getGroupMessage(
                message: $message,
                images: $images,
            ),
            'model'          => $this->engine->name,
            $maxTokenKey     => (int) $this->engine->maxout,
            'stream'         => true,
            'stream_options' => [
                'include_usage' => true,
            ],
        ];
        return $data;
    }

    public function getGroupMessage(string $message, array $images = []): array
    {
        if ($this->multiEngine) {
            return $this->multiParams($message, $images);
        } else {
            return $this->textParams($message);
        }
    }

    protected function multiParams(string $message, array $images = [])
    {
        $data = [
            [
                'role'    => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $message,
                    ],
                ],
            ]
        ];

        if (count($images) > 0 && $this->multiEngine) {
            foreach ($images as $image) {
                $data[0]['content'][] = [
                    'type'      => 'image_url',
                    'image_url' => [
                        'url' => $this->getImgUrl($image),
                    ],
                ];
            }
        }

        $logs        = $this->group->logs()
            ->orderBy('created_at', 'desc')
            ->get();
        $totalTokens = 0;
        foreach ($logs as $log) {
            $totalTokens += $log->tokens;
            if ($totalTokens >= ($this->engine->maxlen - 1000)) {
                break;
            }
            if (! blank($log->response)) {
                array_unshift($data,
                    [
                        'role'    => 'assistant',
                        'content' => $log->response,
                    ]
                );
            }
            if ($log->asset) {
                array_unshift($data,
                    [
                        'role'    => 'assistant',
                        'content' => '已生成资源:'.$this->getImgUrl($log->asset->assetable->coverUrlAttr),
                    ]
                );
            }
            if ($log->asset || (count($log->images ?: []) > 0 && $this->multiEngine)) {
                array_unshift($data, [
                    'role'    => 'user',
                    'content' => array_merge([
                        [
                            'type' => 'text',
                            'text' => $log->message,
                        ],
                    ], collect($log->images)->map(function ($image) {
                        return [
                            'type'      => 'image_url',
                            'image_url' => [
                                'url' => $this->getImgUrl($image),
                            ],
                        ];
                    })->toArray(), $log->asset ? [
                        [
                            'type'      => 'image_url',
                            'image_url' => [
                                'url' => $this->getImgUrl($log->asset->assetable->coverUrlAttr),
                            ],
                        ]
                    ] : []),
                ]);
            } else {
                array_unshift($data, [
                    'role'    => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $log->message,
                        ]
                    ],
                ]);
            }
        }
        if (! blank($this->engine->system)) {
            array_unshift($data, [
                'role'    => 'system',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $this->engine->system,
                    ]
                ],
            ]);
        }
        return $data;
    }

    protected function getImgUrl(string $url)
    {
        return sprintf("%s?x-oss-process=image/resize,m_lfit,w_1024,h_1024/format,jpeg", $url);
    }

    protected function textParams(string $message)
    {
        $data = [
            [
                'role'    => 'user',
                'content' => $message,
            ]
        ];

        $logs        = $this->group->logs()
            ->orderBy('created_at', 'desc')
            ->select('message', 'response')
            ->get();
        $totalTokens = 0;
        foreach ($logs as $log) {
            $totalTokens += $log->tokens;
            if ($totalTokens >= ($this->engine->maxlen - 1000)) {
                break;
            }
            if (! blank($log->response)) {
                array_unshift($data, [
                    'role'    => 'assistant',
                    'content' => $log->response,
                ]);
            }

            array_unshift($data, [
                'role'    => 'user',
                'content' => $log->message,
            ]);
        }
        if (! blank($this->engine->system)) {
            array_unshift($data, [
                'role'    => 'system',
                'content' => $this->engine->system,
            ]);
        }
        return $data;
    }

    public function checkText(StreamResponse $response)
    {
        foreach ($response->getIterator() as $chunk) {
            if ($chunk == 'DONE') {
                if ($this->functionKey) {
                    $loads         = new FunctionLoad();
                    $functions     = $loads->getLoads();
                    $functionClass = $functions[$this->functionKey] ?? '';
                    if ($functionClass) {
                        try {
                            $this->function = new $functionClass();
                            $this->function
                                ->setClient($this)
                                ->doFunction($this->functionParamsString);
                        } catch (Exception $e) {
                            $this->error("\n\n".$e->getMessage()."\n\n");
                        }
                        continue;
                    }
                }
                break;
            }
            $delta         = $chunk['choices'][0]['delta'] ?? [];
            $toolsFunction = $delta['tool_calls'] ?? [];

            if (count($toolsFunction) > 0) {
                $callFunction = $toolsFunction[0]['function'] ?? [];
                if (count($callFunction) > 0) {
                    $name = $callFunction['name'] ?? '';
                    if ($name && blank($this->functionKey)) {
                        $this->functionKey = $name;
                    }
                    $arguments = $callFunction['arguments'] ?? '';
                    if ($arguments) {
                        $this->functionParamsString .= $arguments;
                    }
                    continue;
                }
            }

            $usage  = $chunk['usage'] ?? [];
            $tokens = $usage['total_tokens'] ?? 0;
            if ($tokens > 0) {
                $completion_tokens = $usage['completion_tokens'] ?? 0;
                $prompt_tokens     = $usage['prompt_tokens'] ?? 0;
                $response->setTokens($tokens, $prompt_tokens, $completion_tokens);
            }

            $reasoning = $delta['reasoning_content'] ?? null;
            if (! is_null($reasoning)) {
                $response->pushReasoning($reasoning);
                $this->sendReasoning($reasoning);
            }
            $message = $delta['content'] ?? null;
            if (! is_null($message)) {
                $response->pushMessage($message);
                $this->sendText($message);
            }
        }
    }

    protected function getImgBase64(string $url)
    {
        $url = $this->getImgUrl($url);
        return sprintf("data:image/jpeg;base64,%s", base64_encode(file_get_contents($url)));
    }

}