<?php

namespace App\Packages\WateAiChat;

use App\Models\AiChatConfig;
use App\Models\AiChatEngine;
use App\Models\ChatGroup;
use App\Models\User;
use App\Packages\WateAiChat\Functions\BaseFunction;
use App\Packages\WateAiChat\Tools\SuggestTool;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;

class BaseClient
{
    public string               $url          = '';
    public array                $headers      = [];
    public bool                 $multiEngine  = false;
    public User                 $user;
    public array                $imageUrl     = [];
    public bool                 $think;
    public bool                 $search;
    protected AiChatConfig      $config;
    protected AiChatEngine      $engine;
    protected ChatGroup         $group;
    protected BaseFunction|null $function     = null;
    protected array             $customParams = [];

    public function send(string $message, array $imageUrl = [], bool $think = false, bool $search = false)
    {
        $this->think  = $think;
        $this->search = $search;
        if (($this->group->hasImage() || count($imageUrl) > 0) && $this->config->multiEngine) {
            $this->engine      = $this->config->multiEngine;
            $this->multiEngine = true;
        } elseif (count($imageUrl) > 0) {
            $imageUrl = [];
        }
        if ($think && $this->config->thinkEngine && ! in_array($this->engine->name, [
                'qvq-plus-latest'
            ])) {
            $this->engine = $this->config->thinkEngine;
            if ($this->engine->name != $this->config->multiEngine?->name) {
                $imageUrl          = [];
                $this->multiEngine = false;
            }
        }
        $this->imageUrl = $imageUrl;
        $this->url      = $this->getSendUrl();
        $this->headers  = $this->getHeader();
        $params         = blank($this->customParams) ? $this->getParams($message, '', $imageUrl) : $this->customParams;
        if ($think && method_exists($this, 'setThinkParams')) {
            $params = $this->setThinkParams($params);
        }
        if ($search && method_exists($this, 'setSearchParams')) {
            $params = $this->setSearchParams($params);
        }
        try {
            $client   = new Client([
                'verify' => false,
                'stream' => true,
            ]);
            $response = $client->request('POST', $this->url, [
                'headers' => $this->headers,
                'json'    => $params,
            ]);

            $kernel = new StreamResponse($response,
                $params,
                $message,
                $imageUrl);
            $this->checkText($kernel);
            $log = $kernel->finish(
                group: $this->group,
                user: $this->user,
                channel: $this->config->key,
                engine: $this->engine->name,
                function: $this->function,
            );
            $this->sendItem([
                'id'           => $log->id,
                'group_id'     => $log->group_id,
                'channel'      => $log->channel,
                'total_tokens' => $log->tokens,
            ]);
            $suggestTools = new SuggestTool();
            $suggestTools->setCustParams($log)->sendTools();
        } catch (RequestException $exception) {
            $data   = json_decode($exception->getResponse()->getBody()->getContents(), true);
            $errMsg = $data['message'] ?? '';
            if (blank($errMsg)) {
                $errMsg = $data['msg'] ?? '';
            }
            if (blank($errMsg)) {
                $errMsg = $data['error']['message'] ?? '';
            }
            $this->error($errMsg);
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
        }
    }

    public function getSendUrl(): string
    {
        return Str::finish($this->engine->url, '/').'chat/completions';
    }

    public function getHeader(): array
    {
        return [
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer '.$this->config->token
        ];
    }

    protected function sendItem(array $data = [], string $message = '')
    {
        $this->out($message, 'item', $data);
    }

    public function out(string $message, string $type, array $data = [])
    {
        echo 'data: '.json_encode([
                'type' => $type,
                'msg'  => $message,
                'data' => $data ?: '',
            ], JSON_UNESCAPED_UNICODE)."\n\n";
        ob_flush();
        flush();
        usleep(20000);
    }

    public function error(string $message)
    {
        $this->out($message, 'error');
    }

    public function sendText(string $message)
    {
        $this->out($message, 'msg');
    }

    public function sendTask(array $task)
    {
        $this->out('', 'task', $task);
    }

    protected function sendSearch(array $data = [], string $message = '')
    {
        $this->out($message, 'search', $data);
    }

    protected function setCustomParams(array $params)
    {
        $this->customParams = $params;
        return $this;
    }

    protected function sendSuggest(array $suggest)
    {
        $this->out('', 'suggest', $suggest);
    }

    protected function sendReasoning(string $message)
    {
        $this->out($message, 'reasoning');
    }
}