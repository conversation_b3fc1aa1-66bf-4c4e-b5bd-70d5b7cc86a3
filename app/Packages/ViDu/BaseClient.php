<?php

namespace App\Packages\ViDu;

use App\Models\SystemConfig;
use Exception;
use GuzzleHttp\Client;
use Psr\Http\Message\ResponseInterface;

class BaseClient
{
    protected string $baseUrl;
    protected Client $httpClient;
    protected string $apiKey;
    protected array  $result;
    protected array  $params = [];

    public function __construct()
    {
        $this->baseUrl = SystemConfig::getValue('vidu_url');
        $this->apiKey  = SystemConfig::getValue('vidu_token');
        $this->initializeClient();
    }

    protected function initializeClient(): void
    {
        $this->checkBaseData();
        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'timeout'  => 30,
            'headers'  => [
                'Authorization' => 'Token '.$this->apiKey,
                'Accept'        => 'application/json',
                'Content-Type'  => 'application/json',
            ]
        ]);
    }

    public function checkBaseData(): void
    {
        if (empty($this->baseUrl)) {
            throw new ViDuException('请先配置Vidu接口地址');
        }
        if (empty($this->apiKey)) {
            throw new ViDuException('请先配置Vidu接口密钥');
        }
    }

    public function request(string $path, string $method): ViduResponse
    {
        try {
            $paramsName = $method === 'GET' || $method === 'DELETE' ? 'query' : 'json';

            $response = $this->httpClient->request($method, $path, [
                $paramsName => $this->params,
            ]);

            return new ViduResponse($response, $this->params);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    protected function handleException(Exception $e): ViduResponse
    {
        if (method_exists($e, 'getResponse')) {
            $response = $e->getResponse();
            if ($response instanceof ResponseInterface) {
                $result       = json_decode($response->getBody()->getContents(), true);
                $this->result = $result;
                $errorCode    = $result['reason'] ?? 'UNKNOWN_ERROR';
                return ViduResponse::error(ViDuException::getErrorMessage($errorCode), $this->params);
            }
        }

        return ViduResponse::error($e->getMessage(), $this->params);
    }

    protected function get(string $path): ViduResponse
    {
        return $this->request($path, 'GET');
    }

    protected function post(string $path): ViduResponse
    {
        return $this->request($path, 'POST');
    }

    protected function put(string $path): ViduResponse
    {
        return $this->request($path, 'PUT');
    }

    protected function delete(string $path): ViduResponse
    {
        return $this->request($path, 'DELETE');
    }
}