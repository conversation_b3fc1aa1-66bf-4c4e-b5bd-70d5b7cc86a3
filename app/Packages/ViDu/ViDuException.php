<?php

namespace App\Packages\ViDu;

class ViDuException extends \Exception
{
    // HTTP状态码
    public const HTTP_BAD_REQUEST           = 400;
    public const HTTP_UNAUTHORIZED          = 401;
    public const HTTP_FORBIDDEN             = 403;
    public const HTTP_NOT_FOUND             = 404;
    public const HTTP_CONFLICT              = 409;
    public const HTTP_TOO_MANY_REQUESTS     = 429;
    public const HTTP_INTERNAL_SERVER_ERROR = 500;
    public const HTTP_CLIENT_CANCELED       = 499;

    // 错误码
    public const BAD_REQUEST                   = 'BadRequest';
    public const FIELD_LACKING                 = 'FieldLacking';
    public const FIELD_UNWANTED                = 'FieldUnwanted';
    public const FIELD_ITEM_COUNT_OUT_OF_RANGE = 'FieldItemCountOutOfRange';
    public const PAGE_SIZE_OUT_OF_RANGE        = 'PageSizeOutOfRange';
    public const IMAGE_DOWNLOAD_FAILURE        = 'ImageDownloadFailure';
    public const OPERATION_IN_PROCESS          = 'OperationInProcess';
    public const TASK_PROMPT_POLICY_VIOLATION  = 'TaskPromptPolicyViolation';
    public const IMAGE_FORMAT_INVALID          = 'ImageFormatInvalid';
    public const AUDIT_SUBMIT_ILLEGAL          = 'AuditSubmitIllegal';
    public const CREDIT_INSUFFICIENT           = 'CreditInsufficient';
    public const CREATION_POLICY_VIOLATION     = 'CreationPolicyViolation';
    public const MODEL_UNAVAILABLE             = 'ModelUnavailable';
    public const USER_CANCELLED                = 'UserCancelled';
    public const UNAUTHORIZED                  = 'Unauthorized';
    public const FORBIDDEN                     = 'Forbidden';
    public const TASK_NOT_FOUND                = 'TaskNotFound';
    public const CREATION_NOT_FOUND            = 'CreationNotFound';
    public const CONFLICT                      = 'Conflict';
    public const QUOTA_EXCEEDED                = 'QuotaExceeded';
    public const TOO_MANY_REQUESTS             = 'TooManyRequests';
    public const SYSTEM_THROTTLING             = 'SystemThrottling';
    public const CANCELED                      = 'Canceled';
    public const INTERNAL_SERVICE_FAILURE      = 'InternalServiceFailure';
    public const UNKNOWN_ERROR                 = 'UnknownError';

    // 错误消息映射
    private static array $errorMessages = [
        self::BAD_REQUEST                   => '不合法的请求',
        self::FIELD_LACKING                 => '缺少字段: %s',
        self::FIELD_UNWANTED                => '不需要传某些字段: %s',
        self::FIELD_ITEM_COUNT_OUT_OF_RANGE => '字段超限制: %s',
        self::PAGE_SIZE_OUT_OF_RANGE        => '图像尺寸有问题。要求：图片大小需小于50M,格式只支持 jpg/jpeg/png/webp, 图片长宽比需要小于1:4或者4:1',
        self::IMAGE_DOWNLOAD_FAILURE        => '下载用户图片url失败，请检查链接的有效性',
        self::OPERATION_IN_PROCESS          => '请求在处理中',
        self::TASK_PROMPT_POLICY_VIOLATION  => 'Prompt 触发安审风控',
        self::IMAGE_FORMAT_INVALID          => '图像格式不符合要求',
        self::AUDIT_SUBMIT_ILLEGAL          => '输入没有通过安全审核',
        self::CREDIT_INSUFFICIENT           => '积分不足',
        self::CREATION_POLICY_VIOLATION     => '生成物触发风控',
        self::MODEL_UNAVAILABLE             => '请求的模型不可用，调用任务失败，请检查模型类型并重试',
        self::USER_CANCELLED                => '用户手动终止任务执行',
        self::UNAUTHORIZED                  => '未鉴权',
        self::FORBIDDEN                     => '请求没有权限',
        self::TASK_NOT_FOUND                => 'Task id 没找到',
        self::CREATION_NOT_FOUND            => 'Creation id 没找到',
        self::CONFLICT                      => '资源主键冲突',
        self::QUOTA_EXCEEDED                => '超过并发限制。请联系商务',
        self::TOO_MANY_REQUESTS             => '请求太频繁',
        self::SYSTEM_THROTTLING             => '资源超过限制',
        self::CANCELED                      => '请求被取消',
        self::INTERNAL_SERVICE_FAILURE      => '服务器内部错误，请稍后重试，或联系客服',
        self::UNKNOWN_ERROR                 => '未知错误'
    ];

    public function __construct(string $message = '', int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * 获取错误消息
     *
     * @param  string  $code  错误码
     * @param  array  $params  错误消息参数
     * @return string
     */
    public static function getErrorMessage(string $code, array $params = []): string
    {
        if (! isset(self::$errorMessages[$code])) {
            return self::$errorMessages[self::UNKNOWN_ERROR];
        }

        $message = self::$errorMessages[$code];
        if (! empty($params)) {
            try {
                $message = vsprintf($message, $params);
            } catch (\ValueError $e) {
                // 如果参数数量不匹配，返回原始消息
                return $message;
            }
        }

        return $message;
    }

    /**
     * 验证错误码是否有效
     *
     * @param  string  $code  错误码
     * @return bool
     */
    public static function isValidErrorCode(string $code): bool
    {
        return isset(self::$errorMessages[$code]);
    }

    /**
     * 获取所有错误码
     *
     * @return array
     */
    public static function getAllErrorCodes(): array
    {
        return array_keys(self::$errorMessages);
    }
} 