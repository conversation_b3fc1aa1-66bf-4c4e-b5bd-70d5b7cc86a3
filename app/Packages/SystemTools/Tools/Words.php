<?php

namespace App\Packages\SystemTools\Tools;

use App\Models\SystemConfig;
use App\Packages\SystemTools\BaseClient;
use Exception;

class Words extends BaseClient
{
    /**
     * 敏感信息
     *
     * @param  string  $text
     * @return void
     */
    public function sensitiveness(string $text): bool
    {
        $appCode = SystemConfig::getValue('SensitivenessCode', '');
        if (blank($appCode)) {
            throw new Exception('APPCODE尚未配置');
        }
        $result  = $this->setUrl('https://jmwbsh.market.alicloudapi.com')
            ->setPath('/wbsh/text/review')
            ->setHeader([
                'Content-Type'  => 'application/x-www-form-urlencoded; charset=UTF-8',
                'Authorization' => "APPCODE {$appCode}",
            ])
            ->setParams([
                'text' => $text,
            ])
            ->post();
        $collect = $result->toArray();
        if ($collect['code'] == 200) {
            $data = collect($collect['data']);
            if ($data['result'] == 1) {
                return true;
            } else {
                $resultItems = collect($data['resultItems']);
                throw new Exception($resultItems->pluck('msg')->implode(','));
            }
        } else {
            throw new Exception($collect['msg']);
        }
    }
}