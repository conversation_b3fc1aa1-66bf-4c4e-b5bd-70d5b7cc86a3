<?php

namespace App\Packages\BailianAssistant\Tools;

use AlibabaCloud\SDK\Bailian\V20231229\Models\CreateMemoryNodeRequest;
use AlibabaCloud\SDK\Bailian\V20231229\Models\CreateMemoryNodeResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\CreateMemoryRequest;
use AlibabaCloud\SDK\Bailian\V20231229\Models\CreateMemoryResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\DeleteMemoryNodeResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\DeleteMemoryResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\GetMemoryNodeResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\GetMemoryResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\ListMemoriesRequest;
use AlibabaCloud\SDK\Bailian\V20231229\Models\ListMemoriesResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\ListMemoryNodesRequest;
use AlibabaCloud\SDK\Bailian\V20231229\Models\ListMemoryNodesResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\UpdateMemoryNodeRequest;
use AlibabaCloud\SDK\Bailian\V20231229\Models\UpdateMemoryNodeResponseBody;
use AlibabaCloud\Tea\Exception\TeaError;
use App\Packages\BailianAssistant\BaseClient;
use Exception;

class MemoryTool extends BaseClient
{
    /**
     * @param $nextToken
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\ListMemoriesResponseBody
     */
    public function lists($nextToken = ''): ListMemoriesResponseBody
    {
        $request = new ListMemoriesRequest([
            'maxResults' => 50,
            'nextToken'  => $nextToken
        ]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->listMemoriesWithOptions($this->serviceConfig->getWorkspaceId(), $request,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $description
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\CreateMemoryResponseBody
     */
    public function create(string $description): CreateMemoryResponseBody
    {
        $request = new CreateMemoryRequest([
            'description' => $description,
        ]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->createMemoryWithOptions($this->serviceConfig->getWorkspaceId(), $request,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $memoryId
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\GetMemoryResponseBody
     */
    public function get(string $memoryId): GetMemoryResponseBody
    {
        try {
            $response = $this->client->getMemoryWithOptions($this->serviceConfig->getWorkspaceId(),
                $memoryId,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $memoryId
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\DeleteMemoryResponseBody
     */
    public function delete(string $memoryId): DeleteMemoryResponseBody
    {
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->deleteMemoryWithOptions($this->serviceConfig->getWorkspaceId(), $memoryId,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $memoryId
     * @param  string  $nextToken
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\ListMemoryNodesResponseBody
     */
    public function nodeList(string $memoryId, string $nextToken = ''): ListMemoryNodesResponseBody
    {
        $request = new ListMemoryNodesRequest([
            'maxResults' => 50,
            'nextToken'  => $nextToken
        ]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->listMemoryNodesWithOptions($this->serviceConfig->getWorkspaceId(),
                $memoryId,
                $request,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $memoryId
     * @param  string  $content
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\CreateMemoryNodeResponseBody
     */
    public function createNode(string $memoryId, string $content): CreateMemoryNodeResponseBody
    {
        $request = new CreateMemoryNodeRequest([
            'content' => $content,
        ]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->createMemoryNodeWithOptions($this->serviceConfig->getWorkspaceId(), $memoryId,
                $request,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $memoryId
     * @param  string  $memoryNodeId
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\GetMemoryNodeResponseBody
     */
    public function getNode(string $memoryId, string $memoryNodeId): GetMemoryNodeResponseBody
    {
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->getMemoryNodeWithOptions($this->serviceConfig->getWorkspaceId(),
                $memoryId,
                $memoryNodeId,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $memoryId
     * @param  string  $memoryNodeId
     * @param  string  $content
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\UpdateMemoryNodeResponseBody
     */
    public function updateNode(string $memoryId, string $memoryNodeId, string $content): UpdateMemoryNodeResponseBody
    {
        $request = new UpdateMemoryNodeRequest([
            'content' => $content,
        ]);
        try {
            $response = $this->client->updateMemoryNodeWithOptions($this->serviceConfig->getWorkspaceId(),
                $memoryId,
                $memoryNodeId,
                $request,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $memoryId
     * @param  string  $memoryNodeId
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\DeleteMemoryNodeResponseBody
     */
    public function deleteNode(string $memoryId, string $memoryNodeId): DeleteMemoryNodeResponseBody
    {
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->deleteMemoryNodeWithOptions($this->serviceConfig->getWorkspaceId(),
                $memoryId,
                $memoryNodeId,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }
}