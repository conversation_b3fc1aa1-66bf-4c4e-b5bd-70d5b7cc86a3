<?php

namespace App\Packages\BailianAssistant\Tools;

use AlibabaCloud\SDK\Bailian\*********\Models\AddFileRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\AddFileResponseBody;
use AlibabaCloud\SDK\Bailian\*********\Models\ApplyFileUploadLeaseRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\ApplyFileUploadLeaseResponseBody;
use AlibabaCloud\SDK\Bailian\*********\Models\CreateIndexRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\CreateIndexResponseBody;
use AlibabaCloud\SDK\Bailian\*********\Models\DeleteFileResponseBody;
use AlibabaCloud\SDK\Bailian\*********\Models\DeleteIndexDocumentRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\DeleteIndexDocumentResponseBody;
use AlibabaCloud\SDK\Bailian\*********\Models\DeleteIndexRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\DescribeFileResponseBody;
use AlibabaCloud\SDK\Bailian\*********\Models\GetIndexJobStatusRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\ListIndexDocumentsRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\ListIndicesRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\RetrieveRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\SubmitIndexAddDocumentsJobRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\SubmitIndexJobRequest;
use AlibabaCloud\SDK\Bailian\*********\Models\SubmitIndexJobResponseBody;
use AlibabaCloud\Tea\Exception\TeaError;
use App\Models\BailianAssistantFile;
use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeFile;
use App\Models\Model;
use App\Packages\BailianAssistant\BaseClient;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Database\Eloquent\Model as EloquentModel;
use Modules\Storage\Models\Upload;

class KnowledgeTool extends BaseClient
{
    /**
     * @param  array  $data
     * @return \AlibabaCloud\SDK\Bailian\*********\Models\CreateIndexResponseBody
     */
    /**
     * 创建知识库索引
     *
     * @param  BailianKnowledge|Model  $knowledge  知识库模型
     * @return CreateIndexResponseBody 创建索引响应
     * @throws Exception 异常
     */
    public function createIndex(BailianKnowledge|Model $knowledge): CreateIndexResponseBody
    {
        $data = [
            'name'          => $knowledge->bailian_name,
            'structureType' => $knowledge->structure_type,
            'sourceType'    => $knowledge->source_type,
            'sinkType'      => $knowledge->sink_type,
        ];

        if ($knowledge->source_type == BailianKnowledge::SOURCE_TYPE_CATEGORY) {
            $categories = $knowledge->categories->pluck('bailian_id')->toArray();

            if (empty($categories)) {
                throw new Exception('分类没有bailian_id');
            }

            $data['categoryIds'] = $categories;
        }
        if ($knowledge->source_type == BailianKnowledge::SOURCE_TYPE_FILE) {
            $fileIds = $knowledge->files->pluck('bailian_id')->toArray();

            if (empty($fileIds)) {
                throw new Exception('缺少文件ID');
            }

            $data['documentIds'] = $fileIds;
        }

        $request = new CreateIndexRequest($data);

        try {
            $response = $this->client->createINDEX($this->serviceConfig->getWorkspaceId(), $request);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }

            throw $error;
        }
    }

    /**
     * 获取知识库索引状态
     *
     * @param  string  $indexId  索引ID
     * @param  string  $jobId  任务ID
     * @param  string  $workspaceId  工作空间ID
     * @return mixed 索引状态信息
     * @throws TeaError
     */
    public function getIndexStatus(string $indexId, string $jobId, string $workspaceId)
    {
        try {
            $request = new GetIndexJobStatusRequest([
                'indexId' => $indexId,
                'jobId'   => $jobId,
            ]);

            $response = $this->client->getIndexJobStatus(
                $workspaceId,
                $request
            );

            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * Notes: 提交索引任务
     *
     * @Author: 玄尘
     * @Date: 2025/5/7 16:08
     * @param  string  $indexId
     * @param  string  $workspace_id
     * @return \AlibabaCloud\SDK\Bailian\*********\Models\SubmitIndexJobResponseBody
     */
    public function submitIndexJob(string $indexId, string $workspace_id = ''): SubmitIndexJobResponseBody
    {
        $request = new SubmitIndexJobRequest([
            'indexId' => $indexId,
        ]);

        try {
            if (! $workspace_id) {
                $workspace_id = $this->serviceConfig->getWorkspaceId();
            }
            $response = $this->client->submitIndexJob(
                $workspace_id,
                $request
            );

            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * Notes: 查询索引列表
     *
     * @Author: 玄尘
     * @Date: 2025/5/7 10:43
     */
    public function getListIndices(string $workspace_id = '', $page = 1, $pageSize = 10)
    {
        $request = new ListIndicesRequest([
            'page'     => $page,
            'pageSize' => $pageSize,
        ]);
        try {
            if (! $workspace_id) {
                $workspace_id = $this->serviceConfig->getWorkspaceId();
            }
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->listIndicesWithOptions(
                $workspace_id,
                $request,
                $this->headers,
                $this->runtime
            );
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * Notes: 检索知识索引
     *
     * @Author: 玄尘
     * @Date: 2025/5/21 14:20
     */
    public function getRetrieve(string $indexId, string $workspace_id = '', $query = '', array $images = [])
    {
        $request = new RetrieveRequest([
            'indexId' => $indexId,
            'query'   => $query,
            'images'  => $images,
        ]);
        try {
            if (! $workspace_id) {
                $workspace_id = $this->serviceConfig->getWorkspaceId();
            }
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->retrieveWithOptions(
                $workspace_id,
                $request,
                $this->headers,
                $this->runtime
            );
            return $response->body;
        } catch (Exception $exception) {
        }
    }

    /**
     * Notes: 查询索引下的文档列表
     *
     * @Author: 玄尘
     * @Date: 2025/5/21 15:21
     * @param  string  $indexId
     * @param  string  $workspace_id
     * @return \AlibabaCloud\SDK\Bailian\*********\Models\ListIndexDocumentsResponseBody
     */
    public function ListIndexDocuments(string $indexId, string $workspace_id = '')
    {
        $request = new ListIndexDocumentsRequest([
            'indexId' => $indexId,
        ]);
        try {
            if (! $workspace_id) {
                $workspace_id = $this->serviceConfig->getWorkspaceId();
            }
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->listIndexDocumentsWithOptions(
                $workspace_id,
                $request,
                $this->headers,
                $this->runtime
            );
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * 删除知识库索引
     *
     * @param  string  $workspace_id  工作空间ID
     * @param  string  $indexId  索引ID
     * @return mixed 响应体
     * @throws Exception 异常
     */
    public function deleteIndex(string $workspace_id, string $indexId)
    {
        try {
            $request = new DeleteIndexRequest([
                'indexId' => $indexId,
            ]);

            $response = $this->client->deleteIndex(
                $workspace_id,
                $request
            );

            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }

            throw $error;
        }
    }

    /**
     * 申请文件上传租约
     *
     * @param  string  $fileName  文件名称
     * @param  string  $categoryId  类目ID
     * @param  string  $md5  文件MD5值
     * @param  string  $sizeInBytes  文件大小（字节）
     * @param  string  $workspaceId  工作空间ID (可选)
     * @param  string  $categoryType
     * @return \AlibabaCloud\SDK\Bailian\*********\Models\ApplyFileUploadLeaseResponseBody
     */
    public function applyFileUploadLease(
        string $fileName,
        string $categoryId,
        string $md5,
        string $sizeInBytes,
        string $workspaceId = '',
        string $categoryType = 'UNSTRUCTURED'
    ): ApplyFileUploadLeaseResponseBody {
        $request = new ApplyFileUploadLeaseRequest([
            'categoryType' => $categoryType,
            'fileName'     => $fileName,
            'md5'          => $md5,
            'sizeInBytes'  => $sizeInBytes,
        ]);

        try {
            if (empty($workspaceId)) {
                $workspaceId = $this->serviceConfig->getWorkspaceId();
            }
            $this->logger
                ->info('申请文件上传租约', [
                    'file_name'     => $fileName,
                    'category_id'   => $categoryId,
                    'md5'           => $md5,
                    'workspace_id'  => $workspaceId,
                    'size_in_bytes' => $sizeInBytes,
                    'category_type' => $categoryType,
                ]);
            $response = $this->client->applyFileUploadLease($categoryId, $workspaceId, $request);

            if ($response->statusCode != 200) {
                throw new Exception($response->body->message);
            }
            $this->logger
                ->info('申请文件上传租约成功', [
                    'file_name'    => $fileName,
                    'category_id'  => $categoryId,
                    'workspace_id' => $workspaceId,
                    'body'         => $response->body
                ]);
            return $response->body;
        } catch (Exception $error) {
            $this->logger
                ->error('申请文件上传租约失败', [
                    'file_name'    => $fileName,
                    'category_id'  => $categoryId,
                    'workspace_id' => $workspaceId,
                    'error'        => $error->getMessage(),
                ]);

            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * 将文件添加到知识库
     *
     * @param  string  $leaseId
     * @param  string  $categoryId  分类ID
     * @param  string  $workspaceId  工作空间ID
     * @param  string  $categoryType
     * @return mixed
     */
    public function addFile(
        string $leaseId,
        string $categoryId,
        string $workspaceId,
        string $categoryType = 'UNSTRUCTURED'
    ): AddFileResponseBody {
        try {
            $parser = 'DASHSCOPE_DOCMIND';
            $this->logger->info('准备将文件添加到知识库', [
                'lease_id'      => $leaseId,
                'category_id'   => $categoryId,
                'workspace_id'  => $workspaceId,
                'category_type' => $categoryType,
                'parser'        => $parser,
            ]);
            $request  = new AddFileRequest([
                'leaseId'      => $leaseId,
                'categoryId'   => $categoryId,
                'parser'       => $parser,
                'categoryType' => $categoryType,
            ]);
            $response = $this->client->addFile(
                $workspaceId,
                $request
            );

            return $response->body;
        } catch (Exception $error) {
            $this->logger
                ->error('将文件添加到知识库失败', [
                    'leaseId'      => $leaseId,
                    'category_id'  => $categoryId,
                    'workspace_id' => $workspaceId,
                    'error'        => $error->getMessage(),
                ]);

            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * 上传文件到知识库
     *
     * @param  string  $filePathOrUrl  OSS文件URL或本地文件路径
     * @param  string  $categoryId  分类ID
     * @param  string  $md5  文件MD5值
     * @param  string  $sizeInBytes  文件大小（字节）
     * @param  string  $workspaceId  工作空间ID (可选)
     * @param  string  $categoryType  分类类型 (默认: UNSTRUCTURED)
     * @return array
     */
    public function uploadFileToKnowledge(
        string $filePathOrUrl,
        string $categoryId,
        string $md5,
        string $sizeInBytes,
        string $workspaceId = '',
        string $categoryType = 'UNSTRUCTURED'
    ): array {
        try {
            // 验证文件路径或URL
            if (empty($filePathOrUrl)) {
                throw new Exception('文件路径或URL不能为空');
            }

            // 获取文件名
            $fileName = $this->getFileNameFromPathOrUrl($filePathOrUrl);
            if (! $workspaceId) {
                $workspaceId = $this->serviceConfig->getWorkspaceId();
            }
            // 申请文件上传租约
            $leaseResponse = $this->applyFileUploadLease($fileName, $categoryId, $md5, $sizeInBytes,
                $workspaceId,
                $categoryType);

            if (! $leaseResponse || ! $leaseResponse->data) {
                throw new Exception('申请文件上传租约失败，返回数据为空');
            }
            $param   = $leaseResponse->data->param ?? null;
            $leaseId = $leaseResponse->data->fileUploadLeaseId ?? null;

            if (! $param || ! $leaseId) {
                throw new Exception('申请文件上传租约失败，缺少必要参数');
            }

            // 根据文件来源选择不同的上传方式
            $isUrl = validateUrlWithUnicode($filePathOrUrl);
            $this->logger->info('准备上传文件', [
                'file_name'        => $fileName,
                'category_id'      => $categoryId,
                'file_path_or_url' => $filePathOrUrl,
                'workspace_id'     => $workspaceId,
                'is_url'           => $isUrl,
            ]);
            if ($isUrl) {
                // 如果是URL来源，从URL下载并上传
                $this->uploadFromUrl($filePathOrUrl, $param);
            } else {
                // 如果是本地文件，直接上传
                $this->uploadFromFile($filePathOrUrl, $param);
            }
            // 将文件添加到知识库
            $addFileResponse = $this->addFile($leaseId, $categoryId, $workspaceId, $categoryType);

            $fileId = $addFileResponse->data->fileId ?? null;

            if (! $fileId) {
                throw new Exception('添加文件到知识库失败，未返回文档ID');
            }

            $this->logger
                ->info('文件成功上传并与知识库关联', [
                    'file_id'      => $fileId,
                    'file_name'    => $fileName,
                    'category_id'  => $categoryId,
                    'workspace_id' => $workspaceId,
                ]);

            return [
                'file_id'       => $fileId,
                'category_id'   => $categoryId,
                'workspace_id'  => $workspaceId,
                'file_name'     => $fileName,
                'lease_id'      => $leaseId,
                'parser'        => 'DASHSCOPE_DOCMIND',
                'category_type' => $categoryType,
            ];
        } catch (Exception $error) {
            $this->logger
                ->error('上传文件到知识库失败', [
                    'file_path_or_url' => $filePathOrUrl,
                    'category_id'      => $categoryId,
                    'workspace_id'     => $workspaceId,
                    'error'            => $error->getMessage(),
                ]);

            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * 从文件路径或URL中提取文件名
     *
     * @param  string  $pathOrUrl  文件路径或URL
     * @return string 文件名
     */
    private function getFileNameFromPathOrUrl(string $pathOrUrl): string
    {
        // 如果是URL，从URL中提取文件名
        if (filter_var($pathOrUrl, FILTER_VALIDATE_URL)) {
            $parsedUrl = parse_url($pathOrUrl);
            $path      = $parsedUrl['path'] ?? '';
            return pathinfo($path, PATHINFO_BASENAME) ?: basename($pathOrUrl);
        }

        // 如果是本地路径，直接获取文件名
        return pathinfo($pathOrUrl, PATHINFO_BASENAME);
    }

    /**
     * 从本地文件上传到临时存储空间
     *
     * @param  string  $filePath  本地文件路径
     * @param  object  $param  参数对象
     * @throws Exception
     */
    private function uploadFromFile(string $filePath, object $param): void
    {
        try {
            // 验证文件是否存在且可读
            if (! file_exists($filePath)) {
                throw new Exception('文件不存在: '.$filePath);
            }

            if (! is_readable($filePath)) {
                throw new Exception('文件不可读: '.$filePath);
            }

            // 获取文件大小
            $fileSize = filesize($filePath);
            if ($fileSize === false) {
                throw new Exception('无法获取文件大小: '.$filePath);
            }

            // 获取文件的MIME类型
            $finfo    = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $filePath) ?: 'application/octet-stream';
            finfo_close($finfo);

            // 获取上传URL
            $uploadUrl = $param->url;
            if (empty($uploadUrl)) {
                throw new Exception('上传URL不能为空');
            }

            // 创建 Guzzle HTTP 客户端
            $client = new Client([
                'verify'  => false, // 不验证SSL证书
                'timeout' => 300,  // 设置5分钟超时
            ]);

            // 准备请求选项
            $options = [
                'headers' => $param->headers,
                'body'    => fopen($filePath, 'r'),
            ];

            // 记录请求信息
            $this->logger->info('准备上传文件', [
                'file_path'  => $filePath,
                'upload_url' => $uploadUrl,
                'method'     => $param->method ?? 'PUT',
            ]);

            // 执行请求
            $response = $client->request($param->method ?? 'PUT', $uploadUrl, $options);
            $httpCode = $response->getStatusCode();

            // 检查上传结果
            if ($httpCode !== 200) {
                $this->logger->error('文件上传失败详细信息', [
                    'file_path' => $filePath,
                    'http_code' => $httpCode,
                    'response'  => (string) $response->getBody(),
                ]);

                throw new Exception("文件上传失败: HTTP Code {$httpCode}");
            }

            $this->logger->info('文件上传成功', [
                'file_path' => $filePath,
                'http_code' => $httpCode,
            ]);
        } catch (\GuzzleHttp\Exception\GuzzleException $e) {
            $this->logger->error('文件上传Guzzle异常', [
                'file_path' => $filePath,
                'error'     => $e->getMessage(),
                'trace'     => $e->getTraceAsString(),
            ]);
            throw new Exception("文件上传Guzzle异常: {$e->getMessage()}");
        } catch (Exception $e) {
            $this->logger->error('文件上传异常', [
                'file_path' => $filePath,
                'error'     => $e->getMessage(),
                'trace'     => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    /**
     * 从URL下载并上传文件到阿里云百炼平台
     *
     * @param  string  $fileUrl  源文件URL
     * @param  object  $param  上传参数对象，包含method、url和headers
     * @return void
     * @throws Exception 当上传失败时抛出异常
     */
    private function uploadFromUrl(string $fileUrl, object $param): void
    {
        $this->logger->info('开始处理URL文件上传', [
            'source_url' => $fileUrl,
        ]);

        $fileUrl = urlToUnicode($fileUrl);
        try {
            // 创建 Guzzle HTTP 客户端
            $client = new Client([
                'verify'          => false, // 不验证SSL证书
                'timeout'         => 120,  // 设置2分钟超时
                'allow_redirects' => [
                    'max'       => 5,     // 最大重定向次数
                    'strict'    => false,
                    'referer'   => false,
                    'protocols' => ['http', 'https'],
                ],
            ]);

            // 下载源文件
            $this->logger->info('开始从URL下载文件', ['source_url' => $fileUrl]);

            $sourceResponse = $client->get($fileUrl);
            $sourceHttpCode = $sourceResponse->getStatusCode();
            $contentType    = $sourceResponse->getHeaderLine('Content-Type') ?: 'application/octet-stream';
            $sourceContent  = (string) $sourceResponse->getBody();

            // 检查下载结果
            if ($sourceHttpCode !== 200 || empty($sourceContent)) {
                throw new Exception('无法从源URL下载文件: '.$fileUrl.', HTTP Code: '.$sourceHttpCode);
            }

            $this->logger->info('文件下载成功', [
                'source_url'   => $fileUrl,
                'file_size'    => strlen($sourceContent),
                'content_type' => $contentType
            ]);

            // 从URL中提取文件名
            $fileName = $this->getFileNameFromPathOrUrl($fileUrl);

            // 解码上传URL
            $uploadUrl = $param->url;

            // 准备请求头
            $headers = $param->headers;

            // 如果headers是JSON字符串，解析它
            if (is_string($headers) && $this->isJson($headers)) {
                $headers = json_decode($headers, true);
            }

            // 确保headers是数组
            if (! is_array($headers)) {
                $headers = (array) $headers;
            }

            // 准备上传请求选项
            $options = [
                'headers' => $headers,
                'body'    => $sourceContent,
                'timeout' => 300, // 设置5分钟超时
            ];
            $method  = $param->method ?? 'PUT';

            $this->logger->info('准备上传从URL获取的文件', [
                'source_url'   => $fileUrl,
                'upload_url'   => $uploadUrl,
                'file_name'    => $fileName,
                'content_type' => $contentType,
                'method'       => $method,
                'headers'      => json_encode($headers),
            ]);
            // 执行上传请求
            $response = $client->request($method, $uploadUrl, $options);
            $httpCode = $response->getStatusCode();

            // 检查上传结果
            if ($httpCode < 200 || $httpCode >= 300) {
                $this->logger->error('URL文件上传失败详细信息', [
                    'source_url' => $fileUrl,
                    'http_code'  => $httpCode,
                    'response'   => (string) $response->getBody(),
                ]);
                throw new Exception("URL文件上传失败: HTTP Code {$httpCode}");
            }

            $this->logger->info('URL文件上传成功', [
                'source_url' => $fileUrl,
                'http_code'  => $httpCode,
                'file_size'  => strlen($sourceContent),
            ]);

            return;
        } catch (GuzzleException $e) {
            $this->logger->error('URL文件上传Guzzle异常', [
                'source_url' => $fileUrl,
                'error'      => $e->getMessage(),
                'trace'      => $e->getTraceAsString(),
            ]);
            throw new Exception("URL文件上传Guzzle异常: {$e->getMessage()}", 0, $e);
        } catch (Exception $e) {
            $this->logger->error('URL文件上传异常', [
                'source_url' => $fileUrl,
                'error'      => $e->getMessage(),
                'trace'      => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    /**
     * 检查字符串是否为有效的JSON
     *
     * @param  string  $string  要检查的字符串
     * @return bool 是否为有效的JSON
     */
    private function isJson(string $string): bool
    {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * 创建知识库文件记录
     *
     * @param  BailianKnowledge|Model  $knowledge  知识库模型
     * @param  int  $userId  用户ID
     * @param  string  $categoryId  分类ID
     * @param  int  $storageId  存储ID
     * @param  string  $name  文件名称
     * @param  string  $fileId  百点文件ID
     * @param  string  $workspaceId  工作空间ID
     * @param  string  $leaseId  租约ID
     * @param  EloquentModel|null  $sourceable  源模型实例
     * @return BailianKnowledgeFile 创建的知识库文件记录
     */

    /**
     * 添加文档到知识库
     *
     * @param  BailianKnowledge|Model  $knowledge  知识库模型
     * @param  string  $filePathOrUrl  文件路径或URL
     * @param  string  $categoryId  分类ID
     * @param  string  $md5  文件MD5值
     * @param  string  $sizeInBytes  文件大小（字节）
     * @param  string  $workspaceId  工作空间ID (可选)
     * @param  array  $options  其他选项，如分块模式等
     * @param  int|null  $userId  用户ID（可选）
     * @param  int|null  $storageId  存储ID（可选）
     * @param  EloquentModel|null  $sourceable  源模型实例（可选）
     * @return array 上传结果，包含文件ID和知识库文件记录
     * @throws Exception 异常
     */
    public function addDocumentToKnowledge(
        BailianKnowledge $knowledge,
        string $filePathOrUrl,
        string $categoryId,
        string $md5,
        string $sizeInBytes,
        string $workspaceId = '',
        array $options = [],
    ): array {
        try {
            //检查文件类型是否可以上传到知识库
            $this->checkFileType($filePathOrUrl);
            // 获取文件名（用于日志记录）
            $fileName = $this->getFileNameFromPathOrUrl($filePathOrUrl);

            // 检查知识库是否已有索引ID
            $indexId    = $knowledge->knowledge_id;
            $isNewIndex = false;

            if (! $indexId) {
                // 如果没有索引ID，则创建索引
                $indexResponse = $this->createIndex($knowledge);
                $indexId       = $indexResponse->data->id;
                // 更新知识库模型中的索引ID
                $knowledge->knowledge_id = $indexId;
                $knowledge->save();
                $isNewIndex = true;
            }

            // 上传文件到知识库 - 这个操作已经包含了将文件与分类关联的逻辑
            $documentInfo = $this->uploadFileToKnowledge($filePathOrUrl, $categoryId, $md5, $sizeInBytes,
                $workspaceId);
            $documentId   = $documentInfo['file_id'];

            // 根据是否是新索引选择不同的方法
            if ($isNewIndex) {
                // 对于新创建的知识库，使用submitIndexJob
                $jobResponse = $this->submitIndexJob($indexId, $workspaceId);
            } else {
                $jobResponse = $this->submitIndexAddDocumentsJob(
                    $indexId,
                    [$documentId],
                    $workspaceId,
                    BailianKnowledge::SOURCE_TYPE_FILE,
                    $options
                );
                if ($jobResponse->success !== true) {
                    throw new Exception('追加文档到知识库失败: '.$jobResponse->message);
                }
            }

            return $documentInfo;
        } catch (Exception $error) {
            \Log::error('添加文档到知识库失败', [
                'knowledge_id' => $knowledge->id,
                'file_name'    => $fileName ?? pathinfo($filePathOrUrl, PATHINFO_BASENAME),
                'category_id'  => $categoryId,
                'error'        => $error->getMessage(),
            ]);
            throw $error;
        }
    }

    /**
     * Notes: 删除索引中的文档列表
     *
     * @Author: 玄尘
     * @Date: 2025/5/15 09:49
     * @param  string  $indexId
     * @param  string  $workspaceId
     * @param  array  $documentIds
     * @return \AlibabaCloud\SDK\Bailian\*********\Models\DeleteIndexDocumentResponseBody
     */
    public function deleteIndexDocuments(
        string $indexId,
        string $workspaceId,
        array $documentIds
    ): DeleteIndexDocumentResponseBody {
        try {
            // 校验文档ID列表是否为空
            if (empty($documentIds)) {
                throw new Exception('文档ID列表不能为空');
            }

            // 创建删除索引文档的请求
            $request = new DeleteIndexDocumentRequest([
                'indexId'     => $indexId,
                'documentIds' => $documentIds,
            ]);

            // 调用删除索引文档的方法
            $response = $this->client->deleteIndexDocument(
                $workspaceId,
                $request
            );
            $this->logger
                ->info('删除索引中的文档列表成功', [
                    'index_id'     => $indexId,
                    'workspace_id' => $workspaceId,
                    'document_ids' => $documentIds,
                ]);

            return $response->body;
        } catch (Exception $error) {
            $this->logger
                ->error('删除索引中的文档列表', [
                    'index_id'     => $indexId,
                    'workspace_id' => $workspaceId,
                    'document_ids' => $documentIds,
                    'error'        => $error->getMessage(),
                ]);

            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }

            throw $error;
        }
    }

    /**
     * 向知识库追加文档
     *
     * @param  string  $indexId  知识库索引ID
     * @param  array  $documentIds  文档ID列表
     * @param  string  $workspaceId  工作空间ID (可选)
     * @param  string  $sourceType  数据类型 (默认: DATA_CENTER_FILE)
     * @param  array  $options  其他选项，如分块模式等
     * @return mixed 响应体
     * @throws Exception 异常
     */
    public function submitIndexAddDocumentsJob(
        string $indexId,
        array $documentIds,
        string $workspaceId = '',
        string $sourceType = 'DATA_CENTER_FILE',
        array $options = []
    ): mixed {
        try {
            if (empty($workspaceId)) {
                $workspaceId = $this->serviceConfig->getWorkspaceId();
            }

            $data = [
                'indexId'    => $indexId,
                'sourceType' => $sourceType
            ];

            // 根据sourceType添加相应的参数
            if ($sourceType === BailianKnowledge::SOURCE_TYPE_FILE) {
                $data['documentIds'] = $documentIds;
            } elseif ($sourceType === BailianKnowledge::SOURCE_TYPE_CATEGORY) {
                $data['categoryIds'] = $documentIds;
            }

            // 添加可选的分块参数
            if (isset($options['chunkMode'])) {
                $data['chunkMode'] = $options['chunkMode'];
            }
            if (isset($options['chunkSize'])) {
                $data['chunkSize'] = $options['chunkSize'];
            }
            if (isset($options['overlapSize'])) {
                $data['overlapSize'] = $options['overlapSize'];
            }
            if (isset($options['separator']) && $options['chunkMode'] === 'regex') {
                $data['separator'] = $options['separator'];
            }

            // 创建请求
            $request = new SubmitIndexAddDocumentsJobRequest($data);

            // 发送请求
            $response = $this->client->submitIndexAddDocumentsJob($workspaceId, $request);

            return $response->body;
        } catch (Exception $error) {
            $this->logger->error('向知识库追加文档失败', [
                'index_id'     => $indexId,
                'document_ids' => $documentIds,
                'workspace_id' => $workspaceId,
                'error'        => $error->getMessage(),
            ]);

            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }

            throw $error;
        }
    }

    /**
     * 批量添加文档到知识库
     *
     * @param  BailianKnowledge|Model  $knowledge  知识库模型
     * @param  array  $documents  文档信息数组，每个元素包含文件路径、MD5等信息
     * @param  string  $categoryId  分类ID
     * @param  string  $workspaceId  工作空间ID (可选)
     * @param  array  $options  其他选项，如分块模式等
     * @return array 成功添加的文档ID数组
     * @throws Exception 异常
     */
    public function batchAddDocumentsToKnowledge(
        BailianKnowledge|Model $knowledge,
        array $documents,
        string $categoryId,
        string $workspaceId = '',
        array $options = []
    ): array {
        $successDocumentIds = [];
        $failedDocuments    = [];

        // 检查知识库是否已有索引ID
        $indexId    = $knowledge->knowledge_id ?? null;
        $isNewIndex = false;

        if (! $indexId) {
            // 如果没有索引ID，则创建索引
            $indexResponse           = $this->createIndex($knowledge);
            $indexId                 = $indexResponse->data->id;
            $knowledge->knowledge_id = $indexId;
            $knowledge->save();
            $isNewIndex = true;
        }

        // 上传所有文档
        foreach ($documents as $document) {
            try {
                // 验证文档信息是否完整
                if (! isset($document['path']) || ! isset($document['md5']) || ! isset($document['size'])) {
                    $failedDocuments[] = [
                        'document' => $document,
                        'error'    => '文档信息不完整，缺少path、md5或size字段'
                    ];
                    continue;
                }

                // 上传文件到知识库
                $documentInfo = $this->uploadFileToKnowledge(
                    $document['path'],
                    $categoryId,
                    $document['md5'],
                    $document['size'],
                    $workspaceId
                );

                $successDocumentIds[] = $documentInfo['file_id'];
            } catch (Exception $e) {
                $failedDocuments[] = [
                    'document' => $document,
                    'error'    => $e->getMessage()
                ];

                $this->logger->error('文档上传失败', [
                    'document' => $document,
                    'error'    => $e->getMessage()
                ]);
                // 继续处理下一个文档
            }
        }

        // 如果有成功上传的文档，将它们添加到知识库
        if (! empty($successDocumentIds)) {
            try {
                if ($isNewIndex) {
                    // 对于新创建的知识库，使用submitIndexJob
                    $jobResponse = $this->submitIndexJob($indexId, $workspaceId);
                } else {
                    // 对于已有知识库，使用submitIndexAddDocumentsJob追加文档
                    $jobResponse = $this->submitIndexAddDocumentsJob(
                        $indexId,
                        $successDocumentIds,
                        $workspaceId,
                        BailianKnowledge::SOURCE_TYPE_FILE,
                        $options
                    );
                }
            } catch (Exception $e) {
                $this->logger->error('批量添加文档到知识库失败', [
                    'knowledge_id' => $knowledge->id,
                    'document_ids' => $successDocumentIds,
                    'error'        => $e->getMessage()
                ]);

                throw new Exception('批量添加文档到知识库失败: '.$e->getMessage());
            }
        }

        return [
            'success' => $successDocumentIds,
            'failed'  => $failedDocuments
        ];
    }

    /**
     * 更新知识库中的文档
     *
     * @param  BailianKnowledge|Model  $knowledge  知识库模型
     * @param  string  $documentId  文档ID
     * @param  string  $newFilePath  新文件路径
     * @param  string  $categoryId  分类ID
     * @param  string  $md5  新文件MD5
     * @param  string  $sizeInBytes  新文件大小
     * @param  string  $workspaceId  工作空间ID (可选)
     * @param  array  $options  其他选项，如分块模式等
     * @param  BailianKnowledgeFile|null  $knowledgeFile  知识库文件记录（可选）
     * @return array 更新结果
     * @throws Exception 异常
     */
    public function updateDocumentInKnowledge(
        BailianKnowledge|Model $knowledge,
        string $documentId,
        string $newFilePath,
        string $categoryId,
        string $md5,
        string $sizeInBytes,
        string $workspaceId = '',
        ?BailianKnowledgeFile $knowledgeFile = null
    ): array {
        try {
            if (empty($workspaceId)) {
                $workspaceId = $this->serviceConfig->getWorkspaceId();
            }

            //  删除旧文档
            $this->deleteIndexDocuments($knowledge->knowledge_id, $workspaceId, [$documentId]);

            // 上传新文档
            $documentInfo  = $this->uploadFileToKnowledge($newFilePath, $categoryId, $md5, $sizeInBytes,
                $workspaceId);
            $newDocumentId = $documentInfo['file_id'];

            // 添加新文档到知识库
            $this->submitIndexAddDocumentsJob(
                $knowledge->knowledge_id,
                [$newDocumentId],
                $workspaceId,
                BailianKnowledge::SOURCE_TYPE_FILE,
            );

            if ($knowledgeFile) {
                $knowledgeFile->update([
                    'bailian_category_id' => $categoryId,
                    'file_id'             => $newDocumentId,
                    'lease_id'            => $documentInfo['lease_id'] ?? null
                ]);
            }

            $this->logger->info('更新知识库文档成功', [
                'knowledge_id'    => $knowledge->id,
                'old_document_id' => $documentId,
                'new_document_id' => $newDocumentId,
                'file_path'       => $newFilePath
            ]);

            return [
                'success'         => true,
                'old_document_id' => $documentId,
                'new_document_id' => $newDocumentId,
                'document_info'   => $documentInfo,
                'knowledge_file'  => $knowledgeFile
            ];
        } catch (Exception $error) {
            $this->logger->error('更新知识库文档失败', [
                'knowledge_id' => $knowledge->id,
                'document_id'  => $documentId,
                'file_path'    => $newFilePath,
                'error'        => $error->getMessage()
            ]);

            throw $error;
        }
    }

    /**
     * 搜索知识库内容
     *
     * @param  BailianKnowledge|Model  $knowledge  知识库模型
     * @param  string  $query  搜索关键词
     * @param  int  $topK  返回结果数量
     * @param  string  $workspaceId  工作空间ID (可选)
     * @param  array  $filters  筛选条件
     * @return array 搜索结果
     * @throws Exception 异常
     */
    public function searchKnowledge(
        BailianKnowledge|Model $knowledge,
        string $query,
        int $topK = 5,
        string $workspaceId = '',
        array $filters = []
    ): array {
        try {
            if (empty($workspaceId)) {
                $workspaceId = $this->serviceConfig->getWorkspaceId();
            }

            // 检查知识库是否有索引ID
            $indexId = $knowledge->knowledge_id ?? null;
            if (! $indexId) {
                throw new Exception('知识库索引ID不存在');
            }

            // 构建请求参数
            $data = [
                'indexId' => $indexId,
                'query'   => $query,
                'topK'    => $topK
            ];

            // 添加可选的筛选条件
            if (! empty($filters)) {
                $data['filters'] = $filters;
            }

            // 创建请求
            $request = new RetrieveRequest($data);

            // 发送请求
            $response = $this->client->retrieve($workspaceId, $request);

            $this->logger->info('搜索知识库成功', [
                'knowledge_id' => $knowledge->id,
                'query'        => $query,
                'top_k'        => $topK
            ]);

            return $response->body->data->toArray();
        } catch (Exception $error) {
            $this->logger->error('搜索知识库失败', [
                'knowledge_id' => $knowledge->id,
                'query'        => $query,
                'error'        => $error->getMessage()
            ]);

            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }

            throw $error;
        }
    }

    /**
     * Notes: 查询文档状态
     *
     * @Author: 玄尘
     * @Date: 2025/5/15 17:49
     * @param  string  $fileId
     * @return mixed
     */
    public function getFileStatus(string $fileId): DescribeFileResponseBody
    {
        try {
            $response = $this->client->describeFile(
                $this->serviceConfig->getWorkspaceId(),
                $fileId
            );

            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    //检查文件类型
    public function checkFileType($filePathOrUrl): bool
    {
        $canTypes  = BailianKnowledgeFile::CAN_UPLOAD_FILE_TYPES;
        $extension = pathinfo($filePathOrUrl, PATHINFO_EXTENSION);
        if (! in_array($extension, $canTypes)) {
            throw new Exception('当前文件类型不支持上传到百炼知识库。');
        }
        return true;
    }

    /**
     * Notes: description
     *
     * @Author: 玄尘
     * @Date: 2025/5/21 16:48
     * @param $filePathOrUrl
     * @param $size
     * @return true
     * @throws \Exception
     */
    public function checkAssistantFile($filePathOrUrl, $size): bool
    {
        $canTypes  = BailianAssistantFile::CAN_UPLOAD_FILE_ASSISTANT_TYPES;
        $extension = pathinfo($filePathOrUrl, PATHINFO_EXTENSION);
        if (! isset($canTypes[$extension])) {
            throw new Exception('当前文件类型不支持上传到百炼智能体应用。');
        }
        $maxSize = $canTypes[$extension]['max_size'];
        if ($size > $maxSize) {
            throw new Exception('当前文件大小超过智能体应用允许的最大文件大小。');
        }

        return true;
    }

    /**
     * 上传文件到智能体应用
     *
     * @param  \Modules\Storage\Models\Upload  $storage
     * @param  string  $workspace_id
     * @return array
     * @Author: 玄尘
     * @Date: 2025/5/21 16:33
     */
    public function uploadForAssistant(Upload $storage, string $workspace_id = ''): array
    {
        try {
            if (! $workspace_id) {
                $workspace_id = $this->serviceConfig->getWorkspaceId();
            }
            $filePathUrl = $storage->path_url;

            // 获取文件名
            $fileName    = $this->getFileNameFromPathOrUrl($filePathUrl);
            $md5         = $storage->hash;
            $sizeInBytes = $storage->size;

            // 检查文件类型是否支持
            $this->checkAssistantFile($filePathUrl, $sizeInBytes);

            // 设置CategoryType为SESSION_FILE和CategoryId为default
            $categoryType = 'SESSION_FILE';
            $categoryId   = 'default';
            $workspaceId  = $workspace_id ?? $this->serviceConfig->getWorkspaceId();

            // 申请文件上传租约
            $leaseResponse = $this->applyFileUploadLease(
                $fileName,
                $categoryId,
                $md5,
                $sizeInBytes,
                $workspaceId,
                $categoryType
            );

            if (! $leaseResponse || ! $leaseResponse->data) {
                throw new Exception('申请文件上传租约失败，返回数据为空');
            }

            $param   = $leaseResponse->data->param ?? null;
            $leaseId = $leaseResponse->data->fileUploadLeaseId ?? null;

            if (! $param || ! $leaseId) {
                throw new Exception('申请文件上传租约失败，缺少必要参数');
            }

            // 根据文件来源选择不同的上传方式
            $isUrl = filter_var($filePathUrl, FILTER_VALIDATE_URL);
            if ($isUrl) {
                // 如果是URL来源，从URL下载并上传
                $this->uploadFromUrl($filePathUrl, $param);
            } else {
                // 如果是本地文件，直接上传
                $this->uploadFromFile($filePathUrl, $param);
            }

            // 将文件添加到知识库
            $addFileResponse = $this->addFile($leaseId, $categoryId, $workspaceId, $categoryType);
            $fileId          = $addFileResponse->data->fileId ?? null;

            if (! $fileId) {
                throw new Exception('添加文件失败，未返回文件ID');
            }

            $this->logger->info('文件成功上传到智能体应用', [
                'file_id'       => $fileId,
                'file_name'     => $fileName,
                'category_type' => $categoryType,
                'category_id'   => $categoryId
            ]);

            return [
                'file_id'       => $fileId,
                'file_name'     => $fileName,
                'category_id'   => $categoryId,
                'workspace_id'  => $workspaceId,
                'lease_id'      => $leaseId,
                'category_type' => $categoryType
            ];
        } catch (Exception $error) {
            $this->logger->error('上传文件到智能体应用失败', [
                'file_path_or_url' => $filePathUrl,
                'error'            => $error->getMessage(),
                'trace'            => $error->getTraceAsString()
            ]);

            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * Notes: 删除文件
     *
     * @Author: 玄尘
     * @Date: 2025/6/12 09:30
     * @param  string  $fileId  文件ID
     * @param  string  $workspaceId  工作空间ID (可选)
     * @return DeleteFileResponseBody
     * @throws Exception
     */
    public function deleteFile(string $fileId, string $workspaceId = ''): DeleteFileResponseBody
    {
        try {
            if (! $workspaceId) {
                $workspaceId = $this->serviceConfig->getWorkspaceId();
            }

            $response = $this->client->deleteFile($workspaceId, $fileId);

            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }
}