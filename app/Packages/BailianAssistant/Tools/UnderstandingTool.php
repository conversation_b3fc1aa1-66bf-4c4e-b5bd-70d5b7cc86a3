<?php

namespace App\Packages\BailianAssistant\Tools;

use App\Packages\BailianAssistant\ServiceConfig;
use App\Packages\Coze\Coze;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;
use Psr\Http\Message\StreamInterface;

class UnderstandingTool
{
    protected $response;

    private string        $model = 'qwen-omni-turbo-latest';
    private ServiceConfig $config;
    private Client        $client;

    private array $result = [
        'status'  => false,
        'message' => '',
        'data'    => [],
    ];

    public function __construct()
    {
        $this->config = new ServiceConfig();
        $this->client = new Client([
            'verify'   => false,
            'base_uri' => 'https://dashscope.aliyuncs.com/compatible-mode/v1/',
        ]);
    }

    /**
     * @param  string  $url
     * @param  string  $prompt
     * @return array|void
     * @throws \Exception
     */
    public function url(string $url, string $prompt = '阅读并分析网页')
    {
        set_time_limit(0);
        $response = Coze::workFlow()
            ->setWorkFlowId('7512402844689154063')
            ->send('run', [
                'input' => $url,
            ]);
        if ($response->isSuccess()) {
            $data = $response->toArray();
            if ($data['code'] === 0) {
                $title   = $data['data']['title'];
                $images  = $data['data']['images'];
                $content = $data['data']['content'];
                $system  = wateGetSystemPrompt('UrlToText');
                $prompt  = json_encode([
                    'title'   => $title,
                    'images'  => $images,
                    'content' => $content,
                ], JSON_UNESCAPED_UNICODE);

                try {
                    $client = $response->getAiChat();

                    $client->setPrompt($prompt)
                        ->setSystem($system)
                        ->chat();
                    if ($client->getStatus()) {
                        return [
                            'code' => 0,
                            'data' => $client->getData(),
                        ];
                    } else {
                        return [
                            'code' => 400,
                            'data' => $client->getMessage(),
                        ];
                    }
                } catch (Exception $exception) {
                    return [
                        'code' => 400,
                        'msg'  => $exception->getMessage()
                    ];
                }
            } else {
                return [
                    'code' => 400,
                    'msg'  => $data['error_msg']
                ];
            }
        } else {
            return [
                'code' => 400,
                'msg'  => $response->getMessage()
            ];
        }
    }

    private function chat(string $system, string $prompt, array $mediaArray)
    {
        try {
            $params         = [
                'model'          => $this->model,
                'messages'       => [
                    [
                        'role'    => 'system',
                        'content' => [
                            [
                                'type' => 'text',
                                'text' => $system
                            ]
                        ]
                    ],
                    [
                        'role'    => 'user',
                        'content' => array_merge([
                            [
                                'type' => 'text',
                                'text' => $prompt
                            ]
                        ], $mediaArray)
                    ],
                ],
                'max_tokens'     => 2048,
                'stream'         => true,
                'stream_options' => [
                    'include_usage' => true,
                ],
                'enable_search'  => true,
            ];
            $this->response = $this->client->post('chat/completions', [
                'headers' => $this->getHeader(),
                'json'    => $params,
                'stream'  => true,
            ]);

            if ($this->response->getStatusCode() == 200) {
                $data = [
                    'output' => '',
                    'tokens' => [],
                ];
                foreach ($this->getIterator() as $chat) {
                    $choices        = $chat['data']['choices'][0] ?? [];
                    $usage          = $choices['usage'] ?? [];
                    $data['output'] .= $choices['delta']['content'] ?? '';
                    $data['tokens'] = $usage;
                }
                if (Str::isJson($data['output'])) {
                    $data['output'] = json_decode($data['output'], true);
                    $data['output'] = json_encode($data['output'], JSON_UNESCAPED_UNICODE);
                }
                return $this->success($data);
            } else {
                return $this->error($this->response->getBody()->getContents());
            }
        } catch (RequestException $exception) {
            $errorData = $exception->getResponse()->getBody()->getContents();
            if (Str::isJson($errorData)) {
                return $this->error(json_decode($errorData, true));
            }
            return $this->error($exception->getMessage());
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    private function getHeader()
    {
        return [
            'Authorization' => 'Bearer '.$this->config->getApiKey(),
        ];
    }

    public function getIterator()
    {
        while (! $this->response->getBody()->eof()) {
            $line = $this->readLine($this->response->getBody());
            if (str_starts_with($line, 'id:')) {
                $this->currect['id'] = trim(substr($line, strlen('id:')));
                continue;
            }
            if (str_starts_with($line, 'event:')) {
                $this->currect['event'] = trim(substr($line, strlen('event:')));
                continue;
            }
            if (str_starts_with($line, ':HTTP_STATUS/')) {
                $this->currect['code'] = trim(substr($line, strlen(':HTTP_STATUS/')));
                continue;
            }
            if (str_starts_with($line, 'data:')) {
                $this->currect['data'] = json_decode(trim(substr($line, strlen('data:'))), true);
                yield $this->currect;
            }
        }
    }

    private function readLine(StreamInterface $stream): string
    {
        $buffer = '';

        while (! $stream->eof()) {
            if ('' === ($byte = $stream->read(1))) {
                return $buffer;
            }
            $buffer .= $byte;
            if ($byte === "\n") {
                break;
            }
        }

        return $buffer;
    }

    /**
     * @param  array  $data
     * @return array
     */
    private function success(array $data): array
    {
        $this->result = [
            'status'  => true,
            'message' => 'success',
            'data'    => $data,
        ];
        return $this->result;
    }

    /**
     * @param  string|array  $message
     * @return array
     */
    private function error(string|array $message): array
    {
        $this->result = [
            'status'  => false,
            'message' => is_array($message) ? '失败' : $message,
            'data'    => is_array($message) ? $message : '',
        ];
        return $this->result;
    }

    public function image(string $imageUrl, string $prompt = '分析图片')
    {
        $system = wateGetSystemPrompt('ImageToArticle');

        return $this->chat($system, $prompt, [
            [
                'type'      => 'image_url',
                'image_url' => ['url' => $imageUrl],
            ]
        ]);
    }

    public function audio(string $audioUrl, string $prompt = '识别并分析语音中的内容')
    {
        $system = wateGetSystemPrompt('AudioToText');
        return $this->chat($system, $prompt, [
            [
                'type'        => 'input_audio',
                'input_audio' => ['data' => $audioUrl],
            ]
        ]);
    }

    /**
     * 对提供的文本进行润色，并尝试返回结构化的修改建议。
     *
     * @param  string  $textToPolish  需要润色的文本
     * @param  string  $style  润色风格
     * @return array 包含润色结果或错误的数组
     */
    public function polishText(string $textToPolish, string $style = '请保持整体风格'): array
    {
        $systemPrompt = wateGetSystemPrompt('ArticlePolishText');
        $userPrompt   = $textToPolish.'\n\n'.$style;

        return $this->chat($systemPrompt, $userPrompt, []);
    }

    /**
     * 对提供的文本进行纠错，并尝试返回结构化的修改建议。
     *
     * @param  string  $textToCorrect  需要纠错的文本
     * @return array 包含纠错结果或错误的数组
     */
    public function correctText(string $textToCorrect): array
    {
        $systemPrompt = wateGetSystemPrompt('ArticleCorrectText');

        return $this->chat($systemPrompt, $textToCorrect, []);
    }

    /**
     * Notes: 总结网页内容
     *
     * @Author: 玄尘
     * @Date: 2025/5/29 18:41
     * @param  string  $content
     * @param  string  $prompt
     * @return array
     */
    public function generateNewsSummary(string $content, string $prompt = "生成新闻事件总结"): array
    {
        $systemPrompt = wateGetSystemPrompt('UrlToText');

        $userMessage = "用户需求：{$prompt}\n\n请根据用户的具体需求和以下新闻内容生成事件总结：\n\n{$content}";
        return $this->chat($systemPrompt, $userMessage, []);
    }

    public function generateTagsByText($text, $type = 'article')
    {
        $systemPrompt = wateGetSystemPrompt('GenerateTagsByText');
        return $this->chat($systemPrompt, $text, []);
    }
}