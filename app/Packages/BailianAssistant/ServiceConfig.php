<?php

namespace App\Packages\BailianAssistant;

class ServiceConfig
{
    protected string $appId;
    protected string $workspaceId;
    protected string $apiKey;
    protected string $accessKeyId;
    protected string $accessKeySecret;
    protected string $sessionId;
    protected string $memoryId;

    public function __construct()
    {
        $this->apiKey          = env('BAILIAN_API_KEY', '');
        $this->appId           = env('BAILIAN_APP_ID', '');
        $this->workspaceId     = env('BAILIAN_WORKSPACE_ID', '');
        $this->accessKeyId     = env('BAILIAN_ACCESS_KEY_ID', '');
        $this->accessKeySecret = env('BAILIAN_ACCESS_KEY_SECRET', '');
    }

    public function getSessionId(): string
    {
        return $this->sessionId;
    }

    public function setSessionId(string $sessionId): self
    {
        $this->sessionId = $sessionId;
        return $this;
    }

    public function getMemoryId(): string
    {
        return $this->memoryId;
    }

    public function setMemoryId(string $memoryId): self
    {
        $this->memoryId = $memoryId;
        return $this;
    }

    public function getApiKey(): string
    {
        return $this->apiKey;
    }

    public function getAppId(): string
    {
        return $this->appId;
    }

    public function getWorkspaceId(): string
    {
        return $this->workspaceId;
    }

    public function getAccessKeyId(): string
    {
        return $this->accessKeyId;
    }

    public function getAccessKeySecret(): string
    {
        return $this->accessKeySecret;
    }
}