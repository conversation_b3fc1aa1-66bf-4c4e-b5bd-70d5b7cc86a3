<?php

namespace App\Packages\XfyunCkm;

use App\Packages\XfyunCkm\Client\AggregateClient;
use App\Packages\XfyunCkm\Client\QueryClient;

class XfyunCkm
{
    /**
     * 获取能力聚合客户端
     * 用于提交会议内容并请求生成智能总结、章节概要和待办事项
     *
     * @return AggregateClient
     */
    public static function aggregate(): AggregateClient
    {
        return new AggregateClient();
    }

    /**
     * 获取结果查询客户端
     * 用于查询异步任务的处理结果
     *
     * @return QueryClient
     */
    public static function query(): QueryClient
    {
        return new QueryClient();
    }
}
