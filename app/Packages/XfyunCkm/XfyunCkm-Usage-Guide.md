# XfyunCkm Package 使用指南

## 📋 概述

XfyunCkm Package 是一个集成讯飞会议知识管理服务(CKM)的Laravel包，提供会议内容智能分析、总结生成、待办事项提取等功能。

## 🏗️ Package结构

```
app/Packages/XfyunCkm/
├── XfyunCkm.php               # 主入口类，提供静态工厂方法
├── BaseClient.php             # 基础客户端，处理认证和HTTP请求
├── XfyunCkmException.php      # 增强的异常处理类
├── README.md                  # 详细使用文档
└── Client/
    ├── AggregateClient.php    # 能力聚合客户端（异步接口）
    └── QueryClient.php        # 结果查询客户端（同步接口）
```

## ⚙️ 环境配置

在 `.env` 文件中添加以下配置：

```env
# 讯飞CKM会议知识管理服务配置
XFYUN_CKM_APP_ID=your_app_id
XFYUN_CKM_ACCESS_KEY_ID=your_access_key_id
XFYUN_CKM_ACCESS_KEY_SECRET=your_access_key_secret
XFYUN_CKM_BASE_URI=https://office-api-ckm-dx.iflyaisol.com
```

## 🚀 基本使用

### 1. 快速开始

```php
use App\Packages\XfyunCkm\XfyunCkm;

// 准备会议段落数据
$paragraphs = [
    [
        'ws' => '这是第一段会议内容...',
        'pt' => '1',
        'pg' => '1000',  // 可选：段首时间(ms)
        'pd' => '11000', // 可选：段尾时间(ms)
        'prl' => '1'     // 可选：角色ID
    ],
    [
        'ws' => '这是第二段会议内容...',
        'pt' => '2',
    ]
];

// 提交处理请求
$result = XfyunCkm::aggregate()->requestComplete($paragraphs);
$taskId = $result['taskId'];

// 查询结果
$result = XfyunCkm::query()->pollQueryResult($taskId);
```

### 2. 快捷方法

```php
// 仅请求会议纪要
$result = XfyunCkm::aggregate()->requestSummary($paragraphs);

// 仅请求篇章规整
$result = XfyunCkm::aggregate()->requestRegularity($paragraphs);

// 仅请求会议待办
$result = XfyunCkm::aggregate()->requestMeetingAgent($paragraphs);

// 请求完整处理（纪要+规整+待办）
$result = XfyunCkm::aggregate()->requestComplete($paragraphs);
```

### 3. 结果查询

```php
// 一次性查询
$result = XfyunCkm::query()->getQueryResult($taskId);

// 轮询查询直到完成
$result = XfyunCkm::query()->pollQueryResult($taskId, 30, 5);

// 快捷查询方法
$summary = XfyunCkm::query()->getTextSummary($taskId);
$agents = XfyunCkm::query()->getMeetingAgent($taskId);
$regularity = XfyunCkm::query()->getRegularity($taskId);
```

## 🔗 与会议笔记系统集成

### MEETING类型笔记

我们新增了 `BailianArticleTypeEnum::MEETING` 类型，专门用于会议录音：

```php
use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Services\KnowledgeItemStrategies\ArticleKnowledgeStrategy;

// 创建会议笔记
$article = BailianArticle::create([
    'title' => '项目讨论会议',
    'type' => BailianArticleTypeEnum::MEETING,
    'content' => [
        'audio' => 'https://example.com/meeting.wav',
        'audioDuration' => 1800,
        'data' => '',
        'text_record' => json_encode([
            [
                'start' => 1000,
                'end' => 5000,
                'text' => '大家好，今天我们讨论项目进度...',
                'speaker' => 1
            ],
            [
                'start' => 6000,
                'end' => 12000,
                'text' => '目前开发进度良好，预计下周完成...',
                'speaker' => 2
            ]
        ])
    ],
    'user_id' => 1,
    'status' => 1
]);

// 自动生成会议总结
$strategy = new ArticleKnowledgeStrategy();
$result = $strategy->generateMeetingSummary($article);

// 安全版本（不抛出异常）
$result = $strategy->generateMeetingSummarySafely($article);
```

### 数据转换流程

```
text_record JSON数据
↓ 自动转换
会议段落格式 (ps数组)
↓ 调用讯飞CKM
智能分析结果
↓ 保存到
article->description 字段
```

## 📊 功能码说明

| 功能码 | 功能名称 | 说明 |
|--------|----------|------|
| 4 | 全文会议纪要 | 生成整个会议的总结和要点 |
| 5 | 篇章规整 | 对会议内容进行结构化整理 |
| 6 | 会议待办 | 提取会议中的待办事项 |
| 7 | 意群划分 | 将内容按意群进行划分 |
| 8 | 意群标题生成 | 为意群生成标题 |
| 9 | 意群会议纪要生成 | 为每个意群生成纪要 |

## 🛡️ 异常处理

### 异常类型

XfyunCkmException 提供了多种错误类型判断：

```php
use App\Packages\XfyunCkm\XfyunCkmException;

try {
    $result = XfyunCkm::aggregate()->requestSummary($paragraphs);
} catch (XfyunCkmException $e) {
    // 获取详细错误信息
    $errorDetails = $e->getErrorDetails();
    
    // 根据错误类型进行不同处理
    if ($e->isConfigError()) {
        // 配置错误，检查环境变量
        logger()->error('CKM配置错误', $errorDetails);
    } elseif ($e->isAuthError()) {
        // 认证错误，检查appId、accessKey等
        logger()->error('CKM认证失败', $errorDetails);
    } elseif ($e->isParameterError()) {
        // 参数错误，检查请求参数
        logger()->error('CKM参数错误', $errorDetails);
    } elseif ($e->isRetryable()) {
        // 可重试错误，稍后重试
        logger()->warning('CKM请求失败，可重试', $errorDetails);
    } else {
        // 其他错误
        logger()->error('CKM请求失败', $errorDetails);
    }
}
```

### 错误判断方法

- `isConfigError()` - 配置错误
- `isValidationError()` - 验证错误  
- `isRequestError()` - 请求错误
- `isResponseError()` - 响应错误
- `isTimeoutError()` - 超时错误
- `isBusinessError()` - 业务错误
- `isAuthError()` - 认证错误
- `isParameterError()` - 参数错误
- `isRetryable()` - 是否可重试

### 错误码对照表

| 错误码 | 描述 |
|--------|------|
| 000000 | 服务成功 |
| 999999 | 服务发生未知错误 |
| 100001 | 验签参数不完整 |
| 100002 | UTC 格式错误 |
| 100003 | UTC 超时 |
| 100004 | 验签失败 |
| 100005 | 签名一致性校验失败 |
| 100006 | 签名已过期 |
| 100012 | 接口请求频繁，请稍后再试 |
| 200001 | 请求参数为空 |
| 200002 | 填充参数默认值失败 |
| 200003 | 非法的请求参数 |
| 300001 | 引擎未知异常 |
| 300002 | 调用引擎返回失败结果 |
| 400001 | 响应成功，服务解析响应失败 |
| 400002 | 服务解析时发生未知错误 |

## 🔧 调试功能

### SkyxuController 调试

访问 `SkyxuController::index` 可以进行功能测试：

```php
// 路由示例
Route::get('/debug/ckm', [SkyxuController::class, 'index']);
```

调试功能包括：
- 错误类型判断测试
- UTC格式测试
- 自动创建测试会议笔记
- 详细的错误信息显示
- 浏览器友好的格式化输出

### 开启调试模式

```php
// 开启调试模式，会输出请求详情
$result = XfyunCkm::aggregate()
    ->debug()
    ->requestSummary($paragraphs);
```

## 🎯 高级用法

### 自定义UTC格式

```php
// 如果遇到UTC格式错误，可以尝试其他格式
$result = XfyunCkm::aggregate()
    ->setUtcFormat('Y-m-d H:i:s')
    ->requestSummary($paragraphs);
```

### 带回调URL的请求

```php
$result = XfyunCkm::aggregate()->requestComplete(
    $paragraphs,
    'http://your-server.com/api/ckm-callback'
);
```

### 检查任务状态

```php
$isCompleted = XfyunCkm::query()->isTaskCompleted($taskId);
$isSuccess = XfyunCkm::query()->isTaskSuccess($taskId);
```

## 📝 注意事项

1. **段落文本限制**：每个段落文本内容不能超过 10000 字符
2. **段落编号唯一性**：段落编号 (`pt`) 必须唯一标识一个段落
3. **时间单位**：所有时间参数使用毫秒 (ms) 为单位
4. **SSL验证**：生产环境建议启用 SSL 验证
5. **轮询间隔**：合理设置轮询间隔，避免频繁请求
6. **错误重试**：对于可重试错误，建议实现指数退避重试机制

## 🔄 版本更新

### v1.0.0 特性
- ✅ 完整的HMAC-SHA1签名认证
- ✅ 异步会议内容处理
- ✅ 任务结果查询和轮询
- ✅ 企业级异常处理
- ✅ MEETING类型笔记支持
- ✅ 与ArticleKnowledgeStrategy集成
- ✅ 浏览器友好的调试功能

## 🤝 技术支持

### 常见问题

**Q: UTC格式错误怎么办？**
A: 当前使用的是与Java demo一致的格式 `Y-m-d\TH:i:sO`，如果仍有问题可以尝试其他格式。

**Q: 如何处理请求频繁错误？**
A: 错误码100012表示请求频繁，这是可重试错误，建议实现指数退避重试。

**Q: 会议笔记总结失败怎么办？**
A: 检查text_record数据格式，确保包含必要的字段（text, start, end等）。

### 联系方式

- **开发者**: Claude 4.0 sonnet
- **最后更新**: 2025-06-20
- **版本**: v1.0.0

---

*本文档提供了XfyunCkm Package的完整使用指南，如有疑问请参考代码注释或联系开发团队。*
