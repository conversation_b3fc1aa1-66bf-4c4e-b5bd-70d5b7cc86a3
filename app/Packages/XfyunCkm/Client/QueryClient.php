<?php

namespace App\Packages\XfyunCkm\Client;

use App\Packages\XfyunCkm\BaseClient;
use App\Packages\XfyunCkm\XfyunCkmException;

class QueryClient extends BaseClient
{
    /**
     * 任务状态常量
     */
    const TASK_STATUS_SUCCESS = 0;     // 成功
    const TASK_STATUS_RUNNING = 1;     // 进行中
    const TASK_STATUS_FAILED  = 9;     // 失败

    /**
     * 查询任务结果
     *
     * @param  string  $taskId  任务ID
     * @param  int  $type  查询类型，0代表查询聚合能力的全部结果
     * @return array 任务结果数据
     * @throws XfyunCkmException
     */
    public function getQueryResult(string $taskId, int $type = 0): array
    {
        $this->checkBaseConfig();
        $this->validateQueryParams($taskId, $type);

        $data = [
            'taskId' => $taskId,
            'type'   => $type,
        ];

        $response = $this->post('/ckm/v1/result/query', $data);

        return $this->formatQueryResult($response);
    }

    /**
     * 验证查询参数
     *
     * @param  string  $taskId
     * @param  int  $type
     * @throws XfyunCkmException
     */
    protected function validateQueryParams(string $taskId, int $type): void
    {
        if (empty($taskId)) {
            throw XfyunCkmException::validationError('taskId不能为空', ['taskId' => $taskId, 'type' => $type]);
        }

        if ($type !== 0) {
            throw XfyunCkmException::validationError(
                '当前仅支持type=0（查询聚合能力的全部结果）',
                ['taskId' => $taskId, 'type' => $type]
            );
        }
    }

    /**
     * 格式化查询结果
     *
     * @param  array  $response
     * @return array
     */
    protected function formatQueryResult(array $response): array
    {
        $data = $response['data'] ?? [];

        $result = [
            'taskStatus'  => $data['taskStatus'] ?? null,
            'sid'         => $response['sid'] ?? '',
            'isCompleted' => ($data['taskStatus'] ?? null) !== self::TASK_STATUS_RUNNING,
            'isSuccess'   => ($data['taskStatus'] ?? null) === self::TASK_STATUS_SUCCESS,
            'isFailed'    => ($data['taskStatus'] ?? null) === self::TASK_STATUS_FAILED,
        ];

        // 全文会议纪要 (fcn=4)
        if (isset($data['textSummaryInfo'])) {
            $result['textSummary'] = [
                'summary'    => $data['textSummaryInfo']['summary'] ?? '',
                'highlights' => $data['textSummaryInfo']['highlights'] ?? [],
            ];
        }

        // 篇章规整 (fcn=5)
        if (isset($data['regularityInfo'])) {
            $result['regularity'] = [
                'paragraphs' => $data['regularityInfo']['ps'] ?? [],
            ];
        }

        // 会议待办 (fcn=6)
        if (isset($data['meetingAgentInfo'])) {
            $result['meetingAgent'] = [
                'agents' => $data['meetingAgentInfo']['meetingAgents'] ?? [],
            ];
        }

        // 意群划分 (fcn=7)
        if (isset($data['senseSplitInfo'])) {
            $result['senseSplit'] = $data['senseSplitInfo'];
        }

        // 意群标题 (fcn=8)
        if (isset($data['senseTitleInfo'])) {
            $result['senseTitle'] = $data['senseTitleInfo'];
        }

        // 意群纪要 (fcn=9)
        if (isset($data['senseSummaryInfo'])) {
            $result['senseSummary'] = $data['senseSummaryInfo'];
        }

        return $result;
    }



    /**
     * 检查任务是否完成
     *
     * @param  string  $taskId
     * @return bool
     * @throws XfyunCkmException
     */
    public function isTaskCompleted(string $taskId): bool
    {
        $result = $this->getQueryResult($taskId);
        return $result['isCompleted'];
    }

    /**
     * 检查任务是否成功
     *
     * @param  string  $taskId
     * @return bool
     * @throws XfyunCkmException
     */
    public function isTaskSuccess(string $taskId): bool
    {
        $result = $this->getQueryResult($taskId);
        return $result['isSuccess'];
    }

    /**
     * 获取任务状态描述
     *
     * @param  int  $status
     * @return string
     */
    public function getTaskStatusDescription(int $status): string
    {
        switch ($status) {
            case self::TASK_STATUS_SUCCESS:
                return '成功';
            case self::TASK_STATUS_RUNNING:
                return '进行中';
            case self::TASK_STATUS_FAILED:
                return '失败';
            default:
                return '未知状态';
        }
    }

    /**
     * 快捷方法：仅获取会议纪要
     *
     * @param  string  $taskId
     * @return array|null
     * @throws XfyunCkmException
     */
    public function getTextSummary(string $taskId): ?array
    {
        $result = $this->getQueryResult($taskId);
        return $result['textSummary'] ?? null;
    }

    /**
     * 快捷方法：仅获取会议待办
     *
     * @param  string  $taskId
     * @return array|null
     * @throws XfyunCkmException
     */
    public function getMeetingAgent(string $taskId): ?array
    {
        $result = $this->getQueryResult($taskId);
        return $result['meetingAgent'] ?? null;
    }

    /**
     * 快捷方法：仅获取篇章规整结果
     *
     * @param  string  $taskId
     * @return array|null
     * @throws XfyunCkmException
     */
    public function getRegularity(string $taskId): ?array
    {
        $result = $this->getQueryResult($taskId);
        return $result['regularity'] ?? null;
    }
}
