<?php

namespace App\Packages\XfyunCkm;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;

class BaseClient
{
    protected string $appId;
    protected string $accessKeyId;
    protected string $accessKeySecret;
    protected string $baseUri;
    protected Client $client;
    protected array  $headers   = [];
    protected bool   $debug     = false;
    protected string $utcFormat = 'Y-m-d\TH:i:sO'; // Java格式：2025-06-20T03:30:31+0000

    /**
     * 讯飞CKM错误码映射
     */
    protected const ERROR_CODE_MAP = [
        '000000' => '服务成功',
        '999999' => '服务发生未知错误',
        '100001' => '验签参数不完整',
        '100002' => 'UTC 格式错误',
        '100003' => 'UTC 超时',
        '100004' => '验签失败',
        '100005' => '签名一致性校验失败',
        '100006' => '签名已过期',
        '100012' => '接口请求频繁，请稍后再试',
        '200001' => '请求参数为空',
        '200002' => '填充参数默认值失败',
        '200003' => '非法的请求参数',
        '300001' => '引擎未知异常',
        '300002' => '调用引擎返回失败结果，详细信息见 desc',
        '400001' => '响应成功，服务解析响应失败',
        '400002' => '服务解析时发生未知错误',
    ];

    public function __construct()
    {
        // 从环境变量或配置中获取认证信息
        $this->appId           = env('XFYUN_CKM_APP_ID', '');
        $this->accessKeyId     = env('XFYUN_CKM_ACCESS_KEY_ID', '');
        $this->accessKeySecret = env('XFYUN_CKM_ACCESS_KEY_SECRET', '');
        $this->baseUri         = env('XFYUN_CKM_BASE_URI', 'https://office-api-ckm-dx.iflyaisol.com');

        $this->client = new Client([
            'verify'  => false, // 开发环境可以禁用SSL验证
            'timeout' => 30,
        ]);

        $this->setDefaultHeaders();
    }

    /**
     * 设置默认请求头
     */
    protected function setDefaultHeaders(): void
    {
        $this->headers = [
            'Content-Type' => 'application/json',
            'x-traceid'    => $this->generateUuid(),
            'x-sn'         => $this->generateDeviceId(),
        ];
    }

    /**
     * 开启调试模式
     */
    public function debug(): self
    {
        $this->debug = true;
        return $this;
    }

    /**
     * 测试不同的UTC格式
     *
     * @param  string  $format  UTC时间格式
     * @return self
     */
    public function setUtcFormat(string $format): self
    {
        $this->utcFormat = $format;
        return $this;
    }

    /**
     * 生成签名
     *
     * @param  array  $params  需要签名的参数
     * @return string
     */
    protected function generateSignature(array $params): string
    {
        // 移除空值参数和signature参数本身
        $filteredParams = array_filter($params, function ($value, $key) {
            return $value !== '' && $value !== null && $key !== 'signature';
        }, ARRAY_FILTER_USE_BOTH);

        // 按键名ASCII码排序
        ksort($filteredParams);

        // 构建baseString
        $baseStringParts = [];
        foreach ($filteredParams as $key => $value) {
            $baseStringParts[] = $key.'='.urlencode($value);
        }
        $baseString = implode('&', $baseStringParts);

        // 使用HMAC-SHA1加密并Base64编码
        $signature = base64_encode(hash_hmac('sha1', $baseString, $this->accessKeySecret, true));

        return $signature;
    }

    /**
     * 构建带签名的URL
     *
     * @param  string  $path  API路径
     * @return string
     */
    protected function buildSignedUrl(string $path): string
    {
        // 使用可配置的UTC格式
        $utc  = gmdate($this->utcFormat);
        $uuid = $this->generateUuid();

        $params = [
            'appId'       => $this->appId,
            'accessKeyId' => $this->accessKeyId,
            'utc'         => $utc,
            'uuid'        => $uuid,
        ];

        $signature           = $this->generateSignature($params);
        $params['signature'] = $signature;

        $queryString = http_build_query($params);
        return $this->baseUri.$path.'?'.$queryString;
    }

    /**
     * 发送POST请求
     *
     * @param  string  $path  API路径
     * @param  array  $data  请求数据
     * @return array
     * @throws XfyunCkmException
     */
    protected function post(string $path, array $data = []): array
    {
        return $this->request('POST', $path, $data);
    }

    /**
     * 发送HTTP请求
     *
     * @param  string  $method  HTTP方法
     * @param  string  $path  API路径
     * @param  array  $data  请求数据
     * @return array
     * @throws XfyunCkmException
     */
    protected function request(string $method, string $path, array $data = []): array
    {
        try {
            $url     = $this->buildSignedUrl($path);
            $options = [
                'headers' => $this->headers,
            ];

            if (! empty($data)) {
                $options['json'] = $data;
            }

            if ($this->debug) {
                dump([
                    'method'  => $method,
                    'url'     => $url,
                    'headers' => $this->headers,
                    'data'    => $data,
                ]);
            }

            $response   = $this->client->request($method, $url, $options);
            $statusCode = $response->getStatusCode();
            $content    = $response->getBody()->getContents();
            if ($statusCode >= 200 && $statusCode < 300) {
                $result = json_decode($content, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw XfyunCkmException::responseError(
                        '响应JSON解析失败: '.json_last_error_msg(),
                        ['content' => $content],
                        $data
                    );
                }

                // 检查业务错误码
                if (isset($result['code']) && $result['code'] !== '000000') {
                    $errorCode = $result['code'];
                    $errorDesc = $result['desc'] ?? $this->getErrorDescription($errorCode);

                    throw XfyunCkmException::businessError(
                        $errorDesc,
                        (int) $errorCode,
                        $result,
                        $data
                    );
                }

                return $result;
            } else {
                throw XfyunCkmException::responseError(
                    "HTTP请求失败，状态码: {$statusCode}",
                    ['status_code' => $statusCode, 'content' => $content],
                    $data
                );
            }
        } catch (RequestException $e) {
            $message      = $e->getMessage();
            $responseData = null;

            if ($e->hasResponse()) {
                $responseBody = $e->getResponse()->getBody()->getContents();
                if (Str::isJson($responseBody)) {
                    $errorData    = json_decode($responseBody, true);
                    $message      = $errorData['desc'] ?? $errorData['message'] ?? $message;
                    $responseData = $errorData;
                }
            }

            throw new XfyunCkmException(
                "请求异常: {$message}",
                $e->getCode(),
                $e,
                XfyunCkmException::TYPE_REQUEST_ERROR,
                $responseData,
                $data
            );
        } catch (XfyunCkmException $e) {
            // 重新抛出XfyunCkmException
            throw $e;
        } catch (Exception $e) {
            throw new XfyunCkmException(
                "系统异常: ".$e->getMessage(),
                $e->getCode(),
                $e,
                XfyunCkmException::TYPE_REQUEST_ERROR,
                null,
                $data
            );
        }
    }

    /**
     * 生成UUID
     *
     * @return string
     */
    protected function generateUuid(): string
    {
        return (string) Str::uuid();
    }

    /**
     * 生成设备ID
     *
     * @return string
     */
    protected function generateDeviceId(): string
    {
        return 'device_'.md5(gethostname().time());
    }

    /**
     * 获取错误码描述
     *
     * @param  string  $errorCode
     * @return string
     */
    protected function getErrorDescription(string $errorCode): string
    {
        return self::ERROR_CODE_MAP[$errorCode] ?? "未知错误码: {$errorCode}";
    }

    /**
     * 判断是否为可重试的错误
     *
     * @param  string  $errorCode
     * @return bool
     */
    protected function isRetryableError(string $errorCode): bool
    {
        $retryableErrors = ['100012', '300001', '999999']; // 请求频繁、引擎异常、未知错误
        return in_array($errorCode, $retryableErrors);
    }

    /**
     * 判断是否为认证相关错误
     *
     * @param  string  $errorCode
     * @return bool
     */
    protected function isAuthError(string $errorCode): bool
    {
        $authErrors = ['100001', '100002', '100003', '100004', '100005', '100006'];
        return in_array($errorCode, $authErrors);
    }

    /**
     * 检查基础配置
     *
     * @throws XfyunCkmException
     */
    protected function checkBaseConfig(): void
    {
        if (empty($this->appId)) {
            throw XfyunCkmException::configError('缺少appId配置');
        }
        if (empty($this->accessKeyId)) {
            throw XfyunCkmException::configError('缺少accessKeyId配置');
        }
        if (empty($this->accessKeySecret)) {
            throw XfyunCkmException::configError('缺少accessKeySecret配置');
        }
        if (empty($this->baseUri)) {
            throw XfyunCkmException::configError('缺少baseUri配置');
        }
    }
}
