# 讯飞会议知识管理服务 (XfyunCkm) Package

这个 Package 提供了与讯飞会议知识管理服务 (CKM) API 的集成，支持会议内容的智能分析和处理。

## 功能特性

- 🎯 **能力聚合**: 一次性请求生成智能总结、章节概要和待办事项
- 📊 **结果查询**: 查询异步任务的处理结果
- 🔐 **安全认证**: 完整的 HMAC-SHA1 签名认证机制
- 🚀 **异步处理**: 支持异步任务提交和轮询查询
- 🛡️ **异常处理**: 统一的错误处理和异常机制

## 环境配置

在 `.env` 文件中添加以下配置：

```env
# 讯飞CKM服务配置
XFYUN_CKM_APP_ID=your_app_id
XFYUN_CKM_ACCESS_KEY_ID=your_access_key_id
XFYUN_CKM_ACCESS_KEY_SECRET=your_access_key_secret
XFYUN_CKM_BASE_URI=https://office-api-ckm-dx.iflyaisol.com
```

## 基本使用

### 1. 提交会议内容处理请求

```php
use App\Packages\XfyunCkm\XfyunCkm;

// 准备会议段落数据
$paragraphs = [
    [
        'ws' => '这是第一段会议内容...',
        'pt' => '1',
        'pg' => '1000',  // 可选：段首时间(ms)
        'pd' => '11000', // 可选：段尾时间(ms)
        'prl' => '1'     // 可选：角色ID
    ],
    [
        'ws' => '这是第二段会议内容...',
        'pt' => '2',
    ]
];

// 指定需要的功能
$features = [
    '4', // 全文会议纪要
    '5', // 篇章规整
    '6'  // 会议待办
];

try {
    // 提交处理请求
    $result = XfyunCkm::aggregate()->requestAggregate(
        $paragraphs, 
        $features, 
        'http://your-server.com/api/ckm-callback' // 可选回调URL
    );
    
    $taskId = $result['taskId'];
    $estimatedCost = $result['estimatedCost']; // 预计耗时(ms)
    
    echo "任务已提交，TaskID: {$taskId}，预计耗时: {$estimatedCost}ms";
    
} catch (\App\Packages\XfyunCkm\XfyunCkmException $e) {
    echo "请求失败: " . $e->getMessage();
}
```

### 2. 查询任务结果

```php
use App\Packages\XfyunCkm\XfyunCkm;

try {
    // 查询任务结果
    $result = XfyunCkm::query()->getQueryResult($taskId);
    
    if ($result['isCompleted']) {
        if ($result['isSuccess']) {
            // 获取会议纪要
            if (isset($result['textSummary'])) {
                $summary = $result['textSummary']['summary'];
                $highlights = $result['textSummary']['highlights'];
                echo "会议总结: {$summary}";
            }
            
            // 获取待办事项
            if (isset($result['meetingAgent'])) {
                $agents = $result['meetingAgent']['agents'];
                echo "待办事项: " . implode(', ', $agents);
            }
            
            // 获取篇章规整结果
            if (isset($result['regularity'])) {
                $paragraphs = $result['regularity']['paragraphs'];
                echo "规整后段落数: " . count($paragraphs);
            }
        } else {
            echo "任务处理失败";
        }
    } else {
        echo "任务仍在处理中...";
    }
    
} catch (\App\Packages\XfyunCkm\XfyunCkmException $e) {
    echo "查询失败: " . $e->getMessage();
}
```

### 3. 轮询查询（自动等待完成）

```php
use App\Packages\XfyunCkm\XfyunCkm;

try {
    // 轮询查询直到任务完成（最多尝试30次，每次间隔5秒）
    $result = XfyunCkm::query()->pollQueryResult($taskId, 30, 5);
    
    if ($result['isSuccess']) {
        echo "任务处理成功！";
        // 处理结果...
    } else {
        echo "任务处理失败";
    }
    
} catch (\App\Packages\XfyunCkm\XfyunCkmException $e) {
    echo "轮询查询失败: " . $e->getMessage();
}
```

## 快捷方法

### 能力聚合快捷方法

```php
// 仅请求会议纪要
$result = XfyunCkm::aggregate()->requestSummary($paragraphs);

// 仅请求篇章规整
$result = XfyunCkm::aggregate()->requestRegularity($paragraphs);

// 仅请求会议待办
$result = XfyunCkm::aggregate()->requestMeetingAgent($paragraphs);

// 请求完整处理（纪要+规整+待办）
$result = XfyunCkm::aggregate()->requestComplete($paragraphs);
```

### 结果查询快捷方法

```php
// 仅获取会议纪要
$summary = XfyunCkm::query()->getTextSummary($taskId);

// 仅获取会议待办
$agents = XfyunCkm::query()->getMeetingAgent($taskId);

// 仅获取篇章规整结果
$regularity = XfyunCkm::query()->getRegularity($taskId);

// 检查任务状态
$isCompleted = XfyunCkm::query()->isTaskCompleted($taskId);
$isSuccess = XfyunCkm::query()->isTaskSuccess($taskId);
```

## 功能码说明

| 功能码 | 功能名称 | 说明 |
|--------|----------|------|
| 4 | 全文会议纪要 | 生成整个会议的总结和要点 |
| 5 | 篇章规整 | 对会议内容进行结构化整理 |
| 6 | 会议待办 | 提取会议中的待办事项 |
| 7 | 意群划分 | 将内容按意群进行划分 |
| 8 | 意群标题生成 | 为意群生成标题 |
| 9 | 意群会议纪要生成 | 为每个意群生成纪要 |

## 调试模式

```php
// 开启调试模式，会输出请求详情
$result = XfyunCkm::aggregate()
    ->debug()
    ->requestSummary($paragraphs);
```

## 异常处理

所有方法都可能抛出 `XfyunCkmException` 异常，建议使用 try-catch 进行处理：

```php
try {
    $result = XfyunCkm::aggregate()->requestSummary($paragraphs);
} catch (\App\Packages\XfyunCkm\XfyunCkmException $e) {
    // 获取详细错误信息
    $errorDetails = $e->getErrorDetails();

    // 根据错误类型进行不同处理
    if ($e->isAuthError()) {
        // 认证错误，检查配置
        logger()->error('CKM认证失败', $errorDetails);
    } elseif ($e->isParameterError()) {
        // 参数错误，检查请求参数
        logger()->error('CKM参数错误', $errorDetails);
    } elseif ($e->isRetryable()) {
        // 可重试错误，稍后重试
        logger()->warning('CKM请求失败，可重试', $errorDetails);
    } else {
        // 其他错误
        logger()->error('CKM请求失败', $errorDetails);
    }
}
```

### 错误类型

XfyunCkmException 提供了多种错误类型判断方法：

- `isConfigError()` - 配置错误
- `isValidationError()` - 验证错误
- `isRequestError()` - 请求错误
- `isResponseError()` - 响应错误
- `isTimeoutError()` - 超时错误
- `isBusinessError()` - 业务错误
- `isAuthError()` - 认证错误
- `isParameterError()` - 参数错误
- `isRetryable()` - 是否可重试

### 错误码说明

| 错误码 | 描述 |
|--------|------|
| 000000 | 服务成功 |
| 999999 | 服务发生未知错误 |
| 100001 | 验签参数不完整 |
| 100002 | UTC 格式错误 |
| 100003 | UTC 超时 |
| 100004 | 验签失败 |
| 100005 | 签名一致性校验失败 |
| 100006 | 签名已过期 |
| 100012 | 接口请求频繁，请稍后再试 |
| 200001 | 请求参数为空 |
| 200002 | 填充参数默认值失败 |
| 200003 | 非法的请求参数 |
| 300001 | 引擎未知异常 |
| 300002 | 调用引擎返回失败结果 |
| 400001 | 响应成功，服务解析响应失败 |
| 400002 | 服务解析时发生未知错误 |

## 注意事项

1. 段落文本内容 (`ws`) 长度不能超过 10000 字符
2. 段落编号 (`pt`) 必须唯一标识一个段落
3. 所有时间参数使用毫秒 (ms) 为单位
4. 建议在生产环境中启用 SSL 验证
5. 合理设置轮询间隔，避免频繁请求
