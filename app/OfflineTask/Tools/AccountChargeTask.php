<?php

namespace App\OfflineTask\Tools;

use App\Models\OfflineTask;
use App\Models\OfflineTaskLog;
use App\Models\User;
use App\OfflineTask\BaseClass\BaseTask;
use Exception;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Models\AccountLog;

class AccountChargeTask extends BaseTask
{
    public string    $name    = 'AccountCharge';
    public int       $minutes = 20;
    protected string $cnName  = '账户变动监测';

    public function canJoin(User $user, array $params)
    {
        $task = OfflineTask::ofUser($user)
            ->where('status', OfflineTask::STATUS_ING)
            ->where('command', $this->name)
            ->first();
        if ($task) {
            if ($task->params['account'] != $params['account']) {
                return true;
            }
            if ($task->params['type'] != $params['type']) {
                return true;
            }
            return '任务已经创建：'.$task->id;
        } else {
            return true;
        }
    }

    /**
     * 给予到MCP工具中的说明
     *
     * @return string
     */
    public function description(): string
    {
        return <<<EOF
            ```
            用户积分账户变动的监测
            command:$this->name
            params:
              - account:账户类型[in:score,balance],score:积分,balance:余额 
              - type:监测类型[in:in,out],in:收入,out:支出
            ```
        EOF;
    }

    public function checkLog(OfflineTaskLog $log)
    {
        if ($log->result['status']) {
            $params = $log->run_params;
            $amount = $log->result['data']['amount'] ?? 0;
            if ($amount > 0) {
                $account = AccountType::ACCOUNT_TYPE_MAP[$params['account']] ?? '';
                $prefix  = match ($params['type']) {
                    'in' => '收入',
                    'out' => '消耗',
                    default => ''
                };
                return sprintf("账户：%s,%s:%s",
                    $account,
                    $prefix,
                    $amount
                );
            } else {
                return '无异动';
            }
        } else {
            return '错误：'.$log->result['message'];
        }
    }

    /**
     * 任务执行过程
     *
     * @return void
     */
    public function run(): void
    {
        try {
            #Todo 任务执行过程
            $params  = $this->task->params;
            $account = $params['account'];
            $type    = $params['type'];
            $startAt = $params['start_at'] ?? $this->task->created_at->toDateTimeString();
            if (! $this->task->user) {
                $this->task->delete();
                throw new Exception('用户不存在');
            }
            $model                    = AccountLog::where('account_id', $this->task->user->account->id)
                ->where('type', $account)
                ->where('created_at', '>', $startAt);
            $model                    = match ($type) {
                'in' => $model->where('amount', '>', 0),
                'out' => $model->where('amount', '<', 0),
                default => 0,
            };
            $chargeAmount             = abs($model->sum('amount'));
            $this->params['start_at'] = now()->toDateTimeString();
            $this->success([
                'amount' => $chargeAmount,
            ]);
            if ($chargeAmount > 0) {
                $this->notification();
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->setLogs();
    }

}
