<?php

namespace App\OfflineTask\Tools;

use App\Models\User;
use App\Models\OfflineTask;
use App\Models\OfflineTaskLog;
use App\OfflineTask\BaseClass\BaseTask;
use Exception;

class {{ className }} extends BaseTask
{
    public string $name = '{{ taskName }}';
    public int    $minutes = 20;//运行间隔
    protected string $cnName  = '任务名称';

    /**
     * 是否可以加入任务,主要用于去重
     *
     * @param User $user
     * @param array $params
     * @return bool
     */
    public function canJoin(User $user, array $params): bool
    {
        return true;
    }

    /**
     * 给予到MCP工具中的说明
     *
     * @return string
     */
    public function description(): string
    {
        return '';
    }

    /**
     * 检查日志,输出描述内容
     *
     * @param OfflineTaskLog $log
     * @return string
     */
    public function checkLog(OfflineTaskLog $log): string
    {
        return '';
    }

    /**
     * 任务执行过程
     *
     * @return void
     */
    public function run(): void
    {
        try {
            #Todo 任务执行过程
            $this->success(['abc' => 'abc']);//记录成功数据
            if (true) {
                $this->notification();//执行通知
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->setLogs();//产生记录
    }

}
