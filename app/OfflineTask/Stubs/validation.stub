<?php

namespace App\OfflineTask\Validations;

use App\OfflineTask\BaseClass\BaseValidation;


class {{ className }} extends BaseValidation
{

    /**
     * 表单验证，请认真填写MCP字段注入判定
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
        ];
    }

    /**
     * 错误提示，针对大模型，尽量填写LLM能够判定的内容
     *
     * @return array
     */
    protected function messages(): array
    {
        return [

        ];
    }
}