<?php

namespace App\Enums;

use App\Traits\EnumMethods;

enum ApplyStatus: string
{
    use EnumMethods;

    case INIT   = 'init';
    case REVIEW = 'review';
    case PASS   = 'pass';
    case REJECT = 'reject';

    const STATUS_MAP = [
        self::INIT->value   => '申请中',
        self::REVIEW->value => '审核中',
        self::PASS->value   => '审核通过',
        self::REJECT->value => '驳回',
    ];

    const LABEL_MAP = [
        self::INIT->value   => 'default',
        self::REVIEW->value => 'warning',
        self::PASS->value   => 'success',
        self::REJECT->value => 'danger',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}