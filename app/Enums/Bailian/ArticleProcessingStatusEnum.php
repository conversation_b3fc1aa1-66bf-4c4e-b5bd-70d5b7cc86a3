<?php

namespace App\Enums\Bailian;

use App\Traits\EnumMethods;

enum ArticleProcessingStatusEnum: string
{
    use EnumMethods;

    case PENDING    = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED  = 'completed';
    case FAILED     = 'failed';

    const STATUS_MAP = [
        self::PENDING->value    => '等待处理',
        self::PROCESSING->value => '处理中',
        self::COMPLETED->value  => '已完成',
        self::FAILED->value     => '处理失败',
    ];

    const LABEL_MAP = [
        self::PENDING->value    => 'warning',
        self::PROCESSING->value => 'primary',
        self::COMPLETED->value  => 'success',
        self::FAILED->value     => 'danger',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }

    /**
     * 是否为最终状态
     */
    public function isFinal(): bool
    {
        return in_array($this, [self::COMPLETED, self::FAILED]);
    }

    /**
     * 是否可以重新处理
     */
    public function canReprocess(): bool
    {
        return in_array($this, [self::FAILED, self::COMPLETED]);
    }
}
