<?php

namespace App\Enums\Bailian;

enum ArticleProcessingStatusEnum: string
{
    case PENDING    = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED  = 'completed';
    case FAILED     = 'failed';

    /**
     * 获取状态描述
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::PENDING => '等待处理',
            self::PROCESSING => '处理中',
            self::COMPLETED => '已完成',
            self::FAILED => '处理失败',
        };
    }

    /**
     * 获取状态颜色（用于前端显示）
     */
    public function getColor(): string
    {
        return match ($this) {
            self::PENDING => 'orange',
            self::PROCESSING => 'blue',
            self::COMPLETED => 'green',
            self::FAILED => 'red',
        };
    }

    /**
     * 是否为最终状态
     */
    public function isFinal(): bool
    {
        return in_array($this, [self::COMPLETED, self::FAILED]);
    }

    /**
     * 是否可以重新处理
     */
    public function canReprocess(): bool
    {
        return in_array($this, [self::FAILED, self::COMPLETED]);
    }
}
