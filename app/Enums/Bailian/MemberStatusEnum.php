<?php

namespace App\Enums\Bailian;

use App\Traits\EnumMethods;

enum MemberStatusEnum: string
{
    use EnumMethods;

    case PENDING  = 'pending';
    case APPROVED = 'approved';
    case REJECTED = 'rejected';

    const STATUS_MAP = [
        self::PENDING->value  => '待审核',
        self::APPROVED->value => '通过',
        self::REJECTED->value => '拒绝',
    ];

    const LABEL_MAP = [
        self::PENDING->value  => 'default',
        self::APPROVED->value => 'success',
        self::REJECTED->value => 'danger',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}