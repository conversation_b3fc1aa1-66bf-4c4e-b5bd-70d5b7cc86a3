<?php

namespace App\Enums\Bailian;

use App\Traits\EnumMethods;

enum ItemSyncStatusEnum: string
{
    use EnumMethods;

    // 同步状态常量
    case PENDING = 'pending';
    case SYNCING = 'syncing';
    case SYNCED  = 'synced';
    case FAILED  = 'failed';

    // 删除状态常量
    case DELETING = 'deleting';

    const STATUS_MAP = [
        self::PENDING->value  => '待同步',
        self::SYNCING->value  => '同步中',
        self::SYNCED->value   => '已同步',
        self::FAILED->value   => '同步失败',
        self::DELETING->value => '删除中',
    ];

    const LABEL_MAP = [
        self::PENDING->value  => 'info',
        self::SYNCING->value  => 'primary',
        self::SYNCED->value   => 'success',
        self::FAILED->value   => 'danger',
        self::DELETING->value => 'warning',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}