<?php

namespace App\Providers;

use App\Admin\Livewire\CleanCache;
use App\Admin\Livewire\Notifications;
use App\Enums\ApplyStatus;
use App\Models\AiUnifyAsset;
use App\Models\AppAiSearch;
use App\Models\BailianArticle;
use App\Models\BailianDirectory;
use App\Models\BailianFile;
use App\Models\BailianKnowledge;
use App\Models\Business;
use App\Models\DrawAudio;
use App\Models\DrawImage;
use App\Models\DrawVideo;
use App\Models\FastgptKnowledgeArticle;
use App\Models\MsgWeb;
use App\Models\Note;
use App\Models\PluginJmDraw;
use App\Models\PluginKeLing;
use App\Models\PluginMjDraw;
use App\Models\PluginViduAudioDraw;
use App\Models\PluginViduDraw;
use App\Models\PublishMedium;
use App\Models\RechargeOrder;
use App\Models\User;
use Dcat\Admin\Models\Administrator;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Intervention\Image\ImageManager;
use Livewire\Livewire;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ImageManager::class, function () {
            return ImageManager::withDriver(config('image.driver'));
        });
    }

    public function boot(): void
    {
        Relation::morphMap([
            'user'              => User::class,
            'admin'             => Administrator::class,
            'business'          => Business::class,
            'recharge_order'    => RechargeOrder::class,
            'image_draw'        => DrawImage::class,
            'video_draw'        => DrawVideo::class,
            'message_web'       => MsgWeb::class,
            'search_web'        => AppAiSearch::class,
            'audio_draw'        => DrawAudio::class,
            'plugin_keling'     => PluginKeLing::class,
            'plugin_mj'         => PluginMjDraw::class,
            'plugin_jm'         => PluginJmDraw::class,
            'note'              => Note::class,
            'publish_medium'    => PublishMedium::class,
            'unify_asset'       => AiUnifyAsset::class,
            'fastgpt_article'   => FastgptKnowledgeArticle::class,
            'vidu_draw'         => PluginViduDraw::class,
            'bailian_article'   => BailianArticle::class,
            'bailian_file'      => BailianFile::class,
            'bailian_directory' => BailianDirectory::class,
            'bailian_knowledge' => BailianKnowledge::class,
            'plugin_vidu_audio' => PluginViduAudioDraw::class,
        ]);
        $this->registerConfig();
        $this->bootBlueprint();
        $this->bootAdminLivewire();
    }

    private function registerConfig(): void
    {
        $this->mergeConfigFrom(
            app_path('OfflineTask/TaskConfig.php'), 'offline.task'
        );
    }

    private function bootBlueprint(): void
    {
        Blueprint::macro('cover', function () {
            return $this->string('cover')->nullable()->comment('封面图片');
        });
        Blueprint::macro('pictures', function () {
            return $this->json('pictures')->nullable()->comment('轮播图');
        });
        Blueprint::macro('user', function () {
            return $this->unsignedBigInteger('user_id')->index()
                ->comment('用户ID');
        });
        Blueprint::macro('easyStatus', function () {
            return $this->boolean('status')
                ->default(0)
                ->index();
        });
        Blueprint::macro('attachments', function () {
            return $this->json('attachments')->nullable()->comment('附件');
        });
        Blueprint::macro('no', function () {
            return $this->string('no', 32)->unique()->comment('单据编号');
        });
        Blueprint::macro('remark', function () {
            return $this->text('remark')->nullable()->comment('备注信息');
        });
        Blueprint::macro('approver', function () {
            $this->string('approver_type')
                ->nullable();
            $this->unsignedBigInteger('approver_id')
                ->nullable();
            $this->index(['approver_type', 'approver_id']);
        });
        Blueprint::macro('easyApply', function () {
            $this->enum('apply_status', ApplyStatus::values())
                ->index()
                ->default(ApplyStatus::INIT);
            $this->string('apply_text')
                ->comment('申请说明')
                ->nullable();
            $this->string('reject_reason')
                ->nullable()
                ->comment('拒绝原因');
            $this->string('reviewer_type')
                ->nullable();
            $this->unsignedBigInteger('reviewer_id')
                ->nullable();
            $this->dateTime('passed_at')
                ->nullable()
                ->comment('通过时间');
            $this->dateTime('rejected_at')
                ->nullable()
                ->comment('驳回时间');

            $this->index(['reviewer_type', 'reviewer_id']);
        });
    }

    public function bootAdminLivewire(): void
    {
        Livewire::component('admin.notifications', Notifications::class);
        Livewire::component('admin.clean-cache', CleanCache::class);
        Livewire::setScriptRoute(function ($handle) {
            return Route::get('/vendor/livewire/livewire.min.js', $handle);
        });
        Livewire::setUpdateRoute(function ($handle) {
            return Route::post('/components/update', $handle)
                ->middleware('web');
        });
    }

}
