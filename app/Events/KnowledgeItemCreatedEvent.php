<?php

namespace App\Events;

use App\Models\BailianKnowledgeItem;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 知识库项目创建事件
 *
 * 当知识库项目被创建后触发，用于异步处理同步到阿里云等操作
 */
class KnowledgeItemCreatedEvent implements ShouldQueue
{
    use Dispatchable, SerializesModels;

    public string $connection = 'redis';
    public string $queue      = 'BaiLian';
    public int    $delay      = 2; // 延迟2秒执行

    public BailianKnowledgeItem $knowledgeItem;

    /**
     * 创建知识库项目创建事件
     *
     * @param  BailianKnowledgeItem  $knowledgeItem  创建的知识库项目
     */
    public function __construct(BailianKnowledgeItem $knowledgeItem)
    {
        $this->knowledgeItem = $knowledgeItem;
    }
}
