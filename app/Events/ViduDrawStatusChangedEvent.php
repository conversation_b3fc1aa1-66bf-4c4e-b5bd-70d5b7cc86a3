<?php

namespace App\Events;

use App\Models\PluginViduDraw;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ViduDrawStatusChangedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public PluginViduDraw $draw,
        public string $newStatus
    ) {
    }
} 