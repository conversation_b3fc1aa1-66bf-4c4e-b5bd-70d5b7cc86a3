<?php

namespace App\Events;

use App\Enums\ApplyStatus;
use App\Models\Model;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ApplyStatusChangedEvent
{
    use Dispatchable,
        SerializesModels;

    /**
     * 审核结果变更事件
     *
     * @param  \App\Models\Model  $model  审核模型
     * @param  \App\Enums\ApplyStatus  $status  变更后的状态
     */
    public function __construct(public Model $model, public ApplyStatus $status)
    {
    }
}