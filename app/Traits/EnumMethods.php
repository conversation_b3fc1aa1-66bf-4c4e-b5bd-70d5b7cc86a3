<?php

namespace App\Traits;

trait EnumMethods
{
    /**
     * Notes   : 获取所有枚举的名称
     *
     * @Date   : 2023/4/3 16:00
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public static function keys(): array
    {
        return array_column(self::cases(), 'name');
    }

    /**
     * Notes   : 获取所有枚举项的值
     *
     * @Date   : 2023/4/3 11:19
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Notes   : 列出KEY-VALUE的键值对
     *
     * @Date   : 2023/4/3 11:19
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public static function forSelect(): array
    {
        return array_combine(
            array_column(self::cases(), 'value'),
            array_column(self::cases(), 'name')
        );
    }

    public static function valuesToString(): string
    {
        return implode(',', self::values());
    }

    public static function forSelectToString(): array
    {
        return array_map(function ($item) {
            return [
                'value' => $item->value,
                'name'  => $item->toString(),
            ];
        }, self::cases());
    }

}
