<?php

namespace App\Traits;

use App\Exceptions\ValidatorException;
use App\Models\CompanyStaff;
use App\Models\CompanyUser;
use App\Models\Knowledge;
use App\Models\SystemConfig;
use App\Packages\Knowledge\Client;
use Illuminate\Support\Str;

trait CheckKnowledge
{
    public function checkKnowledgeByDatasetId($userId, $datasetId)
    {
        $knowledge = Knowledge::query()
            ->where('kb_id', $datasetId)
            ->where('is_deleted', 0)
            ->first();

        $this->checkKnowledge($userId, $knowledge);

        return $knowledge;
    }

    public function checkKnowledge($userId, $knowledge, $is_work = 0, $is_check = 1, bool $is_role = true)
    {
        if (! $knowledge) {
            throw new ValidatorException("该知识库不存在");
        }

        if ($knowledge->company_id == 0 && $knowledge->uid != $userId) {
            throw new ValidatorException("无权限查看此知识库");
        }

        if ($knowledge->company_id != 0) {
            $companyUser = CompanyUser::query()
                ->where('uid', $userId)
                ->where('company_id', $knowledge->company_id)
                ->where('is_work', $is_work)
                ->where('is_check', $is_check)
                ->first();
            if (! $companyUser) {
                throw new ValidatorException("该公司未查询到用户信息");
            }
            if ($is_role) {
                $role = CompanyStaff::getRole($userId, $knowledge->company_id, 1);
                if (! $role) {
                    throw new ValidatorException("权限不足");
                }
            }
        }
    }

    public function chatDo($user, $assistant, $conversationId = '', $content = '')
    {
        $chatMessage                                                                                = config('chatMessage');
        $chatMessage['model_config']['pre_prompt']                                                  = $assistant->prologue;
        $chatMessage['model_config']['dataset_configs']['datasets']['datasets'][0]['dataset']['id'] = $assistant->kb_id;
        $chatMessage['model_config']['appId']                                                       = $assistant->app_id;
        $chatMessage['model_config']['model']['name']                                               = $assistant->model_id;
        $chatMessage['conversation_id']                                                             = $conversationId ?? '';
        $chatMessage['query']                                                                       = $content;

        $client        = new Client();
        $authorization = $client->getAuthorization();
        $authorization = Str::replace('Bearer ', '', $authorization);
        $header        = [
            'Authorization' => 'Bearer '.$authorization,
            'Content-Type'  => 'application/json',
        ];

        $KnowledgeUrl = SystemConfig::getValue('KnowledgeUrl');
        if (! $KnowledgeUrl) {
            throw new ValidatorException('缺少接口地址');
        }
        $url = $KnowledgeUrl.'/console/api/apps/'.$assistant->app_id.'/chat-messages';

        $result = streamHttp($url, $chatMessage, $header, $authorization);
        /*$result = [
            'header'  =>  $header,
            'body'  =>  $chatMessage,
            'url'   => "http://36.133.86.95:9094/console/api/apps/".$assistant['app_id']."/chat-messages",
            'stream'    =>  true
        ];*/
    }

}
