<?php

namespace App\Traits;

use App\Models\Business;
use App\Models\Contact;

trait CardTrait
{
    public function findFriend($uid)
    {
        //给我发的名片
        $contacts = Contact::where('dstid', $uid)
            ->where('status', 1)
            ->latest()
            ->get();

        $ids = [];
        if ($contacts->isNotEmpty()) {
            foreach ($contacts as $key => $contact) {
                $dstContact = Contact::where('uid', $uid)
                    ->where('to_bid', $contact->from_bid)
                    ->where('status', 1)
                    ->where('dstid', $contact->uid)
                    ->first();

                if ($dstContact) {
                    $ids[] = $contact->from_bid;
                }
            }
        }

        return $ids;
    }

}
