<?php

namespace App\Traits;

use App\Exceptions\ValidatorException;
use App\Models\Enums\FastgptKnowledgeSetEnum;
use App\Models\FastgptApp;
use App\Models\FastgptKnowledge;
use App\Models\FastgptKnowledgeSet;
use App\Models\SystemConfig;
use App\Packages\FastGpt\FastGpt;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Str;

trait FastGptTrait
{

    /**
     * Notes: 检查用户是否可以创建知识库
     *
     * @Author: 玄尘
     * @Date: 2025/2/27 15:01
     * @param $user
     * @return true
     * @throws \App\Exceptions\ValidatorException
     */
    public function canCreateKnowledge($user)
    {
        $knowledgeCount             = $user->fastgptKnowledges()->count();
        $fastgpt_user_knowledge_num = SystemConfig::getValue('fastgpt_user_knowledge_num', 10);
        if ($knowledgeCount >= $fastgpt_user_knowledge_num) {
            throw new ValidatorException('知识库数量已达上限');
        }
        return true;
    }

    public function getChatId($len = 12)
    {
        return Str::random($len);
    }

    public function getAppById(string $app_id, bool $is_check_status = false)
    {
        $app = FastgptApp::where('app_id', $app_id)->first();
        if (! $app) {
            throw new ValidatorException('应用不存在');
        }

//        if ($is_check_status && $app->isDisabled()) {
//            throw new ValidatorException('应用不可使用');
//        }

        return $app;
    }

    /**
     * Notes: description
     *
     * @Author: 玄尘
     * @Date: 2024/12/18 10:17
     * @param  array  $items  数据项
     * @param  int  $currentPage  当前页
     * @param  int  $perPage  每页数量
     * @param  int  $total  数据总量
     */
    public function getPaginate(array $items, int $currentPage, int $perPage, int $total): LengthAwarePaginator
    {
        // 将数据包装成 LengthAwarePaginator
        return new LengthAwarePaginator(
            $items,        // 数据项
            $total,        // 数据总数
            $perPage,      // 每页数量
            $currentPage,  // 当前页码
            ['path' => request()->url(), 'query' => request()->query()] //
        );
    }

    /**
     * Notes: 检擦知识库
     *
     * @Author: 玄尘
     * @Date: 2024/12/27 15:18
     * @param $knowledge
     * @param  string  $is_status  是否校验状态
     * @return true
     * @throws \App\Exceptions\ValidatorException
     */
    public function checkKnowledge($knowledge, bool $is_check_status = false)
    {
        if (! $knowledge) {
            throw new ValidatorException('知识库不存在');
        }

        if ($is_check_status && $knowledge->isDisabled()) {
            throw new ValidatorException('知识库不可用');
        }
        return true;
    }

    public function checkKnowledgeById(int $knowledge_id)
    {
        $knowledge = FastgptKnowledge::find($knowledge_id);
        $this->checkKnowledge($knowledge);
        return $knowledge;
    }

    /**
     * Notes: 鉴权
     *
     * @Author: 玄尘
     * @Date: 2024/12/20 11:00
     * @param $request
     * @param $knowledge
     * @return true
     * @throws \App\Exceptions\ValidatorException
     */
    public function checkPermissionData($request, $knowledge): true
    {
        $user = $request->kernel->user();
        if (! $knowledge->canSetData($user)) {
            throw new ValidatorException('您没有权限');
        }

        return true;
    }

    /**
     * Notes: 补全模板数据
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 18:03
     * @param $template
     * @param $search
     * @param $value
     * @return mixed
     */
    public function resetChatTemplate($template, $search, $value)
    {
        $templateChat    = Str::replace($search, $value, json_encode($template));
        $decodedTemplate = json_decode($templateChat, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \RuntimeException('Failed to decode JSON: '.json_last_error_msg());
        }
        return $decodedTemplate;
    }

    private function prepareDatasetValues(array $pushData, string $datasetIds): array
    {
        $datasetValues = $this->formatDatasetIds($datasetIds);
        return $this->updateDatasetNodes($pushData, $datasetValues);
    }

    public function getDatasetIdsToString($ids)
    {
        if (is_string($ids)) {
            return $ids;
        }
        if (is_array($ids)) {
            return implode(',', $ids);
        }
        return '';
    }

    private function formatDatasetIds(string $datasetIds): array
    {
        return array_map(function ($id) {
            return ['datasetId' => $id];
        }, $this->str2Arr($datasetIds));
    }

    private function updateDatasetNodes(array $pushData, array $datasetValues): array
    {
        if (empty($datasetValues)) {
            return $pushData;
        }
        foreach ($pushData['nodes'] as &$node) {
            if ($node['flowNodeType'] == 'datasetSearchNode') {
                foreach ($node['inputs'] as &$input) {
                    if ($input['key'] === 'datasets') {
                        $input['value'] = $datasetValues;
                    }
                }
            }
        }
        return $pushData;
    }

    /**
     * Notes: description
     *
     * @Author: 玄尘
     * @Date: 2025/3/13 08:46
     * @param $set
     * @param $sourceKnowledge
     * @param $targetKnowledge
     * @return true
     * @throws \App\Exceptions\ValidatorException
     */
    public function transferKnowledge($set, $sourceKnowledge, $targetKnowledge)
    {
        if ($set->collection_id) {
            FastGpt::set()->deleteSet($set->collection_id);
        }
        switch ($set->type->value) {
            case FastgptKnowledgeSetEnum::EMPTY->value:
                $result = FastGpt::set()->setEmpty(
                    $targetKnowledge->dataset_id,
                    $set->name,
                    $set->input_data['type']
                );

                $set->collection_id = $result['data'];
                $set->knowledge_id  = $targetKnowledge->id;
                $set->save();

                break;
            case FastgptKnowledgeSetEnum::ARTICLE->value:
            case FastgptKnowledgeSetEnum::TEXT->value:
                $result = FastGpt::set()->setText(
                    $targetKnowledge->dataset_id,
                    $set->name,
                    $set->input_data['text'],
                    $set->input_data['trainingType']
                );

                $set->collection_id = $result['data']['collectionId'];
                $set->knowledge_id  = $targetKnowledge->id;
                $set->save();
                break;
            case FastgptKnowledgeSetEnum::LINK->value:
                $result             = FastGpt::set()->setLink(
                    $targetKnowledge->dataset_id,
                    $set->input_data['link'],
                    $set->input_data['trainingType']
                );
                $set->collection_id = $result['data']['collectionId'];
                $set->knowledge_id  = $targetKnowledge->id;
                $set->save();
                break;
            case FastgptKnowledgeSetEnum::FILE->value:
                $result             = FastGpt::set()->setFileData(
                    $targetKnowledge->dataset_id,
                    $set->input_data['file'],
                    $set->input_data['trainingType']
                );
                $set->collection_id = $result['data']['collectionId'];
                $set->knowledge_id  = $targetKnowledge->id;
                $set->save();
                break;
            case FastgptKnowledgeSetEnum::NOTE->value:
                $result = FastGpt::set()->setText(
                    $targetKnowledge->dataset_id,
                    $set->name,
                    $set->note->getContent(),
                    $set->input_data['trainingType']
                );

                $set->collection_id = $result['data']['collectionId'];
                $set->knowledge_id  = $targetKnowledge->id;
                $set->save();
                break;

            case FastgptKnowledgeSetEnum::DIRECTORY->value:
            default:
                $set->knowledge_id = $targetKnowledge->id;
                $set->save();
                break;
        }

        return true;
    }

    /**
     * Notes: 检查知识库id
     *
     * @Author: 玄尘
     * @Date: 2025/3/20 13:40
     * @param $ids
     * @return string|string[]
     */
    public function checkDatasetIds($ids)
    {
        if (empty($ids)) {
            throw new \RuntimeException('知识库id不能为空');
        }

        if (is_array($ids)) {
            return $this->str2Arr($ids);
        }

        $ids = $this->str2Arr($ids);

        $datasets = FastgptKnowledge::whereIn('dataset_id', $ids)->get();

        if ($datasets->isEmpty()) {
            throw new \RuntimeException('知识库不存在');
        }
        return $this->arr2str($datasets->pluck('dataset_id')->toArray());
    }

    public function str2Arr($ids)
    {
        return explode(',', str_replace('，', ',', $ids));
    }

    public function arr2str($ids)
    {
        return implode(',', $ids);
    }

    public function deleteSet($set)
    {
        // 先删除子集合
        $allSets = FastgptKnowledgeSet::query()
            ->where('parent_id', $set->id)
            ->orWhere('id', $set->id)
            ->get();
        foreach ($allSets as $childSet) {
            if ($childSet->collection_id) {
                FastGpt::set()->deleteSet($childSet->collection_id);
            }

            if ($childSet->type == FastgptKnowledgeSetEnum::NOTE && $childSet->note) {
                $childSet->note->delete();
            }
            if ($set->type == FastgptKnowledgeSetEnum::ARTICLE && $set->knowledgeArticle) {
                $set->knowledgeArticle->delete();
            }
            $childSet->delete();
        }
        return true;
    }

    public function updateSetTitle($set, $name)
    {
        $set->name = $name;
        $set->save();
        if ($set->collection_id) {
            $data   = [
                'id'   => $set->collection_id,
                'name' => $set->name,
            ];
            $result = FastGpt::set()->setUpdate($data);
        }
        //更新小记
        $note = $set->note;
        if ($note) {
            $note->title = $name;
            $note->save();
        }

        //更新文档
        $article = $set->knowledgeArticle;
        if ($article) {
            $article->title = $name;
            $article->save();
        }
        return true;
    }

}
