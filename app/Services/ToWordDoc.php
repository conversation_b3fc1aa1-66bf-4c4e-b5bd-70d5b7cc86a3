<?php

namespace App\Services;

use DOMDocument;
use Exception;
use PhpOffice\PhpWord\Element\Section;

class ToWordDoc
{
    public function __construct(protected Section $section, protected string $content)
    {
    }

    public function convert()
    {
        $this->processHtmlInOrder($this->section, $this->content);

        // 如果没有内容被添加，添加一个提示
        if (empty($html) || strlen(trim(strip_tags($html))) === 0) {
            $this->section->addText('警告：处理后的内容为空', [
                'name'       => '微软雅黑',
                'size'       => 14,
                'lineHeight' => 1.5,
                'color'      => 'red'
            ]);
        }
        return $this->section;
        // 尝试保存文件，添加调试信息
    }

    /**
     * 按照HTML内容的原始顺序处理元素
     */
    private function processHtmlInOrder($section, $html)
    {
        // 检查HTML内容是否为空
        if (empty(trim($html))) {
            $section->addText('内容为空，请检查输入的Markdown内容。', [
                'name'       => '微软雅黑',
                'size'       => 14,
                'lineHeight' => 1.5
            ]);
            return;
        }

        // 使用DOMDocument来正确解析HTML
        $dom           = new DOMDocument();
        $dom->encoding = 'UTF-8';

        // 添加错误处理
        $success = $dom->loadHTML(
            '<?xml encoding="UTF-8">'.$html,
            LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING
        );

        if (! $success) {
            $section->addText('HTML解析失败，原始内容：'.$html, [
                'name'       => '微软雅黑',
                'size'       => 14,
                'lineHeight' => 1.5
            ]);
            return;
        }
        foreach ($dom->childNodes as $child) {
            $this->processNode($section, $child);
        }
    }

    /**
     * 递归处理DOM节点
     */
    private function processNode($section, $node)
    {
        // 如果是文档节点，处理其子节点
        if ($node->nodeType === XML_DOCUMENT_NODE) {
            foreach ($node->childNodes as $child) {
                $this->processNode($section, $child);
            }
            return;
        }

        if ($node->nodeType === XML_TEXT_NODE) {
            // 处理文本节点
            $text = trim($node->textContent);
            if (! empty($text)) {
                $section->addText($text, [
                    'name'       => '微软雅黑',
                    'size'       => 10,
                    'lineHeight' => 1.5
                ], [
                    'keepNext'        => false,
                    'keepLines'       => false,
                    'pageBreakBefore' => false,
                    'indentLeft'      => 567  // 2个字符缩进 (14号字体约28.35磅，2个字符约567缇)
                ]);
            }
        } elseif ($node->nodeType === XML_ELEMENT_NODE) {
            $tagName = strtolower($node->tagName);

            switch ($tagName) {
                case 'h1':
                case 'h2':
                case 'h3':
                case 'h4':
                case 'h5':
                case 'h6':
                    $level = substr($tagName, 1);
                    $this->addHeading($section, $level, $node->textContent);
                    break;

                case 'img':
                    $imgHtml = $node->ownerDocument->saveHTML($node);
                    $this->addImageFromHtml($section, $imgHtml);
                    break;

                case 'ul':
                    $listHtml = '';
                    foreach ($node->childNodes as $child) {
                        if ($child->nodeType === XML_ELEMENT_NODE && strtolower($child->tagName) === 'li') {
                            $listHtml .= $child->ownerDocument->saveHTML($child);
                        }
                    }
                    $this->addListFromHtml($section, $listHtml);
                    break;

                case 'p':
                case 'div':
                case 'span':
                    // 对于段落和容器元素，递归处理子节点
                    foreach ($node->childNodes as $child) {
                        $this->processNode($section, $child);
                    }
                    break;

                case 'strong':
                case 'b':
                    // 处理加粗文本 - 如果只有纯文本则直接添加，否则递归处理子节点
                    if ($node->childNodes->length === 1 && $node->firstChild->nodeType === XML_TEXT_NODE) {
                        $text = trim($node->textContent);
                        if (! empty($text)) {
                            $section->addText($text, [
                                'bold'       => true,
                                'name'       => '微软雅黑',
                                'size'       => 14,
                                'lineHeight' => 1.5
                            ], [
                                'keepNext'        => false,
                                'keepLines'       => false,
                                'pageBreakBefore' => false,
                                'indentLeft'      => 567  // 2个字符缩进
                            ]);
                        }
                    } else {
                        // 有嵌套内容，递归处理子节点
                        foreach ($node->childNodes as $child) {
                            $this->processNode($section, $child);
                        }
                    }
                    break;

                case 'em':
                case 'i':
                    // 处理斜体文本 - 如果只有纯文本则直接添加，否则递归处理子节点
                    if ($node->childNodes->length === 1 && $node->firstChild->nodeType === XML_TEXT_NODE) {
                        $text = trim($node->textContent);
                        if (! empty($text)) {
                            $section->addText($text, [
                                'italic'     => true,
                                'name'       => '微软雅黑',
                                'size'       => 14,
                                'lineHeight' => 1.5
                            ], [
                                'keepNext'        => false,
                                'keepLines'       => false,
                                'pageBreakBefore' => false,
                                'indentLeft'      => 567  // 2个字符缩进
                            ]);
                        }
                    } else {
                        // 有嵌套内容，递归处理子节点
                        foreach ($node->childNodes as $child) {
                            $this->processNode($section, $child);
                        }
                    }
                    break;

                default:
                    foreach ($node->childNodes as $child) {
                        $this->processNode($section, $child);
                    }
                    break;
            }
        }
    }

    /**
     * 添加标题
     */
    private function addHeading($section, $level, $text)
    {
        $text     = strip_tags($text);
        $fontSize = max(16 - $level, 10);

        $section->addText($text, [
            'name'       => '微软雅黑',
            'size'       => $fontSize,
            'bold'       => true,
            'lineHeight' => 2.0  // 标题行间距2倍
        ], [
            'keepNext'        => false,
            'keepLines'       => false,
            'pageBreakBefore' => false
        ]);
    }

    /**
     * 从HTML标签中添加图片
     */
    private function addImageFromHtml($section, $imgTag)
    {
        // 提取图片URL
        if (preg_match('/src=["\']([^"\'>]+)["\']/', $imgTag, $srcMatch)) {
            $imagePath = $srcMatch[1];
            try {
                // 只处理外链图片
                if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                    $this->addImageWithA4Scaling($section, $imagePath);
                }
            } catch (Exception $e) {
            }
        }
    }

    /**
     * 添加图片并按A4纸张宽度70%等比例缩放居中显示
     */
    private function addImageWithA4Scaling($section, $imagePath)
    {
        // A4纸张宽度约210mm，70%约147mm，转换为像素约420px（按72dpi计算）
        $maxWidth  = 420; // A4宽度70%对应的像素值
        $maxHeight = 315; // 保持4:3比例的最大高度

        // 方法1: 尝试最基本的addImage调用（嵌入式）
        try {
            $section->addImage($imagePath, [
                'width'         => $maxWidth,
                'height'        => $maxHeight,
                'wrappingStyle' => 'square',
                'alignment'     => 'center'
            ]);
        } catch (Exception $e1) {
            // 方法2: 如果失败，尝试简化的尺寸参数（嵌入式）
            try {
                $section->addImage($imagePath, [
                    'width'         => $maxWidth,
                    'height'        => $maxHeight,
                    'wrappingStyle' => 'square'
                ]);
            } catch (Exception $e2) {
                // 方法3: 如果还是失败，尝试下载图片到临时文件并等比例缩放
                try {
                    $tempFile  = tempnam(sys_get_temp_dir(), 'phpword_img_');
                    $imageData = file_get_contents($imagePath);
                    if ($imageData !== false) {
                        file_put_contents($tempFile, $imageData);

                        // 获取图片实际尺寸进行等比例缩放
                        $imageInfo = getimagesize($tempFile);
                        if ($imageInfo) {
                            $originalWidth  = $imageInfo[0];
                            $originalHeight = $imageInfo[1];

                            // 计算等比例缩放尺寸
                            $scaleWidth  = $maxWidth / $originalWidth;
                            $scaleHeight = $maxHeight / $originalHeight;
                            $scale       = min($scaleWidth, $scaleHeight, 1); // 不放大，只缩小

                            $newWidth  = (int) ($originalWidth * $scale);
                            $newHeight = (int) ($originalHeight * $scale);

                            $section->addImage($tempFile, [
                                'width'         => $newWidth,
                                'height'        => $newHeight,
                                'wrappingStyle' => 'square',
                                'alignment'     => 'center'
                            ]);
                        } else {
                            $section->addImage($tempFile, [
                                'width'  => $maxWidth,
                                'height' => $maxHeight
                            ]);
                        }

                        unlink($tempFile);
                    } else {
                        throw new Exception('无法下载图片内容');
                    }
                } catch (Exception $e3) {
                }
            }
        }
    }

    /**
     * 从HTML中添加列表
     */
    private function addListFromHtml($section, $listContent)
    {
        $listItems = [];
        $liPattern = '/<li[^>]*>(.*?)<\/li>/is';

        if (preg_match_all($liPattern, $listContent, $liMatches)) {
            foreach ($liMatches[1] as $liContent) {
                $listItems[] = strip_tags($liContent);
            }
        }

        if (! empty($listItems)) {
            $section->addList($listItems, null, [
                'keepNext'        => false,
                'keepLines'       => false,
                'pageBreakBefore' => false
            ]);
        }
    }
}