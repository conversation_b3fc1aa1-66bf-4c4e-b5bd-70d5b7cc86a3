<?php

namespace App\Services;

use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeItem;
use App\Services\KnowledgeItemStrategies\KnowledgeItemStrategyFactory;
use DB;
use Exception;
use Log;

/**
 * 知识库项目服务类
 */
class KnowledgeItemService
{
    /**
     * 将项目添加到知识库
     *
     * @param  mixed  $item  项目实例
     * @param  int  $knowledgeId  知识库ID
     * @param  string|null  $categoryId  分类ID
     * @param  array  $options  选项
     * @return mixed
     * @throws Exception
     */
    public function addToKnowledgeBase($item, int $knowledgeId, ?string $categoryId = null, array $options = [])
    {
        try {
            DB::beginTransaction();

            // 获取知识库
            $knowledge = $this->getKnowledgeById($knowledgeId);

            // 如果没有提供分类ID，使用知识库默认分类
            if (! $categoryId) {
                $categoryId = $this->resolveCategoryId($item, $knowledge);
            }

            // 验证父级目录
            $this->validateParentDirectory($options['parent_id'] ?? null);

            // 获取策略并执行添加操作
            $strategy = KnowledgeItemStrategyFactory::createForModel($item);
            $strategy->addToKnowledgeBase($item, $knowledge, $categoryId, $options);

            // 更新知识库大小
            $this->updateKnowledgeSize($knowledge, $item->getSize());

            // 触发知识库内容更新事件,只有笔记和文档才触发
            if (in_array($item->getMorphClass(), ['bailian_article', 'bailian_file'])) {
                $item->fireKnowledgeContentUpdatedEvent($knowledge, 'created');
            }

            DB::commit();
            return $item;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('将项目添加到知识库失败', [
                'id'           => $item->id,
                'type'         => class_basename($item),
                'knowledge_id' => $knowledgeId,
                'error'        => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 从知识库中移除项目
     *
     * @param  mixed  $item  项目实例
     * @param  bool  $deleteSelf  是否删除项目本身
     * @return bool
     * @throws Exception
     */
    public function removeFromKnowledge($item, bool $deleteSelf = false): bool
    {
        try {
            // 获取策略并执行移除操作
            $strategy = KnowledgeItemStrategyFactory::createForModel($item);
            $strategy->removeFromKnowledge($item);

            // 删除项目本身
            if ($deleteSelf) {
                Log::info('删除项目本身', [
                    'is_self'        => $deleteSelf,
                    'class_basename' => class_basename($item)
                ]);
                $item->delete();
            }

            return true;
        } catch (Exception $e) {
            Log::error('从知识库中移除项目失败', [
                'id'    => $item->id,
                'type'  => class_basename($item),
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 根据ID从知识库中移除项目
     *
     * @param  string  $modelClass  模型类名
     * @param  int  $itemId  项目ID
     * @param  int|null  $storageId  存储ID
     * @param  bool  $deleteSelf  是否删除项目本身
     * @return bool
     * @throws Exception
     */
    public function removeFromKnowledgeById(
        string $modelClass,
        int $itemId,
        ?int $storageId = null,
        bool $deleteSelf = false
    ): bool {
        $strategy = KnowledgeItemStrategyFactory::createForClass($modelClass);
        return $strategy->removeFromKnowledgeById($itemId, $storageId, $deleteSelf);
    }

    /**
     * 获取知识库
     */
    private function getKnowledgeById(int $knowledgeId): BailianKnowledge
    {
        $knowledge = BailianKnowledge::find($knowledgeId);
        if (! $knowledge) {
            throw new Exception('知识库不存在');
        }
        return $knowledge;
    }

    /**
     * 获取分类ID
     */
    private function resolveCategoryId($item, BailianKnowledge $knowledge): string
    {
        $category = $item->category ?? null;
        if ($category && $category->bailian_id) {
            return $category->bailian_id;
        }

        $category = $item->user->getDefaultCategory();
        if (! $category) {
            $category = $knowledge->categories()->first();
            if (! $category) {
                throw new Exception('知识库没有可用的分类');
            }
        }
        return $category->bailian_id;
    }

    /**
     * 验证父级目录
     */
    private function validateParentDirectory(?int $parentId): void
    {
        if ($parentId) {
            $parent = BailianKnowledgeItem::find($parentId);
            if (! $parent) {
                throw new Exception('上级目录不存在');
            }
            if ($parent->itemable_type != 'bailian_directory') {
                throw new Exception('上级必须是目录');
            }
        }
    }

    /**
     * 更新知识库大小
     */
    private function updateKnowledgeSize(BailianKnowledge $knowledge, int $size): void
    {
        if ($size > 0) {
            $knowledge->update([
                'size' => max(bcadd($knowledge->size, $size), 0)
            ]);
        }
    }
}
