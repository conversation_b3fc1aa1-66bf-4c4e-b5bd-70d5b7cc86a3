<?php

namespace App\Services\KnowledgeItemStrategies;

use App\Enums\Bailian\ItemSyncStatusEnum;
use App\Events\KnowledgeItemCreatedEvent;
use App\Jobs\BaiLian\SyncKnowledgeItemToAlibabaJob;
use App\Models\BailianFile;
use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeFile;
use App\Models\BailianKnowledgeItem;
use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use Exception;
use Modules\Storage\Models\Upload;

/**
 * 文件知识库策略
 */
class FileKnowledgeStrategy extends AbstractKnowledgeItemStrategy
{

    public function addToKnowledgeBase($item, BailianKnowledge $knowledge, string $categoryId, array $options): void
    {
        // 确保有storage记录
        $storage = $item->storage;
        if (! $storage) {
            throw new Exception('文件的存储记录不存在');
        }

        // 获取知识库工具
        $knowledgeTool = app(KnowledgeTool::class);

        // 添加文档到知识库
        $documentInfo = $knowledgeTool->addDocumentToKnowledge(
            $knowledge,
            $storage->path_url,
            $categoryId,
            $storage->hash,
            $storage->size,
            $knowledge->workspace_id
        );

        // 创建或更新知识库文件记录
        $this->saveKnowledgeFile($item, $knowledge, $storage, $categoryId, $documentInfo);

        // 创建知识库项目记录
        $knowledgeItem = $this->createKnowledgeItem($item, $knowledge, $options);
        $knowledgeItem->update([
            'sync_status' => ItemSyncStatusEnum::SYNCED,
            'sync_error'  => null,
            'synced_at'   => now(),
        ]);
    }

    public function addToKnowledge(
        $item,
        BailianKnowledge $knowledge,
        string $categoryId,
        array $options
    ): ?BailianKnowledgeItem {
        // 新的快速流程：只创建本地记录，不同步到阿里云
        // 使用基类的 createKnowledgeItem 方法，自动设置 sync_status 为 PENDING
        $knowledgeItem = $this->createKnowledgeItem($item, $knowledge, $options);

        // 触发知识库项目创建事件，异步处理同步
        event(new KnowledgeItemCreatedEvent($knowledgeItem));

        return $knowledgeItem;
    }

    /**
     * 同步文件到阿里云知识库
     */
    public function syncToAlibaba($item, BailianKnowledge $knowledge, string $categoryId): void
    {
        // 确保有storage记录
        $storage = $item->storage;
        if (! $storage) {
            throw new Exception('文件的存储记录不存在');
        }

        // 获取知识库工具
        $knowledgeTool = app(KnowledgeTool::class);

        // 添加文档到知识库
        $documentInfo = $knowledgeTool->addDocumentToKnowledge(
            $knowledge,
            $storage->path_url,
            $categoryId,
            $storage->hash,
            $storage->size,
            $knowledge->workspace_id
        );

        // 创建或更新知识库文件记录
        $this->saveKnowledgeFile($item, $knowledge, $storage, $categoryId, $documentInfo);

        // 更新知识库大小（文档直接增加大小）
        $fileSize = $storage->size;
        if ($fileSize > 0) {
            $this->updateKnowledgeSize($knowledge, $fileSize);

            $this->logger->info('文档加入知识库，更新知识库大小', [
                'file_id'      => $item->id,
                'knowledge_id' => $knowledge->id,
                'file_size'    => $fileSize
            ]);
        }
    }

    public function removeFromKnowledge($item): void
    {
        // 处理知识库文件记录
        $knowledge = $this->handleKnowledgeFileRemoval($item);

        // 处理知识库项目记录
        if (! $knowledge) {
            $knowledge = $this->handleKnowledgeItemRemoval($item);
        } else {
            $this->handleKnowledgeItemRemoval($item);
        }
    }

    /**
     * 文件内容更新后的同步处理
     * 用于新的优化流程：重置同步状态并重新同步
     */
    public function handleContentUpdate($item): void
    {
        $knowledgeItem = $item->knowledgeItem;
        if (! $knowledgeItem) {
            return; // 不在知识库中，无需处理
        }

        // 如果正在同步中，不重复触发
        if ($knowledgeItem->isSyncing()) {
            $this->logger->info('文件正在同步中，跳过内容更新同步', [
                'file_id'           => $item->id,
                'knowledge_item_id' => $knowledgeItem->id
            ]);
            return;
        }

        // 重置同步状态为待同步
        $knowledgeItem->update([
            'sync_status' => \App\Enums\Bailian\ItemSyncStatusEnum::PENDING,
            'sync_error'  => null,
        ]);

        // 重新触发异步同步
        SyncKnowledgeItemToAlibabaJob::dispatch($knowledgeItem);

        $this->logger->info('文件内容更新，重新触发同步', [
            'file_id'           => $item->id,
            'knowledge_item_id' => $knowledgeItem->id
        ]);
    }

    public function removeFromKnowledgeById(int $itemId, ?int $storageId = null, bool $deleteSelf = false): bool
    {
        try {
            $storage   = $storageId ? Upload::find($storageId) : null;
            $size      = $storage->size ?? 0;
            $knowledge = null;

            $document = new BailianFile();

            // 获取知识库文件记录
            $knowledgeFile = BailianKnowledgeFile::query()
                ->where('sourceable_type', $document->getMorphClass())
                ->where('sourceable_id', $itemId)
                ->first();

            if ($knowledgeFile) {
                $knowledge = BailianKnowledge::find($knowledgeFile->knowledge_id);

                if ($knowledge && $knowledge->knowledge_id && $knowledgeFile->file_id) {
                    $knowledgeTool = app(KnowledgeTool::class);
                    $knowledgeTool->deleteIndexDocuments(
                        $knowledge->knowledge_id,
                        $knowledge->workspace_id,
                        [$knowledgeFile->file_id]
                    );
                }

                $knowledgeFile->delete();
            }

            $knowledgeItem = BailianKnowledgeItem::query()
                ->where('itemable_type', $document->getMorphClass())
                ->where('itemable_id', $itemId)
                ->first();

            if ($knowledgeItem) {
                // 如果之前没有获取到知识库，从 knowledgeItem 获取
                if (! $knowledge) {
                    $knowledge = $knowledgeItem->baiLianKnowledge;
                }

                if ($size > 0 && $knowledge) {
                    // 使用基类的 updateKnowledgeSize 方法（传递负数减少大小）
                    $this->updateKnowledgeSize($knowledge, -$size);

                    $this->logger->info('文档从知识库删除，更新知识库大小', [
                        'file_id'      => $itemId,
                        'knowledge_id' => $knowledge->id,
                        'size_change'  => -$size
                    ]);
                }
                $knowledgeItem->delete();
            }

            if ($deleteSelf) {
                $this->logger->info('删除文档本身', [
                    'is_self'        => $deleteSelf,
                    'class_basename' => class_basename($document)
                ]);

                // 删除存储文件
                $this->deleteStorageFile($storage);

                BailianFile::query()->where('id', $itemId)->delete();
            }

            return true;
        } catch (Exception $e) {
            $this->logError('从知识库中移除文档失败', $itemId, class_basename(new BailianFile()), $e);
            throw $e;
        }
    }

    public function getSupportedModelClass(): string
    {
        return BailianFile::class;
    }
}
