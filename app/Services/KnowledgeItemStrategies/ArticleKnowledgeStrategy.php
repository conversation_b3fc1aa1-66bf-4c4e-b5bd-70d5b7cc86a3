<?php

namespace App\Services\KnowledgeItemStrategies;

use App\Enums\Bailian\ItemSyncStatusEnum;
use App\Events\KnowledgeItemCreatedEvent;
use App\Jobs\BaiLian\SyncKnowledgeItemToAlibabaJob;
use App\Models\BailianArticle;
use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeFile;
use App\Models\BailianKnowledgeItem;
use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use Exception;
use Modules\Storage\Models\Upload;

/**
 * 笔记知识库策略
 */
class ArticleKnowledgeStrategy extends AbstractKnowledgeItemStrategy
{

    /**
     * Notes: 同步添加笔记到阿里云知识库
     *
     * @Author: 玄尘
     * @Date: 2025/6/11 15:39
     * @param $item
     * @param  \App\Models\BailianKnowledge  $knowledge
     * @param  string  $categoryId
     * @param  array  $options
     * @throws \Exception
     */
    public function addToKnowledgeBase($item, BailianKnowledge $knowledge, string $categoryId, array $options): void
    {
        // 确保存储文件存在
        $storage = $this->ensureStorageExists($item);

        // 获取知识库工具
        $knowledgeTool = app(KnowledgeTool::class);

        // 添加文档到知识库
        $documentInfo = $knowledgeTool->addDocumentToKnowledge(
            $knowledge,
            $storage->path_url,
            $categoryId,
            $storage->hash,
            $storage->size,
            $knowledge->workspace_id
        );

        // 创建或更新知识库文件记录
        $this->saveKnowledgeFile($item, $knowledge, $storage, $categoryId, $documentInfo);

        // 创建知识库项目记录并设置为已同步状态
        $knowledgeItem = $this->createKnowledgeItem($item, $knowledge, $options);
        $knowledgeItem->update([
            'sync_status' => ItemSyncStatusEnum::SYNCED,
            'sync_error'  => null,
            'synced_at'   => now(),
        ]);
    }

    /**
     * Notes: description
     *
     * @Author: 玄尘
     * @Date: 2025/6/11 09:27
     * @param $item
     * @param  \App\Models\BailianKnowledge  $knowledge
     * @param  string  $categoryId
     * @param  array  $options
     * @return \App\Models\BailianKnowledgeItem
     */
    public function addToKnowledge(
        $item,
        BailianKnowledge $knowledge,
        string $categoryId,
        array $options
    ): BailianKnowledgeItem {
        // 新的快速流程：只创建本地记录，不同步到阿里云
        // 使用基类的 createKnowledgeItem 方法，自动设置 sync_status 为 PENDING
        $knowledgeItem = $this->createKnowledgeItem($item, $knowledge, $options);

        // 触发知识库项目创建事件，异步处理同步
        event(new KnowledgeItemCreatedEvent($knowledgeItem));

        return $knowledgeItem;
    }

    /**
     * 同步笔记到阿里云知识库
     */
    public function syncToAlibaba($item, BailianKnowledge $knowledge, string $categoryId): void
    {
        // 获取旧文件信息（用于计算大小变化和删除旧文件）
        $oldSize       = 0;
        $oldStorage    = null;
        $knowledgeFile = $item->knowledgeFile;
        if ($knowledgeFile && $knowledgeFile->storage_id) {
            $oldStorage = Upload::find($knowledgeFile->storage_id);
            $oldSize    = $oldStorage ? $oldStorage->size : 0;
        }

        // 确保存储文件存在
        $storage = $this->ensureStorageExists($item);

        // 获取知识库工具
        $knowledgeTool = app(KnowledgeTool::class);

        // 添加文档到知识库
        $documentInfo = $knowledgeTool->addDocumentToKnowledge(
            $knowledge,
            $storage->path_url,
            $categoryId,
            $storage->hash,
            $storage->size,
            $knowledge->workspace_id
        );

        // 创建或更新知识库文件记录
        $this->saveKnowledgeFile($item, $knowledge, $storage, $categoryId, $documentInfo);

        // 删除旧存储文件（如果存在）
        if ($oldStorage) {
            $this->deleteStorageFile($oldStorage);
        }

        // 更新知识库大小
        $newSize        = $storage->size;
        $sizeDifference = $newSize - $oldSize;
        if ($sizeDifference != 0) {
            $this->updateKnowledgeSize($knowledge, $sizeDifference);

            $this->logger->info('更新知识库大小', [
                'article_id'      => $item->id,
                'knowledge_id'    => $knowledge->id,
                'old_size'        => $oldSize,
                'new_size'        => $newSize,
                'size_difference' => $sizeDifference
            ]);
        }
    }

    public function removeFromKnowledge($item): void
    {
        $this->logger->info('开始删除笔记', ['article_id' => $item->id]);

        // 获取实际文件大小（优先从当前storage获取，其次从knowledgeFile获取）
        $actualSize = 0;
        $storage    = $item->storage;

        if ($storage) {
            $actualSize = $storage->size;
        } else {
            // 如果当前storage不存在，从knowledgeFile获取
            $knowledgeFile = $item->knowledgeFile;
            if ($knowledgeFile && $knowledgeFile->storage_id) {
                $knowledgeStorage = Upload::find($knowledgeFile->storage_id);
                $actualSize       = $knowledgeStorage ? $knowledgeStorage->size : 0;
            }
        }

        $this->logger->info('删除笔记时获取笔记存储信息', [
            'article_id' => $item->id,
            'storage_id' => $storage?->id,
            'size'       => $actualSize
        ]);

        // 处理知识库文件记录
        $knowledge = $this->handleKnowledgeFileRemoval($item);

        // 处理知识库项目记录
        $knowledgeItem = $item->knowledgeItem;
        if ($knowledgeItem) {
            $knowledge = $knowledgeItem->baiLianKnowledge;

            if ($actualSize > 0 && $knowledge) {
                $this->updateKnowledgeSize($knowledge, -$actualSize);
                $this->logger->info('删除笔记时更新知识库大小', [
                    'article_id'       => $item->id,
                    'knowledge_id'     => $knowledge->id,
                    'actual_file_size' => $actualSize,
                    'size_change'      => -$actualSize
                ]);
            }

            $knowledgeItem->delete();
        }

        // 删除笔记存储文件
        if ($storage) {
            $this->deleteStorageFile($storage);
            $item->update(['storage_id' => null]);
        }
    }

    /**
     * 笔记内容更新后的同步处理
     * 用于新的优化流程：重置同步状态并重新同步
     */
    public function handleContentUpdate($item): void
    {
        $knowledgeItem = $item->knowledgeItem;
        if (! $knowledgeItem) {
            return; // 不在知识库中，无需处理
        }

        // 如果正在同步中，不重复触发
        if ($knowledgeItem->isSyncing()) {
            $this->logger->info('笔记正在同步中，跳过内容更新同步', [
                'article_id'        => $item->id,
                'knowledge_item_id' => $knowledgeItem->id
            ]);
            return;
        }
        // 重置 storage_id，旧文件将在 syncToAlibaba 中删除
        $item->update([
            'storage_id' => null
        ]);

        // 重置同步状态为待同步
        $knowledgeItem->update([
            'sync_status' => ItemSyncStatusEnum::PENDING,
            'sync_error'  => null,
        ]);

        // 重新触发异步同步
        SyncKnowledgeItemToAlibabaJob::dispatch($knowledgeItem);

        $this->logger->info('笔记内容更新，重新触发同步', [
            'article_id'        => $item->id,
            'knowledge_item_id' => $knowledgeItem->id
        ]);
    }

    public function removeFromKnowledgeById(int $itemId, ?int $storageId = null, bool $deleteSelf = false): bool
    {
        try {
            $storage = $storageId ? Upload::find($storageId) : null;
            $size    = $storage->size ?? 0;

            $article = new BailianArticle();

            // 获取知识库项目记录
            $knowledgeItem = BailianKnowledgeItem::query()
                ->where('itemable_type', $article->getMorphClass())
                ->where('itemable_id', $itemId)
                ->first();

            // 检查同步状态，如果正在同步中，等待一段时间
            if ($knowledgeItem && $knowledgeItem->isSyncing()) {
                $this->logger->warning('笔记正在同步中，延迟删除操作', [
                    'article_id'        => $itemId,
                    'knowledge_item_id' => $knowledgeItem->id,
                    'sync_status'       => $knowledgeItem->sync_status->value
                ]);

                // 标记为删除待处理，但不立即删除
                // 这里可以考虑使用延迟队列重新调度删除任务
                throw new Exception('笔记正在同步中，请稍后再试');
            }

            // 获取知识库文件记录
            $knowledgeFile = BailianKnowledgeFile::query()
                ->where('sourceable_type', $article->getMorphClass())
                ->where('sourceable_id', $itemId)
                ->first();

            if ($knowledgeFile) {
                $knowledge = BailianKnowledge::find($knowledgeFile->knowledge_id);

                // 只有在已同步的情况下才调用阿里云删除API
                if ($knowledge && $knowledge->knowledge_id && $knowledgeFile->file_id &&
                    (! $knowledgeItem || $knowledgeItem->isSynced())) {
                    $knowledgeTool = app(KnowledgeTool::class);
                    $knowledgeTool->deleteIndexDocuments(
                        $knowledge->knowledge_id,
                        $knowledge->workspace_id,
                        [$knowledgeFile->file_id]
                    );
                }

                $knowledgeFile->delete();
            }

            if ($knowledgeItem) {
                $knowledge = $knowledgeItem->baiLianKnowledge;
                if ($size > 0 && $knowledge) {
                    // 使用基类的 updateKnowledgeSize 方法（传递负数减少大小）
                    $this->updateKnowledgeSize($knowledge, -$size);
                }
                $knowledgeItem->delete();
            }

            // 删除存储文件
            $this->deleteStorageFile($storage);

            if ($deleteSelf) {
                $this->logger->info('删除笔记本身', [
                    'is_self'        => $deleteSelf,
                    'class_basename' => class_basename($article)
                ]);
                BailianArticle::query()->where('id', $itemId)->delete();
                BailianArticle::query()->where('parent_id', $itemId)->delete();
            }

            return true;
        } catch (Exception $e) {
            $this->logError('从知识库中移除笔记失败', $itemId, class_basename(new BailianArticle()), $e);
            throw $e;
        }
    }

    /**
     * 确保笔记存储文件存在
     *
     * @param  BailianArticle  $item
     * @return Upload
     * @throws Exception
     */
    private function ensureStorageExists($item): Upload
    {
        if ($item->storage_id && ! $item->storage) {
            $this->logger->warning('笔记storage_id存在但storage记录不存在，重新生成', [
                'article_id' => $item->id,
                'storage_id' => $item->storage_id
            ]);
            $item->update(['storage_id' => null]);
        }

        // 如果没有storage_id或storage记录不存在，生成新的存储文件
        if (! $item->storage_id || ! $item->storage) {
            $item->prepareStorage();
            $item = $item->refresh();
        }

        // 获取存储信息
        $storage = $item->storage;
        if (! $storage) {
            throw new Exception('笔记存储文件创建失败');
        }

        return $storage;
    }

    public function getSupportedModelClass(): string
    {
        return BailianArticle::class;
    }
}
