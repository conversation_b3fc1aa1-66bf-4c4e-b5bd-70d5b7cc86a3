<?php

namespace App\Services\KnowledgeItemStrategies;

use App\Contracts\KnowledgeItemStrategyInterface;
use App\Enums\Bailian\ItemSyncStatusEnum;
use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeFile;
use App\Models\BailianKnowledgeItem;
use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use Exception;
use Log;
use Modules\Storage\Models\Upload;
use Psr\Log\LoggerInterface;
use Storage;

/**
 * 知识库项目策略抽象基类
 */
abstract class AbstractKnowledgeItemStrategy implements KnowledgeItemStrategyInterface
{
    public LoggerInterface $logger;

    public function __construct()
    {
        $this->logger = Log::channel('bailian');
    }

    /**
     * 创建知识库项目记录
     *
     * @param  mixed  $item  项目实例
     * @param  BailianKnowledge  $knowledge  知识库
     * @param  array  $options  选项参数
     *   - parent_id: 父级ID
     *   - sync_status: 同步状态，默认为 PENDING
     *   - user_id: 用户ID，如果不提供则使用 $item->user_id
     */
    protected function createKnowledgeItem($item, BailianKnowledge $knowledge, array $options): BailianKnowledgeItem
    {
        if ($item->knowledgeItem) {
            return $item->knowledgeItem;
        }

        $itemableType = $item->getMorphClass();

        return BailianKnowledgeItem::create([
            'knowledge_id'  => $knowledge->id,
            'title'         => $item->getTitle(),
            'parent_id'     => $options['parent_id'] ?? null,
            'user_id'       => $options['user_id'] ?? $item->user_id,
            'itemable_id'   => $item->id,
            'itemable_type' => $itemableType,
            'description'   => $item->getDescription(),
            'order'         => $itemableType == 'bailian_directory' ? 0 : 1,
            'sync_status'   => $options['sync_status'] ?? ItemSyncStatusEnum::PENDING,
        ]);
    }

    /**
     * 保存知识库文件记录
     */
    protected function saveKnowledgeFile(
        $item,
        BailianKnowledge $knowledge,
        $storage,
        string $categoryId,
        array $documentInfo
    ): void {
        $knowledgeFile = $item->knowledgeFile;

        if (! $knowledgeFile) {
            BailianKnowledgeFile::create([
                'knowledge_id'        => $knowledge->id,
                'storage_id'          => $item->storage_id,
                'user_id'             => $item->user_id,
                'bailian_category_id' => $categoryId,
                'file_id'             => $documentInfo['file_id'],
                'lease_id'            => $documentInfo['lease_id'] ?? null,
                'parser'              => $documentInfo['parser'] ?? null,
                'name'                => $item->getTitle(),
                'workspace_id'        => $documentInfo['workspace_id'],
                'category_type'       => $documentInfo['category_type'] ?? null,
                'sourceable_id'       => $item->id,
                'sourceable_type'     => $item->getMorphClass(),
            ]);
        } else {
            $knowledgeFile->update([
                'storage_id'          => $item->storage_id,
                'bailian_category_id' => $categoryId,
                'file_id'             => $documentInfo['file_id'],
                'lease_id'            => $documentInfo['lease_id'] ?? null,
                'name'                => $item->getTitle(),
            ]);
        }
    }

    /**
     * 处理知识库文件记录移除
     */
    protected function handleKnowledgeFileRemoval($item): ?BailianKnowledge
    {
        $knowledgeFile = $item->knowledgeFile;
        $knowledge     = null;

        if ($knowledgeFile) {
            $knowledge = BailianKnowledge::find($knowledgeFile->knowledge_id);

            if ($knowledge && $knowledge->knowledge_id && $knowledgeFile->file_id) {
                $knowledgeTool = app(KnowledgeTool::class);
                $knowledgeTool->deleteIndexDocuments(
                    $knowledge->knowledge_id,
                    $knowledge->workspace_id,
                    [$knowledgeFile->file_id]
                );
            }

            $knowledgeFile->delete();
        }

        return $knowledge;
    }

    /**
     * 处理知识库项目记录移除
     */
    protected function handleKnowledgeItemRemoval($item): ?BailianKnowledge
    {
        $knowledgeItem = $item->knowledgeItem;
        $knowledge     = null;

        if ($knowledgeItem) {
            $knowledge = $knowledgeItem->baiLianKnowledge;
            $size      = $item->getSize();

            if ($size > 0 && $knowledge) {
                // 使用统一的 updateKnowledgeSize 方法（传递负数减少大小）
                $this->updateKnowledgeSize($knowledge, -$size);
            }

            $item->knowledgeItem()->delete();
        }

        return $knowledge;
    }

    /**
     * 更新知识库大小
     *
     * @param  BailianKnowledge  $knowledge  知识库
     * @param  int  $size  大小变化量（正数为增加，负数为减少）
     */
    public function updateKnowledgeSize(BailianKnowledge $knowledge, int $size): void
    {
        if ($size != 0) {
            if ($size > 0) {
                // 增加大小
                $knowledge->update([
                    'size' => max(bcadd($knowledge->size, $size), 0)
                ]);
            } else {
                // 减少大小
                $knowledge->update([
                    'size' => max(bcsub($knowledge->size, abs($size)), 0)
                ]);
            }
        }
    }

    /**
     * 记录错误日志
     */
    protected function logError(string $operation, int $itemId, string $modelType, Exception $e): void
    {
        $this->logger->error($operation, [
            'id'    => $itemId,
            'type'  => $modelType,
            'error' => $e->getMessage()
        ]);
    }

    /**
     * 删除存储文件
     */
    protected function deleteStorageFile(?Upload $storage): void
    {
        if ($storage) {
            Storage::delete($storage->path);
            $storage->delete();
        }
    }

    /**
     * 默认的同步到阿里云实现（子类可以重写）
     */
    public function syncToAlibaba($item, BailianKnowledge $knowledge, string $categoryId): void
    {
        throw new Exception('此类型的项目不支持同步到阿里云');
    }
}
