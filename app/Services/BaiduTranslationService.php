<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BaiduTranslationService
{
    private string $appId;
    private string $appKey;
    private string $apiUrl;
    private int    $timeout;

    public function __construct()
    {
        $this->appId   = config('services.baidu_translate.app_id');
        $this->appKey  = config('services.baidu_translate.app_key');
        $this->apiUrl  = config('services.baidu_translate.url', 'https://fanyi-api.baidu.com/api/trans/vip/translate');
        $this->timeout = config('services.baidu_translate.timeout', 30);
    }

    /**
     * 翻译文本
     *
     * @param  string  $text  要翻译的文本
     * @param  string  $to  目标语言（zh: 中文, en: 英文）
     * @param  string  $from  源语言（auto: 自动检测）
     * @return string 翻译结果
     * @throws Exception
     */
    public function translate(string $text, string $to = 'zh', string $from = 'auto'): string
    {
        if (empty($this->appId) || empty($this->appKey)) {
            throw new Exception('百度翻译配置不完整，请检查 APP_ID 和 APP_KEY');
        }

        // 生成随机数
        $salt = rand(10000, 99999);

        // 生成签名
        $sign = $this->generateSign($text, $salt);

        // 准备请求参数
        $params = [
            'q'     => $text,
            'from'  => $from,
            'to'    => $to,
            'appid' => $this->appId,
            'salt'  => $salt,
            'sign'  => $sign
        ];

        try {
            // 发送请求
            $response = Http::timeout($this->timeout)
                ->asForm()
                ->post($this->apiUrl, $params);

            if (! $response->successful()) {
                throw new Exception("百度翻译API请求失败: HTTP {$response->status()}");
            }

            $result = $response->json();

            // 检查错误
            if (isset($result['error_code'])) {
                throw new Exception("百度翻译API错误: {$result['error_code']} - {$result['error_msg']}");
            }

            // 提取翻译结果
            if (isset($result['trans_result']) && is_array($result['trans_result']) && ! empty($result['trans_result'])) {
                $translatedText = $result['trans_result'][0]['dst'] ?? '';

                if (empty($translatedText)) {
                    throw new Exception('百度翻译返回空结果');
                }

                return $translatedText;
            }

            throw new Exception('百度翻译响应格式无效');
        } catch (Exception $e) {
            Log::error('百度翻译失败', [
                'error' => $e->getMessage(),
                'text'  => $text,
                'from'  => $from,
                'to'    => $to
            ]);
            throw $e;
        }
    }

    /**
     * 生成签名
     */
    private function generateSign(string $query, int $salt): string
    {
        // 百度翻译签名算法：MD5(appid + query + salt + key)
        $str = $this->appId.$query.$salt.$this->appKey;
        return md5($str);
    }

    /**
     * 测试连接
     */
    public function testConnection(): array
    {
        try {
            // 获取当前IP信息
            $currentIp = $this->getCurrentIp();

            $testText = 'Hello, world!';
            $result   = $this->translate($testText, 'zh', 'en');

            return [
                'success'    => true,
                'test_text'  => $testText,
                'translated' => $result,
                'current_ip' => $currentIp,
                'message'    => '百度翻译连接正常'
            ];
        } catch (Exception $e) {
            $currentIp = $this->getCurrentIp();

            return [
                'success'    => false,
                'error'      => $e->getMessage(),
                'current_ip' => $currentIp,
                'message'    => '百度翻译连接失败',
                'solution'   => $this->getIpErrorSolution($e->getMessage())
            ];
        }
    }

    /**
     * 获取当前IP地址
     */
    private function getCurrentIp(): array
    {
        $ips = [];

        // 获取服务器IP
        $ips['server_ip'] = $_SERVER['SERVER_ADDR'] ?? 'unknown';

        // 获取客户端IP
        $ips['client_ip'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        // 获取公网IP
        try {
            $response = Http::timeout(5)->get('https://api.ipify.org?format=json');
            if ($response->successful()) {
                $ips['public_ip'] = $response->json()['ip'] ?? 'unknown';
            } else {
                $ips['public_ip'] = 'failed_to_get';
            }
        } catch (Exception $e) {
            $ips['public_ip'] = 'error: '.$e->getMessage();
        }

        return $ips;
    }

    /**
     * 获取IP错误的解决方案
     */
    private function getIpErrorSolution(string $errorMessage): string
    {
        if (str_contains($errorMessage, 'INVALID_CLIENT_IP') || str_contains($errorMessage, '58000')) {
            return '请在百度翻译控制台的IP白名单中添加当前IP地址，或设置为 0.0.0.0/0 允许所有IP（仅用于测试）';
        }

        return '请检查网络连接和API配置';
    }

    /**
     * 翻译Vidu prompt（专门优化）
     */
    public function translateViduPrompt(string $prompt): string
    {
        return $this->translate($prompt, 'zh', 'en');
    }
}
