<?php

namespace App\Services;

use App\Facades\Api;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Encoders\AutoEncoder;
use Intervention\Image\ImageManager;
use Modules\Storage\Models\Upload;

class UploadService
{
    protected string  $path;
    protected string  $disk;
    protected ?string $customFileName    = null;
    protected bool    $useOriginalName   = false;
    protected bool    $useUniqueFileName = false;
    protected bool    $createThumbnail   = true;
    protected int     $thumbnailQuality  = 10;
    protected array   $thumbnailSizes    = [];

    protected array $imageMimes = [
        'image/png',
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/bmp',
        'image/webp',
    ];

    /**
     * 知识库文档允许的MIME类型
     */
    protected array $knowledgeBaseMimes = [
        // 文档
        'application/pdf', // pdf
        'application/msword', // doc
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
        'text/plain', // txt
        'text/markdown', // md
        'application/vnd.ms-powerpoint', // ppt
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
        'application/vnd.ms-excel', // xls
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
        // 图片
        'image/png', // png
        'image/jpeg', // jpg, jpeg
        'image/bmp', // bmp
        'image/gif', // gif
    ];

    /**
     * 智能体应用会话交互文件允许的MIME类型
     */
    protected array $agentInteractionMimes = [
        // 文档
        'application/msword', // doc
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
        'application/wps-office.docx', // wps
        'application/vnd.ms-powerpoint', // ppt
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
        'application/vnd.ms-excel', // xls
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
        'text/markdown', // md
        'text/plain', // txt
        'application/pdf', // pdf
        // 图片
        'image/png', // png
        'image/jpeg', // jpg, jpeg
        'image/bmp', // bmp
        'image/gif', // gif
        // 音频
        'audio/aac', // aac
        'audio/amr', // amr
        'audio/flac', // flac
        'video/x-flv', // flv
        'audio/mp4', // m4a
        'audio/mpeg', // mp3
        'audio/mpeg', // mpeg
        'audio/ogg', // ogg
        'audio/opus', // opus
        'audio/wav', // wav
        'audio/webm', // webm
        'audio/x-ms-wma', // wma
        // 视频
        'video/mp4', // mp4
        'video/x-matroska', // mkv
        'video/x-msvideo', // avi
        'video/quicktime', // mov
        'video/x-ms-wmv', // wmv
    ];

    /**
     * 文件名长度限制
     */
    protected int $minFileNameLength = 4;
    protected int $maxFileNameLength = 128;

    /**
     * 清理文件名，移除URL不安全字符
     *
     * @param  string  $fileName  原始文件名
     * @return string 清理后的文件名
     */
    protected function sanitizeFileName(string $fileName): string
    {
        // URL解码
        $fileName = urldecode($fileName);

        // 移除或替换不安全的字符
        $unsafeChars = [
            '<', '>', ':', '"', '/', '\\', '|', '?', '*',
            "\0", "\x01", "\x02", "\x03", "\x04", "\x05", "\x06", "\x07",
            "\x08", "\x09", "\x0A", "\x0B", "\x0C", "\x0D", "\x0E", "\x0F",
            "\x10", "\x11", "\x12", "\x13", "\x14", "\x15", "\x16", "\x17",
            "\x18", "\x19", "\x1A", "\x1B", "\x1C", "\x1D", "\x1E", "\x1F"
        ];

        // 替换不安全字符为下划线
        $fileName = str_replace($unsafeChars, '_', $fileName);

        // 移除连续的下划线
        $fileName = preg_replace('/_+/', '_', $fileName);

        // 移除开头和结尾的下划线和空格
        $fileName = trim($fileName, '_ ');

        // 如果文件名为空，生成默认名称
        if (empty($fileName)) {
            $fileName = 'file_'.time();
        }

        // 处理文件名长度
        $pathInfo  = pathinfo($fileName);
        $extension = $pathInfo['extension'] ?? '';
        $baseName  = $pathInfo['filename'] ?? $fileName;

        // 计算扩展名长度（包括点号）
        $extensionLength   = $extension ? strlen($extension) + 1 : 0;
        $maxBaseNameLength = $this->maxFileNameLength - $extensionLength;

        // 截断基础文件名
        if (strlen($baseName) > $maxBaseNameLength) {
            $baseName = mb_substr($baseName, 0, $maxBaseNameLength, 'UTF-8');
        }

        // 重新组合文件名
        $sanitizedFileName = $extension ? $baseName.'.'.$extension : $baseName;

        // 确保最小长度
        if (strlen($sanitizedFileName) < $this->minFileNameLength) {
            $sanitizedFileName = $baseName.'_'.time().($extension ? '.'.$extension : '');
        }

        return $sanitizedFileName;
    }

    public function __construct()
    {
        $this->path           = date('Y/m/d');
        $this->disk           = config('filesystems.default');
        $this->thumbnailSizes = [
            'thumb' => [config('admin.thumb_size', 200), config('admin.thumb_size', 200)]
        ];
    }

    /**
     * 设置上传路径
     *
     * @param  string  $path
     * @return $this
     */
    public function setPath(string $path): self
    {
        $this->path = $path;
        return $this;
    }

    /**
     * 设置存储磁盘
     *
     * @param  string  $disk
     * @return $this
     */
    public function setDisk(string $disk): self
    {
        $this->disk = $disk;
        return $this;
    }

    /**
     * 设置自定义文件名
     *
     * @param  string  $fileName
     * @return $this
     */
    public function setFileName(string $fileName): self
    {
        $this->customFileName = $fileName;
        return $this;
    }

    /**
     * 使用原始文件名
     *
     * @param  bool  $use
     * @return $this
     */
    public function useOriginalName(bool $use = true): self
    {
        $this->useOriginalName = $use;
        return $this;
    }

    /**
     * 使用唯一文件名（时间戳+随机字符串）
     *
     * @param  bool  $use
     * @return $this
     */
    public function useUniqueFileName(bool $use = true): self
    {
        $this->useUniqueFileName = $use;
        return $this;
    }

    /**
     * 设置是否创建缩略图
     *
     * @param  bool  $create
     * @return $this
     */
    public function createThumbnail(bool $create = true): self
    {
        $this->createThumbnail = $create;
        return $this;
    }

    /**
     * 设置缩略图质量
     *
     * @param  int  $quality
     * @return $this
     */
    public function setThumbnailQuality(int $quality): self
    {
        $this->thumbnailQuality = $quality;
        return $this;
    }

    /**
     * 设置缩略图尺寸
     *
     * @param  array  $sizes  格式: ['thumb' => [width, height], 'medium' => [width, height]]
     * @return $this
     */
    public function setThumbnailSizes(array $sizes): self
    {
        $this->thumbnailSizes = $sizes;
        return $this;
    }

    public function getFileNameLengthLimits()
    {
        return [
            'min' => $this->minFileNameLength,
            'max' => $this->maxFileNameLength,
        ];
    }

    public function getKnowledgeBaseMimes()
    {
        return $this->knowledgeBaseMimes;
    }

    public function getAgentInteractionMimes()
    {
        return $this->agentInteractionMimes;
    }

    /**
     * 保存上传的文件
     *
     * @param  UploadedFile  $file
     * @return array
     * @throws Exception
     */
    public function save(UploadedFile $file): array
    {
        $hash      = File::hash($file);
        $extension = $file->getClientOriginalExtension();
        if ($extension == 'bin') {
            $extension = 'png';
        }

        // 确定文件名
        if ($this->customFileName) {
            $name = $this->customFileName.'.'.$extension;
        } elseif ($this->useOriginalName) {
            $name = $this->sanitizeFileName($file->getClientOriginalName());
        } elseif ($this->useUniqueFileName) {
            $name = time().'_'.uniqid().'.'.$extension;
        } else {
            $name = sprintf('%s.%s', $hash, $extension);
        }

        $path = sprintf('%s/%s', $this->path, $name);

        // 检查文件是否已存在（仅当使用哈希作为文件名时）
        $existFile = Upload::where('hash', $hash)->first();

        if (! $existFile) {
            if (Storage::disk($this->disk)->putFileAs($this->path, $file, $name) === false) {
                throw new Exception('文件上传失败', 4040);
            }

            Upload::create([
                'user_id'  => Api::id(),
                'hash'     => $hash,
                'size'     => File::size($file),
                'type'     => $file->getClientMimeType(),
                'original' => $file->getClientOriginalName(),
                'disk'     => $this->disk,
                'path'     => $path,
            ]);

            // 处理图片缩略图
            if ($this->createThumbnail && in_array($file->getMimeType(), $this->imageMimes)) {
                $fileNameWithoutExt = pathinfo($name, PATHINFO_FILENAME);

                foreach ($this->thumbnailSizes as $suffix => $dimensions) {
                    $width  = $dimensions[0] ?? config('admin.thumb_size', 200);
                    $height = $dimensions[1] ?? config('admin.thumb_size', 200);

                    $thumb = ImageManager::imagick()
                        ->read($file)
                        ->resize($width, $height)
                        ->encode(new AutoEncoder(quality: $this->thumbnailQuality));

                    $thumbName = sprintf('%s/%s-%s.%s', $this->path, $fileNameWithoutExt, $suffix, $extension);
                    Storage::disk($this->disk)->put($thumbName, $thumb);
                    unset($thumb);
                }
            }

            return [
                'hash'     => $hash,
                'type'     => $file->getMimeType(),
                'size'     => File::size($file),
                'original' => $file->getClientOriginalName(),
                'url'      => Storage::disk($this->disk)->url($path),
                'path'     => $path,
            ];
        } else {
            if (Storage::disk($existFile->disk)->exists($existFile->path)) {
                return [
                    'hash'     => $hash,
                    'type'     => $existFile->type,
                    'size'     => $existFile->size,
                    'original' => $existFile->original,
                    'url'      => Storage::disk($existFile->disk)->url($existFile->path),
                    'path'     => $existFile->path,
                ];
            }
            $existFile->delete();
            return $this->save($file);
        }
    }

    /**
     * 保存文档文件（可以添加文档特定的处理逻辑）
     *
     * @param  UploadedFile  $file
     * @param  array  $allowedMimes  允许的MIME类型
     * @param  int  $maxSize  最大文件大小（KB）
     * @return array
     * @throws Exception
     */
    public function saveDocument(UploadedFile $file, array $allowedMimes = [], int $maxSize = 10240): array
    {
        // 验证文件类型
        if (! empty($allowedMimes) && ! in_array($file->getMimeType(), $allowedMimes)) {
            throw new Exception('不支持的文件类型', 4001);
        }

        // 验证文件大小
        if ($file->getSize() > $maxSize * 1024) {
            throw new Exception('文件大小超过限制', 4002);
        }

        // 使用自定义路径保存文档
        $originalPath = $this->path;

        // 对于文档，默认不创建缩略图
        $originalThumbSetting  = $this->createThumbnail;
        $this->createThumbnail = false;

        $result = $this->save($file);

        // 恢复原始设置
        $this->path            = $originalPath;
        $this->createThumbnail = $originalThumbSetting;

        return $result;
    }

    /**
     * 处理文件上传（从本地文件路径）
     *
     * @param  string  $filePath  本地文件路径
     * @param  array  $options  上传选项
     * @return Upload
     * @throws Exception
     */
    public function handleUpload(string $filePath, array $options = []): Upload
    {
        // 验证文件是否存在
        if (! file_exists($filePath)) {
            throw new Exception('文件不存在: '.$filePath);
        }

        // 获取文件信息
        $fileName = $this->sanitizeFileName($options['name'] ?? basename($filePath));
        $mimeType = $options['type'] ?? mime_content_type($filePath);
        $userId   = $options['user_id'] ?? Api::id();

        // 设置上传路径
        if (isset($options['folder'])) {
            $this->setPath($options['folder']);
        }

        // 创建 UploadedFile 对象
        $file = new UploadedFile(
            $filePath,
            $fileName,
            $mimeType,
            null,
            true // 测试模式，不检查上传过程
        );

        // 保存文件
        $result = $this->save($file);

        // 查找并返回 Upload 模型
        $upload = Upload::where('path', $result['path'])->first();

        // 如果需要，更新用户ID
        if ($upload && $userId && $upload->user_id != $userId) {
            $upload->update(['user_id' => $userId]);
            $upload->refresh();
        }

        return $upload;
    }

    /**
     * 获取图片的缩略图URL，如果不存在则生成
     *
     * @param  string  $imageUrl  原图片URL
     * @param  string  $suffix  缩略图后缀，默认为 'thumb'
     * @return string 缩略图URL
     */
    public function getThumbnailUrl(string $imageUrl, string $suffix = 'thumb'): string
    {
        // 解析URL获取路径
        $thumbPath = parse_url($imageUrl, PHP_URL_PATH);
        if (! $thumbPath) {
            return $imageUrl; // 如果无法解析，返回原图
        }

        // 生成缩略图路径
        $pathInfo  = pathinfo($thumbPath);
        $thumbPath = $pathInfo['dirname'].'/'.$pathInfo['filename'].'-'.$suffix.'.'.$pathInfo['extension'];

        // 检查缩略图是否存在
        if (Storage::disk($this->disk)->exists(ltrim($thumbPath, '/'))) {
            return Storage::disk($this->disk)->url(ltrim($thumbPath, '/'));
        }

        // 缩略图不存在，尝试生成
        try {
            $this->generateThumbnailFromUrl($imageUrl, $suffix);
            // 生成成功，返回缩略图URL
            return Storage::disk($this->disk)->url(ltrim($thumbPath, '/'));
        } catch (Exception $e) {
            // 生成失败，返回原图
            return $imageUrl;
        }
    }

    /**
     * 从URL生成缩略图
     *
     * @param  string  $imageUrl  原图片URL
     * @param  string  $suffix  缩略图后缀
     * @throws Exception
     */
    protected function generateThumbnailFromUrl(string $imageUrl, string $suffix = 'thumb'): void
    {
        // 解析URL获取路径
        $imagePath = parse_url($imageUrl, PHP_URL_PATH);
        if (! $imagePath) {
            throw new Exception('无法解析图片URL');
        }

        $imagePath = ltrim($imagePath, '/');

        // 检查原图是否存在
        if (! Storage::disk($this->disk)->exists($imagePath)) {
            throw new Exception('原图不存在');
        }

        // 获取原图内容
        $imageContent = Storage::disk($this->disk)->get($imagePath);

        // 获取缩略图尺寸配置
        $dimensions = $this->thumbnailSizes[$suffix] ?? $this->thumbnailSizes['thumb'] ?? [200, 200];
        $width      = $dimensions[0];
        $height     = $dimensions[1];

        // 生成缩略图
        $thumb = ImageManager::imagick()
            ->read($imageContent)
            ->resize($width, $height)
            ->encode(new AutoEncoder(quality: $this->thumbnailQuality));

        // 生成缩略图路径
        $pathInfo  = pathinfo($imagePath);
        $thumbPath = $pathInfo['dirname'].'/'.$pathInfo['filename'].'-'.$suffix.'.'.$pathInfo['extension'];

        // 保存缩略图
        Storage::disk($this->disk)->put($thumbPath, $thumb);
        unset($thumb);
    }

    /**
     * 检查URL是否为图片文件
     *
     * @param  string  $url
     * @return bool
     */
    public function isImageUrl(string $url): bool
    {
        // 常见的图片文件扩展名
        $imageExtensions = [
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg',
            'ico', 'tiff', 'tif', 'avif', 'heic', 'heif'
        ];

        // 获取URL的文件扩展名
        $parsedUrl = parse_url(trim($url));
        if (! isset($parsedUrl['path'])) {
            return false;
        }

        $pathInfo  = pathinfo($parsedUrl['path']);
        $extension = strtolower($pathInfo['extension'] ?? '');

        return in_array($extension, $imageExtensions);
    }

    /**
     * 静态方法：检查URL是否为图片文件
     *
     * @param  string  $url
     * @return bool
     */
    public static function checkIsImageUrl(string $url): bool
    {
        return (new static())->isImageUrl($url);
    }
}
