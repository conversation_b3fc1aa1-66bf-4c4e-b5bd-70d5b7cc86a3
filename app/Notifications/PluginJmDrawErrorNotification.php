<?php

namespace App\Notifications;

use App\Models\PluginJmDraw;
use App\Traits\NoticeTrait;
use Illuminate\Database\Eloquent\Model;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\IMMessage;

class PluginJmDrawErrorNotification extends BaseNotification
{
    use NoticeTrait;

    protected PluginJmDraw $draw;

    public function __construct(PluginJmDraw $draw)
    {
        $this->draw = $draw;
    }

    public static function getTitle(): string
    {
        return '生成产品';
    }

    public static function getData($item)
    {
        $data  = $item->data;
        $model = $data['model'] ?? '';

        $resource = self::getResource($model);
        return array_merge($data, [
            'resource' => $resource
        ]);
    }

    public function toDatabase($notifiable): array
    {
        $info = $this->draw;
        return [
            'model'   => [
                'id'    => $info->id,
                'type'  => $info->getMorphClass(),
                'cover' => $info->cover_url,
            ],
            'type'    => 'draw_failure',
            'title'   => self::getTitle(),
            'target'  => [
                'id'   => $info->id,
                'type' => $info->getMorphClass(),
            ],
            'user'    => [
                'id'       => $info->user->id,
                'nickname' => $info->user->info->nickname,
                'avatar'   => $info->user->info->avatar_url,
            ],
            'content' => $this->draw->type_text.'失败',
        ];
    }

    public function toIM(Model $notifiable): IMMessage|array
    {
        $imUser = $notifiable->imUser;
        if ($imUser) {
            return new IMMessage(
                self::getTitle(),
                sprintf('您的%s已生成失败', $this->draw->type_text),
                [$imUser->im_user_id]
            );
        }
    }
} 