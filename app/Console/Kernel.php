<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('horizon:snapshot')->everyFiveMinutes();//Horizon
        $schedule->command('offline:task')->everyMinute();//离线任务调度，每分钟一次
        $schedule->command('search:news')->hourly();//刷新豆包新闻，每小时一次
        $schedule->command('order:clear')->everyFiveMinutes();//清除订单每5分钟一次
        $schedule->command('asset:activityStatus')->everyMinute();//刷新活动状态
        $schedule->command('order:send_vip_score')->everyFiveMinutes()->between('00:10', '01:10');//发送vip积分
        $schedule->command('account:score_expiration')->everyFiveMinutes();//积分过期
        $schedule->command('ai:rtcCloseRoom')->everyMinute();//删除rtc房间
        $schedule->command('style:comfyDrawStyle 41')->dailyAt('00:01');//画图样式
        $schedule->command('style:comfyDrawStyle 25')->dailyAt('00:02');//画图样式
        $schedule->command('style:comfyDrawStyle 40')->dailyAt('00:03');//画图样式

        // 清理智能体过期文件，释放OSS存储空间
        $schedule->command('bailian:clear-assistant-files --days=1')->dailyAt('02:00');//每日2点清理一次
    }

    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');
    }
}
