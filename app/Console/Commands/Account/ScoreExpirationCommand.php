<?php

namespace App\Console\Commands\Account;

use App\Jobs\User\ScoreExpirationJob;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class ScoreExpirationCommand extends Command
{
    protected $signature = 'account:score_expiration';

    #描述
    protected $description = '定时处理积分过期';

    public function handle(): void
    {
        $users = User::whereHas('account.logs', function (Builder $builder) {
            $builder->where('expired_at', '<=', now())
                ->where('is_expired', 0);
        })
            ->limit(1000)
            ->get();
        foreach ($users as $user) {
            ScoreExpirationJob::dispatch($user);
        }
    }
}