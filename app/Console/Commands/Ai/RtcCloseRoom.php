<?php

namespace App\Console\Commands\Ai;

use App\Models\AudioRoom;
use Illuminate\Console\Command;

class RtcCloseRoom extends Command
{
    protected $signature = 'ai:rtcCloseRoom';

    #描述
    protected $description = '定时清理到期房间';

    public function handle(): void
    {
        $rooms = AudioRoom::where('status', AudioRoom::STATUS_ING)
            ->get();
        $rooms->each(function (AudioRoom $room) {
            $room->checkOnlineUser();
        });

        $rooms = AudioRoom::where('expiration_at', '<=', now())
            ->where('status', AudioRoom::STATUS_ING)
            ->get();
        $rooms->each(function (AudioRoom $room) {
            $room->closeRoom();
        });

        $rooms = AudioRoom::where('status', AudioRoom::STATUS_OVER)
            ->get();
        $rooms->each(function (AudioRoom $room) {
            $room->closeRoom();
        });
    }

}