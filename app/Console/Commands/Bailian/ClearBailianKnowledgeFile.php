<?php

namespace App\Console\Commands\Bailian;

use App\Models\BailianKnowledge;
use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use Illuminate\Console\Command;

class ClearBailianKnowledgeFile extends Command
{
    /**
     * 命令名称和参数
     *
     * @var string
     */
    protected $signature = 'skyxu:clear-knowledge-file {knowledge_id : 知识库ID}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '清理指定百炼知识库中的文档';

    /**
     * 知识库工具实例
     *
     * @var KnowledgeTool
     */
    protected KnowledgeTool $knowledgeTool;

    /**
     * 创建命令实例
     *
     * @return void
     */
    public function __construct(KnowledgeTool $knowledgeTool)
    {
        parent::__construct();
        $this->knowledgeTool = $knowledgeTool;
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        try {
            $knowledgeId = $this->argument('knowledge_id');

            if (! $knowledgeId) {
                $this->error("缺少参数 knowledge_id");
                return 1;
            }
            // 获取指定的知识库
            $knowledge = BailianKnowledge::find($knowledgeId);

            if (! $knowledge) {
                $this->error("未找到ID为 {$knowledgeId} 的知识库！");
                return 1;
            }

            $this->info("开始清理知识库 [{$knowledge->name}] 的文档...");

            // 获取百炼平台上的文档列表
            $platformDocuments = [];
            if ($knowledge->knowledge_id) {
                $workspaceId = $knowledge->workspace_id ?: $this->knowledgeTool->serviceConfig->getWorkspaceId();
                $response    = $this->knowledgeTool->listIndexDocuments($knowledge->knowledge_id, $workspaceId);
                if ($response->status != 200) {
                    $this->error("获取百炼平台文档列表时发生错误：".$response->message);
                    return 1;
                }
                $this->info("从百炼平台获取到文档成功");
                if (isset($response->data->documents)) {
                    foreach ($response->data->documents as $document) {
                        $platformDocuments[$document->id] = [
                            'id'      => $document->id,
                            'name'    => $document->name,
                            'status'  => $document->status,
                            'message' => $document->message
                        ];
                    }
                    $this->info("从百炼平台获取到文档成功，共 ".count($response->data->documents)." 条");
                }
            }
            if (empty($platformDocuments)) {
                $this->info("知识库 [{$knowledge->name}] 在百炼平台中没有文档记录");
                return 0;
            }

            // 获取数据库中的文档记录
            $dbFiles = $knowledge->files()->pluck('file_id');

            $this->info("数据库中找到 {$dbFiles->count()} 条文档记录");

            // 需要删除的文档ID列表
            $toDeleteIds = [];
            foreach ($platformDocuments as $platformDocument) {
                if (! in_array($platformDocument['id'], $dbFiles->toArray())) {
                    $toDeleteIds[] = $platformDocument['id'];
                }
            }

            // 从平台删除文档
            if (! empty($toDeleteIds) && $knowledge->knowledge_id) {
                $this->info("正在从百炼平台删除 ".count($toDeleteIds)." 个文档...");
                foreach ($toDeleteIds as $id) {
                    $this->info("正在删除文档 [{$platformDocuments[$id]['name']}]");
                }
                $res = $this->knowledgeTool->deleteIndexDocuments(
                    $knowledge->knowledge_id,
                    $knowledge->workspace_id ?: $this->knowledgeTool->serviceConfig->getWorkspaceId(),
                    $toDeleteIds
                );
                if ($res->status != 200) {
                    $this->error("从百炼平台删除文档时发生错误：".$res->message);
                    return 1;
                }
            }

            $this->info("知识库 [{$knowledge->name}] 的文档清理完成！");

            return 0;
        } catch (\Exception $e) {
            $this->error('清理知识库文档时发生错误：'.$e->getMessage());

            return 1;
        }
    }
}
