<?php

namespace App\Console\Commands;

use Exception;
use <PERSON>uzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Support\Str;
use OpenAI;

class MakePromptCommand extends Command
{
    protected $signature   = 'make:prompt 
    {name : The name of the System Prompt} 
    {--remark=empty : The remark of the System Prompt}';
    protected $description = '创建一个大模型的SystemPrompt文件';

    /**
     * The filesystem instance.
     *
     * @var \Illuminate\Filesystem\Filesystem
     */
    protected $files;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(Filesystem $files)
    {
        parent::__construct();

        $this->files = $files;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $fileName = $this->getClassName();
        $path     = $this->getPath($fileName);

        // Check if file already exists
        $this->info('---判断文件是否存在---');
        if ($this->files->exists($path)) {
            $this->error("工程文件 {$fileName} 已经存在!");
            return 1;
        }
        $this->info('---创建文件---');

        // Create directories if they don't exist
        $this->makeDirectory($path);
        // Generate the file using stub
        $content = '# '.$fileName;

        $remark = $this->hasOption('remark') ? $this->option('remark') : '';
        if (! blank($remark) && $remark != 'empty') {
            $this->info('---初始化Prompt工程内容---');
            $content = $this->getPrompt($remark);
        }
        $this->info('---写入内容---');
        $this->files->put($path, $content);

        $this->info("描述工程文件 {$fileName} 创建完成.");
        return 0;
    }

    /**
     * Get the class name from the command argument.
     *
     * @return string
     */
    protected function getClassName()
    {
        $name = $this->argument('name');

        // Clean up the input: remove multiple spaces, hyphens, underscores
        // and handle mixed case input
        $name = preg_replace('/[\s\-_]+/', ' ', trim($name));

        // Convert to StudlyCase
        $name = Str::studly($name);

        // Ensure the class name ends with "Tool" if not already
        if (! Str::endsWith($name, 'Prompt')) {
            $name .= 'Prompt';
        }

        return $name;
    }

    /**
     * Get the destination file path.
     *
     * @return string
     */
    protected function getPath(string $fileName)
    {
        // Create the file in the app/MCP/Tools directory
        return app_path("Prompts/{$fileName}.md");
    }

    /**
     * Build the directory for the class if necessary.
     *
     * @param  string  $path
     * @return string
     */
    protected function makeDirectory($path)
    {
        $directory = dirname($path);

        if (! $this->files->isDirectory($directory)) {
            $this->files->makeDirectory($directory, 0755, true, true);
        }

        return $directory;
    }

    protected function getPrompt(string $functionRemark)
    {
        $client = OpenAI::factory()
            ->withApiKey(env('PROMPT_APIKEY', 'sk-6625f866cdcd41feb700ae01715e75df'))
            ->withBaseUri(env('PROMPT_BASEURI', 'https://dashscope.aliyuncs.com/compatible-mode/v1'))
            ->withHttpClient(new Client([
                'verify' => false,
                'stream' => false,
            ]))
            ->make();
        $params = [
            'model'    => env('PROMPT_MODEL', 'qwen-plus-latest'),
            'messages' => [
                [
                    'role'    => 'system',
                    'content' => $this->systemPrompt(),
                ],
                [
                    'role'    => 'user',
                    'content' => $functionRemark,
                ],
            ],
            'stream'   => false,
        ];
        try {
            $response = $client->chat()->create($params);
            return $response->choices[0]->message->content;
        } catch (Exception $exception) {
            return <<<EOF
    # 发生了错误
       {$exception->getMessage()}
EOF;
        }
    }

    protected function systemPrompt()
    {
        return wateGetSystemPrompt('CreateSystem');
    }
}