<?php

namespace App\Console\Commands\Asset;

use App\Models\AiUnifyActivity;
use Illuminate\Console\Command;

class ActivityStatusCommand extends Command
{
    protected $signature = 'asset:activityStatus';

    #描述
    protected $description = '调整活动状态';

    public function handle(): void
    {
        AiUnifyActivity::where('status', AiUnifyActivity::STATUS_INIT)
            ->where('start_at', '<', now())
            ->where('end_at', '>', now())
            ->update(['status' => AiUnifyActivity::STATUS_ON]);
        AiUnifyActivity::whereIn('status', [
            AiUnifyActivity::STATUS_INIT,
            AiUnifyActivity::STATUS_ON,
        ])
            ->where('end_at', '<', now())
            ->update(['status' => AiUnifyActivity::STATUS_CLOSE]);
    }
}