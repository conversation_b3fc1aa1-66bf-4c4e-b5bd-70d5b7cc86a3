<?php

namespace App\Console\Commands\Asset;

use App\Jobs\SchedulingJob;
use App\Models\AiUnifyAsset;
use Illuminate\Console\Command;

class SchedulingCommand extends Command
{
    protected $signature = 'asset:scheduling';

    #描述
    protected $description = '任务调度';

    public function handle(): void
    {
        $maxNumber  = 10;
        $runNumber  = AiUnifyAsset::where('status', AiUnifyAsset::STATUS_ING)->count();
        $diffNumber = $maxNumber - $runNumber;
        if ($diffNumber > 0) {
            $logs = AiUnifyAsset::with(['assetable'])
                ->where('status', AiUnifyAsset::STATUS_INIT)
                ->orderBy('created_at')
                ->limit($diffNumber)
                ->get();
            foreach ($logs as $log) {
                SchedulingJob::dispatch($log);
            }
        }
    }
}