<?php

namespace App\Console\Commands;

use App\Models\PluginJmDraw;
use App\Models\PluginKeLing;
use App\Models\PluginMjDraw;
use Illuminate\Console\Command;

class OneKeyCommand extends Command
{
    protected $signature = 'run:onekey';

    #描述
    protected $description = '一次性脚本';

    public function handle(): void
    {
        $jmTask = PluginJmDraw::query()->doesntHave('asset')->get();
        foreach ($jmTask as $item) {
            $item->pushAsset();
        }
        $klTask = PluginKeLing::query()->doesntHave('asset')->get();
        foreach ($klTask as $item) {
            $item->pushAsset();
        }
        $mjTask = PluginMjDraw::query()->doesntHave('asset')->get();
        foreach ($mjTask as $item) {
            $item->pushAsset();
        }
    }

}