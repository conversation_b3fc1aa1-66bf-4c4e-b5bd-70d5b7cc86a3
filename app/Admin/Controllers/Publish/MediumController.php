<?php

namespace App\Admin\Controllers\Publish;

use App\Models\PublishMedium;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class MediumController extends AdminController
{
    protected string $title = '发布内容管理';

    public function grid(): Grid
    {
        return Grid::make(PublishMedium::with(['category'])
            ->withCount([
                'likeable',
                'commentable',
                'favoriteable',
                'browsable',
            ])
            ->orderByDesc('created_at')
            ->orderByDesc('id'), function (Grid $grid) {
            $grid->column('id');
            $grid->column('type', '类型')->using(PublishMedium::TYPES);
            $grid->column('category.name', '分类名称');
            $grid->column('tags', '标签')->implode(' #')->prepend("#");
            $grid->column('title', '标题');
            $grid->column('description', '描述')->limit(20);
            $grid->column('cover', '封面图')->image('', 50);
            $grid->column('pictures', '图集')->image('', 50);
            $grid->column('video', '视频')->display('点击查看')
                ->link(fn() => $this->videoUrlAttr);
            $grid->column('audio', '音频')->display('点击查看')
                ->link(fn() => $this->audioUrlAttr);
            $grid->column('likeable_count', '点赞');
            $grid->column('commentable_count', '评论');
            $grid->column('favoriteable_count', '收藏');
            $grid->column('browsable_count', '浏览');

            $grid->column('created_at', '发布时间');
        });
    }

}