<?php

namespace App\Admin\Controllers\Publish;

use App\Models\PublishTag;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class TagsController extends AdminController
{
    protected string $title = '发布内容标签';

    public function grid(): Grid
    {
        return Grid::make(PublishTag::ordered(), function (Grid $grid) {
            $grid->column('id');
            $grid->column('name', '标签名称');
            $grid->column('order', '排序')->orderable();
            $grid->column('used_count', '使用次数');
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }

    protected function form(): Form
    {
        return Form::make(PublishTag::class, function (Form $form) {
            $form->text('name', '标签名称');
            $form->number('order', '排序')->default(0);
        });
    }
}