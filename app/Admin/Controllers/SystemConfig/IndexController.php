<?php

namespace App\Admin\Controllers\SystemConfig;

use App\Models\SystemConfig;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class IndexController extends AdminController
{
    protected string $title = '系统参数';

    public function grid(): Grid
    {
        return Grid::make(new SystemConfig, function (Grid $grid) {
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '名称');
                $filter->like('key', '键');
                $filter->like('value', '值');
            });
            $grid->column('id', '#ID#');
            $grid->column('name', '名称');
            $grid->column('key', '键');
            $grid->column('value', '值')
                ->limit(30);
            $grid->column('description', '简介');
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(new SystemConfig(), function (Form $form) {
            $id = $form->getKey();
            $form->text('name', '名称')->required();
            $form->text('key', '键')
                ->creationRules(['required', "unique:system_configs"])
                ->updateRules(['required', "unique:system_configs,key,$id"],
                    ['unique' => '参数不能重复'])
                ->required();
            $form->text('value', '值')->required();
            $form->textarea('description', '简介');
        });
    }
}
