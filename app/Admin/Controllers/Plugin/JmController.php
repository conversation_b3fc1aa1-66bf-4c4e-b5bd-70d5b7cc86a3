<?php

namespace App\Admin\Controllers\Plugin;

use App\Admin\Actions\Jm\DoDrawAction;
use App\Admin\Actions\Jm\ResetVideoAction;
use App\Models\AiUnifyAsset;
use App\Models\PluginJmDraw;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class JmController extends AdminController
{
    protected string $title = '即梦视频列表';

    public function form(): Form
    {
        return Form::make(new PluginJmDraw(), function (Form $form) {
            $form->switch('platform');
        });
    }

    protected function grid(): Grid
    {
        return Grid::make(PluginJmDraw::latest('created_at'), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit();
                if ($actions->row->canResetVideo()) {
                    $actions->append(new ResetVideoAction());
                }
                if ($actions->row->status != AiUnifyAsset::STATUS_SUCCESS) {
                    $actions->append(new DoDrawAction());
                }
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('no', '编号');
                $filter->in('type', '类型')->multipleSelect(PluginJmDraw::TYPE_PLUGIN);
                $filter->equal('status', '状态')->select(AiUnifyAsset::STATUS);
            });
            $grid->column('id');
            $grid->column('no');
            $grid->column('type', '类型')->using(PluginJmDraw::TYPE_PLUGIN);
            $grid->column('prompt', '描述')->limit(20);
            $grid->column('llm_result', '优化描述')->limit(20);
            $grid->column('inputs', '输入')->display(function ($values) {
                $urls   = collect($values)->pluck('url');
                $result = [];
                foreach ($urls as $url) {
                    if (is_array($url)) {
                        foreach ($url as $item) {
                            $result[] = $item;
                        }
                    } else {
                        $result[] = $url;
                    }
                }
                return $result;
            })->image('', 40);
            $grid->column('cover', '结果封面')
                ->image('', 50, 50);
            $grid->column('video_url', '结果视频')
                ->display(fn() => '点击查看')
                ->link(fn() => $this->videoUrlAttr);
            $grid->column('status', '状态')->using(AiUnifyAsset::STATUS);
            $grid->column('发布状态')->display(fn() => $this->asset)->bool();
            $grid->column('error_message', '说明')->limit(10);
            $grid->column('created_at');
            $grid->column('start_at', '执行时间');
            $grid->column('over_at', '完成时间');
        });
    }
}