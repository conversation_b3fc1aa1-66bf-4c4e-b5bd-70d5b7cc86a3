<?php

namespace App\Admin\Controllers\Plugin;

use App\Models\PluginAiPptConfig;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AiPptConfigController extends AdminController
{
    protected string $title = 'AI PPT 配置';

    public function grid(): Grid
    {
        return Grid::make(PluginAiPptConfig::orderBy('created_at'), function (Grid $grid) {
            $grid->column('key_word', '关键字');
            $grid->column('title', '标题');
            $grid->column('type', '类型')->using(PluginAiPptConfig::TYPE);
            $grid->column('values', '内容')->display(fn(
            ) => is_array($this->getValues()) ? json_encode($this->getValues(),
                JSON_UNESCAPED_UNICODE) : $this->getValues())
                ->limit(50);
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }

    public function form(): Form
    {
        return Form::make(new PluginAiPptConfig(), function (Form $form) {
            if ($form->isCreating()) {
                $form->text('key_word', '关键字')
                    ->required()
                    ->rules(['unique:plugin_ai_ppt_configs,key_word']);
                $form->text('title', '标题')
                    ->required();
                $form->select('type', '类型')
                    ->options(PluginAiPptConfig::TYPE)
                    ->required();
            } else {
                $form->text('key_word', '关键字')->readOnly();
                $form->text('title', '标题')->required();
                $form->display('type', '类型')->with(fn() => PluginAiPptConfig::TYPE[$this->type]);
                if ($form->model()->type == PluginAiPptConfig::TYPE_TEXT) {
                    $form->text('values', '内容');
                }
                if ($form->model()->type == PluginAiPptConfig::TYPE_JSON) {
                    $form->keyValue('values', '内容');
                }
                if ($form->model()->type == PluginAiPptConfig::TYPE_LIST) {
                    $form->list('values', '内容')->horizontal(true);
                }
            }
        });
    }
}