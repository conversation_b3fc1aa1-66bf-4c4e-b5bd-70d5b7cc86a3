<?php

namespace App\Admin\Controllers\Bailian;

use App\Admin\Traits\WithUploads;
use App\Models\BailianCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;

class CategoryController extends AdminController
{
    use WithUploads;

    protected string $title = '百炼分类管理';

    public function grid(): Grid
    {
        return Grid::make(BailianCategory::with(['parent', 'user'])->latest(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('cover', '图标')->image('', 60, 60);
            $grid->column('user', '所属用户')->display(function ($user) {
                return $user ? $user->showAllName : '';
            });
            $grid->column('parent.name', '上级分类');
            $grid->column('bailian_id', '百炼分类id');
            $grid->column('type', '类型')->using(BailianCategory::TYPES);
            $grid->column('name', '分类名称');

            $grid->column('status', '状态')->switch();
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }

    public function form(): Form
    {
        return Form::make(new BailianCategory(), function (Form $form) {
            $form->select('type', '分类类型')
                ->options(BailianCategory::TYPES)
                ->required()
                ->default(BailianCategory::TYPE_NORMAL)
                ->load('parent_id', admin_url('bailian/category/ajax'));
            $form->select('parent_id', '上级分类分类')
                ->addDefaultConfig(0, '请选择上级分类');
            $form->text('name', '分类名称')
                ->required();
            $this->cover($form, 'cover', '图标');
            $form->switch('status', '状态')->default(1);
        });
    }

    public function ajax(Request $request)
    {
        $q = $request->q;
        return BailianCategory::where('type', $q)
            ->where(function ($query) use ($q) {
                $query->where('parent_id', 0)
                    ->orWhereNull('parent_id');
            })
            ->select('id', 'name as text')
            ->get();
    }
}