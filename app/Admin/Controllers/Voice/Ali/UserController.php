<?php

namespace App\Admin\Controllers\Voice\Ali;

use App\Models\AudioTimbre;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class UserController extends AdminController
{
    protected string $title = '用户声音复刻';

    public function grid(): Grid
    {
        return Grid::make(AudioTimbre::with(['user'])->latest('id'), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->column('id');
            $grid->column('user.username', '复刻用户')->display(fn() => $this->user->showName);
            $grid->column('voice_id', '音色ID');
            $grid->column('model', '模型');
            $grid->column('image_url', '展示')->image('', 50);
            $grid->column('people', '名称');
            $grid->column('scenarios', '场景')->limit(20);
            $grid->column('url', '试听')
                ->display(fn() => '点击试听')
                ->link(fn() => $this->url, 'target="_blank"');
            $grid->column('status', '状态')->switch();
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }

    public function form(): Form
    {
        return Form::make(new AudioTimbre(), function (Form $form) {
            $form->switch('status', '状态');
        });
    }
}