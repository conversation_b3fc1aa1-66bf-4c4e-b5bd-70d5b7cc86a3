<?php

namespace App\Admin\Controllers\Voice\Ali;

use App\Admin\Traits\WithUploads;
use App\Models\AudioSystemTimbre;
use Dcat\Admin\Form;
use Dcat\Admin\Form\BlockForm;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class SystemController extends AdminController
{
    use WithUploads;

    protected string $title = '阿里系统声音';

    public function grid(): Grid
    {
        return Grid::make(AudioSystemTimbre::latest('id'), function (Grid $grid) {
            $grid->column('id');
            $grid->column('voice_id', '音色ID')->editable();
            $grid->column('model', '模型')->editable();
            $grid->column('image_url', '展示')->image('', 50);
            $grid->column('people', '名称')->editable();
            $grid->column('scenarios', '场景')->limit(20);
            $grid->column('lang', '语言')->editable();
            $grid->column('url', '试听')
                ->display(fn() => '点击试听')
                ->link(fn() => $this->url, 'target="_blank"');
            $grid->column('hz', '采样')->editable();
            $grid->column('format', '格式')->editable();
            $grid->column('status', '状态')->switch();
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }

    public function form(): Form
    {
        return Form::make(new AudioSystemTimbre(), function (Form $form) {
            $form->block(6, function (BlockForm $form) {
                $form->text('people', '名称');
                $this->cover($form, 'image_url', '展示图');
                $form->textarea('scenarios', '场景');
                $form->switch('status', '状态');
                $form->showFooter();
            });
            $form->block(6, function (BlockForm $form) {
                $form->text('voice_id', '音色ID');
                $form->text('model', '模型');
                $form->text('lang', '语言');
                $form->text('hz', '采样');
                $form->text('format', '格式');
                $form->url('url', '试听');
            });
        });
    }
}