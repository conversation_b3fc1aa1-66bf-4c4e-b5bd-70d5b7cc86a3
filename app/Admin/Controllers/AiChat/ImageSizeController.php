<?php

namespace App\Admin\Controllers\AiChat;

use App\Models\DrawImageSize;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class ImageSizeController extends AdminController
{
    protected string $title = '图片生成尺寸管理';

    protected function grid(): Grid
    {
        return Grid::make(DrawImageSize::query()->ordered(), function (Grid $grid) {
            $grid->column('id');
            $grid->column('name', '标题');
            $grid->column('width', '宽');
            $grid->column('height', '高');
            $grid->column('bigmodel', '智普尺寸');
            $grid->column('status', '状态')->switch();
            $grid->column('is_default', '默认')->switch();
            $grid->column('order', '排序')->orderable();
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }

    protected function form(): Form
    {
        return Form::make(new DrawImageSize(), function (Form $form) {
            $form->text('name', '标题')->required();
            $form->number('width', '宽')->required()->default(1024);
            $form->number('height', '高')->required()->default(1024);
            $form->text('bigmodel', '智普尺寸')->required();
            $form->switch('status', '状态')->default(1);
            $form->switch('is_default', '默认')->default(0);
            $form->number('order', '排序')->default(0);
        });
    }
}