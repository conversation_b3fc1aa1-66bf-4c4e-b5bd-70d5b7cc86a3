<?php

namespace App\Admin\Controllers\AiChat;

use App\Admin\Actions\DrawAudio\CopyStyleRowAction;
use App\Models\DrawAudioStyle;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AudioStyleController extends AdminController
{
    protected string $title = '音乐风格管理';

    protected function grid(): Grid
    {
        return Grid::make(DrawAudioStyle::latest(), function (Grid $grid) {
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->append(new CopyStyleRowAction());
            });
            $grid->column('id');
            $grid->column('type')->using(DrawAudioStyle::TYPES);
            $grid->column('group_title', '分组');
            $grid->column('title', '标签');
            $grid->column('status')->switch();
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }

    protected function form(): Form
    {
        return Form::make(DrawAudioStyle::class, function (Form $form) {
            $form->select('type', '分类')
                ->options(DrawAudioStyle::TYPES)
                ->required();
            $form->text('group_title', '分组标题')
                ->required();
            $form->text('title', '标签')
                ->required();
            $form->switch('status')->default(true);
        });
    }
}