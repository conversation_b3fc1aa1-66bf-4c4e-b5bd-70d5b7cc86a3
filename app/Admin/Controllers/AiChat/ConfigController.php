<?php

namespace App\Admin\Controllers\AiChat;

use App\Admin\Traits\WithUploads;
use App\Models\AiChatConfig;
use App\Models\AiChatEngine;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illum<PERSON>\Http\Request;

class ConfigController extends AdminController
{
    use WithUploads;

    protected string $title = 'AI模型参数配置';

    public function ajaxEngine(Request $request)
    {
        $key = $request->input('q');
        return AiChatEngine::where('type', $key)
            ->select('title', 'id')
            ->get()
            ->toArray();
    }

    protected function grid(): Grid
    {
        return Grid::make(AiChatConfig::with([
            'engine', 'multiEngine', 'thinkEngine'
        ])->orderByDesc('is_default')->orderBy('order')->orderByDesc('id'), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('cover', 'logo')->image('', 50, 50);
            $grid->column('name', '名称')->editable();
            $grid->column('key', '模型')->using(AiChatConfig::KEYS);
            $grid->column('engine.title', '主引擎')->append(function () {
                return $this->engine ? $this->getToolsI($this->engine) : '';
            })->append(function () {
                return $this->engine ? $this->getSystemI($this->engine) : '';
            })->append(function () {
                return $this->engine ? $this->getSearchI($this->engine) : '';
            });
            $grid->column('multiEngine.title', '多态引擎')->append(function () {
                return $this->multiEngine ? $this->getToolsI($this->multiEngine) : '';
            })->append(function () {
                return $this->multiEngine ? $this->getSystemI($this->multiEngine) : '';
            })->append(function () {
                return $this->multiEngine ? $this->getSearchI($this->multiEngine) : '';
            });
            $grid->column('thinkEngine.title', '思考引擎')->append(function () {
                return $this->thinkEngine ? $this->getToolsI($this->thinkEngine) : '';
            })->append(function () {
                return $this->thinkEngine ? $this->getSystemI($this->thinkEngine) : '';
            })->append(function () {
                return $this->thinkEngine ? $this->getSearchI($this->thinkEngine) : '';
            });
            $grid->column('token', 'Token');
            $grid->column('status', '状态')->switch();
            $grid->column('order', '排序(小到大)')->editable();
            $grid->column('is_default', '默认')->switch();
            $grid->column('assistant', 'AI助理')->switch();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        });
    }

    protected function form(): Form
    {
        return Form::make(AiChatConfig::class, function (Form $form) {
            $form->text('name', '名称')->required();
            $this->cover($form, 'cover', 'LOGO');
            $form->textarea('remark', '描述');
            $form->select('key', '模型')
                ->options(AiChatConfig::KEYS)
                ->loads(['engine_id', 'engine_multi_id', 'engine_think_id'], [
                    admin_url('ai/config/get_engine'),
                    admin_url('ai/config/get_engine'),
                    admin_url('ai/config/get_engine')
                ], 'id',
                    'title')
                ->required();
            $form->select('engine_id', '主引擎')
                ->options()
                ->required();
            $form->select('engine_multi_id', '多态引擎')
                ->options();
            $form->select('engine_think_id', '深度引擎')
                ->options();
            $form->text('token', 'OpenAiToken')->required();
            $form->switch('status', '状态')->default(1);
            $form->number('order', '排序(小到大)')->default(0);
            $form->switch('is_default', '默认')->default(1);
            $form->switch('assistant', 'AI助理')->default(0);
            $form->keyValue('params', '其他配置');
        });
    }
}