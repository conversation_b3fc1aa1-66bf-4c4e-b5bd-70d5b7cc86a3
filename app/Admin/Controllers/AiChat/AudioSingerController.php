<?php

namespace App\Admin\Controllers\AiChat;

use App\Models\AudioSinger;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AudioSingerController extends AdminController
{
    protected string $title = '创建的歌手';

    protected function grid(): Grid
    {
        return Grid::make(AudioSinger::with([
            'user'
        ])->latest(), function (Grid $grid) {
            $grid->column('id');
            $grid->column('user.username', '账户');
            $grid->column('no', '单号');
            $grid->column('name', '歌手姓名');
            $grid->column('cover', '展示图')->image('', 50);
            $grid->column('audio', '训练音频')->display(function () {
                return '点击查看';
            })->link(fn() => $this->audio_url, '_blank');
            $grid->column('job_id', 'SUNO序号');
            $grid->column('status', '状态')->using(AudioSinger::STATUS);
            $grid->column('over_at', '完成时间');
            $grid->column('created_at', '创建时间');
        });
    }
}