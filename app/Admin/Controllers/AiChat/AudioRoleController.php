<?php

namespace App\Admin\Controllers\AiChat;

use App\Admin\Traits\WithUploads;
use App\Models\AudioRole;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AudioRoleController extends AdminController
{
    use WithUploads;

    protected string $title = 'AI对话角色管理';

    protected function grid(): Grid
    {
        return Grid::make(AudioRole::ordered(), function (Grid $grid) {
            $grid->column('id');
            $grid->column('cover', '展示图')->image('', 50);
            $grid->column('name', '标题');
            $grid->column('description', '描述');
            $grid->column('male', '男名');
            $grid->column('female', '女名');
            $grid->column('about', '角色说明');
            $grid->column('status', '状态')->switch();
            $grid->column('order', '排序')->orderable();
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }

    protected function form(): Form
    {
        return Form::make(new AudioRole(), function (Form $form) {
            $form->text('name', '标题')->required();
            $form->text('description', '描述')->required();
            $form->text('male', '男名')->required();
            $form->text('female', '女名')->required();
            $form->text('about', '角色说明')->required();
            $this->cover($form);
            $form->textarea('remark', '回答要求')
                ->rows(8)
                ->required();
            $form->switch('status', '状态')->default(true);
            $form->text('order', '排序')->default(0)->required();
        });
    }
}