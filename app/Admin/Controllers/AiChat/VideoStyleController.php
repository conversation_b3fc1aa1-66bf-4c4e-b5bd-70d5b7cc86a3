<?php

namespace App\Admin\Controllers\AiChat;

use App\Models\DrawVideoStyle;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class VideoStyleController extends AdminController
{
    protected string $title = '视频风格管理';

    protected function grid(): Grid
    {
        return Grid::make(DrawVideoStyle::ordered(), function (Grid $grid) {
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
            });
            $grid->column('id');
            $grid->column('name', '标题');
            $grid->column('prefix', '修饰词');
            $grid->column('status', '状态')->switch();
            $grid->column('order')->orderable();
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }

    protected function form(): Form
    {
        return Form::make(new DrawVideoStyle(), function (Form $form) {
            $form->text('name', '标题')->required();
            $form->text('prefix', '修饰词')->required();
            $form->switch('status', '状态')->default(1);
        });
    }
}