<?php

namespace App\Admin\Controllers\AiChat;

use App\Admin\Actions\DrawAudio\RefRunAudio;
use App\Admin\Actions\DrawAudio\UpdateAudioStatus;
use App\Models\AiUnifyAsset;
use App\Models\DrawAudio;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AudioController extends AdminController
{
    protected string $title = '生成音乐列表';

    public function form(): Form
    {
        return Form::make(new DrawAudio(), function (Form $form) {
            $form->switch('platform', '是否公开');
        });
    }

    protected function grid(): Grid
    {
        return Grid::make(DrawAudio::latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableEditButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('no', '单号');
                $filter->equal('job_id', '任务ID');
                $filter->like('name', '歌曲名称');
                $filter->equal('status', '状态')->select(AiUnifyAsset::STATUS);
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->append(new UpdateAudioStatus());
                if (in_array($actions->row->status, [
                    AiUnifyAsset::STATUS_INIT,
                    AiUnifyAsset::STATUS_ERROR,
                ])) {
                    $actions->append(new RefRunAudio());
                }
            });

            $grid->column('id')->sortable();
            $grid->column('no', '单号');
            $grid->column('mode', '类型')->using([
                1 => '普通生成',
                2 => '自定义',
            ]);
            $grid->column('job_id', '任务ID');
            $grid->column('prompt', '描述')->limit(50);
            $grid->column('model', '模型');
            $grid->column('name', '歌曲名称');
            $grid->column('tags', '标签');
            $grid->column('score', '积分');
            $grid->column('cover', '封面')->image('', 100);
            $grid->column('audio', '音频')->display(fn() => $this->audio_url);
            $grid->column('video', '视频');
            $grid->column('duration', '时长');
            $grid->column('status', '状态')->using(AiUnifyAsset::STATUS);
            $grid->column('platform', '是否公开')->switch();
            $grid->column('error_message', '错误信息');
            $grid->column('over_at');
            $grid->column('created_at');
        });
    }
}