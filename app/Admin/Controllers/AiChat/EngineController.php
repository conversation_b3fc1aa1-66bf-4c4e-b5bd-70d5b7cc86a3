<?php

namespace App\Admin\Controllers\AiChat;

use App\Models\AiChatConfig;
use App\Models\AiChatEngine;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class EngineController extends AdminController
{
    protected string $title = '聊天引擎管理';

    public function form(): Form
    {
        return Form::make(new AiChatEngine(), function (Form $form) {
            $form->select('type', '驱动')
                ->options(AiChatConfig::KEYS)
                ->required();
            $form->text('title', '名称')->required();
            $form->text('name', '标识')->required();
            $form->url('url', '接口地址')->required();
            $form->text('maxlen', '上下文长度')->required();
            $form->text('maxinput', '最大输入TOKEN')->required();
            $form->text('maxout', '最大输出TOKEN')->required();
            $form->textarea('system', '前缀');
            $form->switch('status', '状态')->default(1);
            $form->switch('has_tools', '是否支持工具')->default(1);
            $form->switch('search', '是否支持搜索')->default(false);
        });
    }

    protected function grid(): Grid
    {
        return Grid::make(AiChatEngine::orderByDesc('type')->orderByDesc('id'), function (Grid $grid) {
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('type', '驱动')
                    ->select(AiChatConfig::KEYS);
                $filter->like('title', '名称');
                $filter->like('name', '标识');
            });
            $grid->column('id');
            $grid->column('type', '驱动')->using(AiChatConfig::KEYS);
            $grid->column('title', '名称');
            $grid->column('name', '标识');
            $grid->column('url', '接口地址');
            $grid->column('maxlen', '上下文长度');
            $grid->column('maxinput', '最大输入TOKEN');
            $grid->column('maxout', '最大输出TOKEN');
            $grid->column('has_tools', '是否支持工具')->switch();
            $grid->column('search', '是否支持联网')->switch();
            $grid->column('system', '前缀')->limit(20);
            $grid->column('status', '状态')->switch();
        });
    }
}