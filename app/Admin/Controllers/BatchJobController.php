<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Jobs\CancelBatch;
use App\Models\JobBatch;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class BatchJobController extends AdminController
{
    protected string $title = '批处理任务';

    public function grid(): Grid
    {
        return Grid::make(JobBatch::class, function (Grid $grid) {
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (! $actions->row->is_finished) {
                    $actions->append(new CancelBatch);
                }
                $actions->disableEdit();
                $actions->disableDelete();
            });

            $grid->disableRowSelector();

            $grid->column('id')
                ->copyable();
            $grid->column('name', '批次名称');
            $grid->column('任务进度')
                ->display(fn() => $this->process)
                ->append('%');
            $grid->column('total_jobs', '任务总数');
            $grid->column('pending_jobs', '等待中任务');
            $grid->column('failed_jobs', '失败任务');
            $grid->column('已完成任务')
                ->display(fn() => $this->processed_jobs);
            $grid->column('is_finished', '完成状态')
                ->bool();
            $grid->column('cancelled_at', '取消时间');
            $grid->column('finished_at', '完成时间');
            $grid->column('created_at');
        });
    }
}