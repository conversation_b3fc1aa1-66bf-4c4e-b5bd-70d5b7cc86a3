<?php

namespace App\Admin\Controllers\FastGpt;

use App\Admin\Traits\WithUploads;
use App\Models\FastgptApp;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AppController extends AdminController
{
    use WithUploads;

    protected string $title = '应用管理';

    public function grid(): Grid
    {
        return Grid::make(FastgptApp::class, function (Grid $grid) {
            $grid->model()->with(['user'])->latest('id');
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->quickSearch(['name'])
                ->placeholder('标题');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '名称');
                $filter->like('user_id', '所属用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
            });

            $grid->column('id', '#ID#');
            $grid->column('image', '图片')->image('', 60, 60);
            $grid->column('name', '标题');
            $grid->column('username', '所属用户')
                ->display(fn() => $this->user->show_name);
            $grid->column('welcome', '提示词');
            $grid->column('prologue', '开场白');
            $grid->column('description', '简介');
            $grid->column('created_at');
        });
    }

}
