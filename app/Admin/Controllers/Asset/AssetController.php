<?php

namespace App\Admin\Controllers\Asset;

use App\Models\AiUnifyAsset;
use App\Models\PluginJmDraw;
use App\Models\PluginKeLing;
use App\Models\PluginMjDraw;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AssetController extends AdminController
{
    protected string $title = '作品统一资源';

    public function grid(): Grid
    {
        return Grid::make(AiUnifyAsset::with(['assetable', 'category', 'activity', 'chatLog'])->latest('id'),
            function (Grid $grid) {
                $grid->actions(function (Grid\Displayers\Actions $actions) {
                    $actions->disableEdit();
                });
                $grid->filter(function (Grid\Filter $filter) {
                    $filter->like('user.username', '用户账号');
                    $filter->like('description', '描述');
                    $filter->equal('status', '状态')->select(AiUnifyAsset::STATUS);
                    $filter->where('type', function ($where) {
                        $where->whereHasMorph('assetable', '*', function ($query) {
                            return $query->whereIn('type', $this->input);
                        });
                    }, '类别')->multipleSelect(array_merge(PluginJmDraw::TYPE_PLUGIN,
                        PluginMjDraw::TYPE_PLUGIN, PluginKeLing::TYPES));
                });
                $grid->column('id', 'ID');
                $grid->column('no', '统一资源号')->copyable();
                $grid->column('用户')->display(fn() => $this->user->showAllName);
                $grid->column('二级编号')
                    ->display(fn() => $this->assetable->no)
                    ->copyable();
                $grid->column('类别')
                    ->display(fn() => $this->assetable->type_text);
                $grid->column('描述')
                    ->display(fn() => $this->assetable->prompt)
                    ->limit(20);
                $grid->column('图片')
                    ->display(fn() => $this->assetable->coverUrlAttr)
                    ->image('', 100);
                $grid->column('视频')
                    ->display(fn(
                    ) => $this->assetable->videoUrlAttr ? sprintf("<a href='%s' target='_blank'>点击查看</a>",
                        $this->assetable->videoUrlAttr) : '无');
                $grid->column('音频')
                    ->display(fn(
                    ) => $this->assetable->audioUrlAttr ? sprintf("<a href='%s' target='_blank'>点击查看</a>",
                        $this->assetable->audioUrlAttr) : '无');
                $grid->column('platform', '发布')->bool();
                $grid->column('category.name', '发布分类');
                $grid->column('activity.title', '参与活动');
                $grid->column('status', '状态')->using(AiUnifyAsset::STATUS);
                $grid->column('失败说明')->display(fn() => $this->assetable->error_message)->limit(10);
                $grid->column('chatLog', '来自聊天')->bool()->append(function () {
                    return sprintf("<br>%s[%s]",
                        $this->chatLog?->chatConfig->name,
                        $this->chatLog?->chatEngine->title
                    );
                });
                $grid->column('assetable.created_at', '创作时间');
                $grid->column('assetable.over_at', '完成时间');
            });
    }

    public function form(): Form
    {
        return Form::make(AiUnifyAsset::with(['assetable'])->latest('id'), function (Form $form) {
            $form->switch('platform');
        });
    }
}