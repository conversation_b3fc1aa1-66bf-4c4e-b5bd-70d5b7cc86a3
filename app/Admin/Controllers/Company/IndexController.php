<?php

namespace App\Admin\Controllers\Company;

use App\Admin\Traits\WithUploads;
use App\Models\Company;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class IndexController extends AdminController
{
    use WithUploads;

    protected string $title = '企业';

    public function grid(): Grid
    {
        return Grid::make(Company::withCount(['users']), function (Grid $grid) {
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('company_name', '部门名称');
                $filter->between('created_at', '创建时间')->datetime();
            });

            $grid->column('id', 'ID')->sortable();
            $grid->column('logo', 'LOGO')->image('', 50, 50);
            $grid->column('img', '营业执照')->thumb(32);
            $grid->column('company_name', '企业名称');
            $grid->column('enterprise_no', '社会统一信用代码');
            $grid->column('legal_name', '法人姓名');
            $grid->column('id_card', '法人身份证号');
            $grid->column('code', '邀请码');
            $grid->column('exp_time', '邀请码到期时间');
            $grid->column('is_open', '状态')->using(Company::IS_OPEN)->label(Company::IS_OPEN_LABEL);
            $grid->column('business_type', '业务介绍类型')->using(Company::TYPE_MAP)->label(Company::TYPE_MAP_LABEL);
            $grid->column('company_type', '业务介绍类型')->using(Company::TYPE_MAP)->label(Company::TYPE_MAP_LABEL);

            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(Company::class, function (Form $form) {
            $form->ignore([
                'company_zu',
                'company_video',
                'company_img',
                'business_zu',
                'business_img',
                'business_video',
            ]);

            $form->text('company_name', '名称')->required();
            $this->cover($form, 'logo', 'LOGO')->width(4);
            $this->cover($form, 'img', '营业执照')->width(4);
            $form->text('legal_name', '法人姓名')->required();
            $form->text('id_card', '法人身份证')->required();
            $form->text('code', '邀请码')->required();
            $form->datetime('exp_time', '邀请码时间');
            $form->switch('is_open', '状态')->default(true);

            $form->radio('company_type', '企业介绍类型')
                ->options(Company::TYPE_MAP)
                ->default(Company::TYPE_IMG)
                ->when(Company::TYPE_IMG, function (Form $form) use ($info) {
                    $images = $form->model()->company_type == Company::TYPE_IMG ?
                        $form->model()->getCompany('system') :
                        '';
                    $this->pictures($form, 'company_img', '企业介绍')->value($images);
                })
                ->when(Company::TYPE_ZU, function (Form $form) use ($info) {
                    $zu = $form->model()->company_type == Company::TYPE_ZU ?
                        $form->model()->company :
                        '';
                    $form->editor('company_zu', '企业介绍')->value($zu);
                })
                ->when(Company::TYPE_VIDEO, function (Form $form) use ($info) {
                    $videos = $form->model()->company_type == Company::TYPE_VIDEO ?
                        $form->model()->getCompany('system') :
                        '';
                    $this->videos($form, 'company_video', '企业介绍')->value($videos);
                });
            $form->radio('business_type', '业务介绍类型')
                ->options(Company::TYPE_MAP)
                ->default(Company::TYPE_IMG)
                ->when(Company::TYPE_IMG, function (Form $form) use ($info) {
                    $images = $form->model()->business_type == Company::TYPE_IMG ?
                        $form->model()->getBusiness('system') :
                        '';
                    $this->pictures($form, 'business_img', '业务介绍')->value($images);
                })
                ->when(Company::TYPE_ZU, function (Form $form) use ($info) {
                    $zu = $form->model()->business_type == Company::TYPE_ZU ?
                        $form->model()->business :
                        '';
                    $form->editor('business_zu', '业务介绍')->value($zu);
                })
                ->when(Company::TYPE_VIDEO, function (Form $form) use ($info) {
                    $videos = $form->model()->business_type == Company::TYPE_VIDEO ?
                        $form->model()->getBusiness('system') :
                        '';
                    $this->videos($form, 'business_video', '业务介绍')->value($videos);
                });
            $form->hidden('company');
            $form->hidden('business');

            $form->saving(function (Form $form) {
                // 根据类型添加 company 字段
                switch ($form->company_type) {
                    case Company::TYPE_ZU:
                        $form->company = request()->company_zu;
                        break;
                    case Company::TYPE_IMG:
                        $form->company = json_encode(explode(',', request()->company_img));
                        break;
                    case Company::TYPE_VIDEO:
                        $form->company = request()->company_video;
                        break;
                }

                switch ($form->company_type) {
                    case Company::TYPE_ZU:
                        $form->business = request()->business_zu;
                        break;
                    case Company::TYPE_IMG:
                        $form->business = json_encode(explode(',', request()->business_img));
                        break;
                    case Company::TYPE_VIDEO:
                        $form->business = request()->business_video;
                        break;
                }
            });
        });
    }

}