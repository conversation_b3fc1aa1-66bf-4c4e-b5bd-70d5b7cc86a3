<?php

namespace App\Admin\Controllers\User\Sign;

use App\Admin\Tables\UserSignTable;
use App\Models\User;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;

class IndexController extends AdminController
{
    protected string $title = '用户签到管理';

    public function grid(): Grid
    {
        return Grid::make(User::with(['lastSign'])
            ->withCount(['signs'])
            ->withMax('signs', 'continuous_day')
            ->latest(), function (Grid $grid) {
            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->disableRowSelector();
            $grid->disableBatchActions();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('username', '账号');
                $filter->like('info.nickname', '昵称');
                $filter->between('lastSign.continuous_day', '连续签到');
                $filter->between('lastSign.sign_at', '最后签到')->datetime();
            });
            $grid->column('id', '用户ID');
            $grid->column('username', '用户账号')->display(fn() => $this->showAllName);
            $grid->column('signs_count', '签到次数')
                ->append('次')
                ->append(
                    function () {
                        return Modal::make()
                            ->size('free')
                            ->title('用户签到记录')
                            ->body(UserSignTable::make(['key' => $this->id]))
                            ->button('查看详情');
                    }
                );
            $grid->column('lastSign.sign_at', '最后一次签到');
            $grid->column('lastSign.continuous_day', '连续签到')
                ->append('次');
            $grid->column('signs_max_continuous_day', '最多连续签到')
                ->append('次');
        });
    }
}