<?php

namespace App\Admin\Controllers\Resource;

use App\Admin\Traits\WithUploads;
use App\Models\Resources;
use App\Models\SystemResource;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class SystemController extends AdminController
{
    use WithUploads;

    protected string $title = '背景图';

    public function grid(): Grid
    {
        return Grid::make(new SystemResource(), function (Grid $grid) {
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '名称');
            });
            $grid->column('id', '#ID#');
            $grid->column('cover', '图')->image('', 50, 50);
            $grid->column('name', '名称');
            $grid->column('type', '类型');
            $grid->column('key', 'KEY');
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(new SystemResource(), function (Form $form) {
            $form->text('name', '名称')->required();
            $form->text('type', '类型')->required();
            $form->text('key', 'KEY')->required();
            $form->number('order', '排序')->required();
            $this->cover($form, 'cover', '背景图');
        });
    }
}
