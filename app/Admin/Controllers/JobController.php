<?php

namespace App\Admin\Controllers;

use App\Models\Job;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class JobController extends AdminController
{
    protected string $title = '队列';

    public function grid(): Grid
    {
        return Grid::make(Job::orderByDesc('available_at'), function (Grid $grid) {
            $grid->async();

            $grid->disableActions();
            $grid->disableRowSelector();
            $grid->disableCreateButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('queue', '队列名称');
            });

            $grid->column('id', '#ID#');
            $grid->column('queue', '队列名称');
            $grid->column('attempts', '执行次数');
            $grid->column('reserved_at', '启动时间');
            $grid->column('available_at', '预备时间');
            $grid->column('created_at', '创建时间');
        });
    }
}