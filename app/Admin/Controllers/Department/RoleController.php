<?php

namespace App\Admin\Controllers\Department;

use App\Admin\Traits\WithUploads;
use App\Models\CompanyRole;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class RoleController extends AdminController
{
    use WithUploads;

    protected string $title = '角色';

    public function grid(): Grid
    {
        return Grid::make(CompanyRole::withCount(['users']), function (Grid $grid) {
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->onlyTrashed();

                $filter->like('name', '角色名称');
            });

            $grid->column('id', '#ID#');
            $grid->column('name', '角色名称');
            $grid->column('label', '标签');
            $grid->column('description', '备注');

            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(CompanyRole::class, function (Form $form) {
            $form->text('name', '角色名称')
                ->required();
            $form->text('label', '标签')
                ->required();
            $form->textarea('description', '备注');
        });
    }

}