<?php

namespace App\Admin\Controllers\Note;

use App\Admin\Traits\WithUploads;
use App\Models\Note;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class NoteController extends AdminController
{
    use WithUploads;

    protected string $title = '小记管理';

    public function grid(): Grid
    {
        return Grid::make(Note::class, function (Grid $grid) {
            $grid->model()
                ->with(['tags', 'knowledgeSet.knowledge'])
                ->latest('id');
            $grid->showBatchDelete();

            $grid->quickSearch(['title'])
                ->placeholder('标题');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '标题');
                $filter->equal('is_top', '是否置顶')
                    ->select([
                        0 => '否',
                        1 => '是'
                    ]);
                $filter->equal('is_archived', '是否归档')
                    ->select([
                        0 => '否',
                        1 => '是'
                    ]);
                $filter->like('user_id', '所属用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
            });

            $grid->column('id', '#ID#');
            $grid->column('title', '标题');
            $grid->column('knowledgeSet', '知识库')
                ->display(function () {
                    return $this->knowledgeSet->knowledge->name ?? '---';
                });
            $grid->column('username', '所属用户')
                ->display(fn() => $this->user->show_name);
            $grid->column('tags', '标签')
                ->pluck('name')
                ->label();
            $grid->column('is_top', '是否置顶')->bool();
            $grid->column('is_archived', '是否归档')->bool();
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(Note::with('tags'), function (Form $form) {
//            $form->text('title', '标题')->required();
            $form->select('user_id', '隶属用户')
                ->options(function ($userId) {
                    if ($userId) {
                        return [$userId => User::find($userId)->showName];
                    } else {
                        return [];
                    }
                })
                ->ajax(route('admin.user.users.ajax'))
                ->load('tags', route('admin.note_tags.ajax'))
                ->required();
            $form->multipleSelect('tags', '标签')
                ->customFormat(function ($v) {
                    return array_column($v, 'id');
                });
            $form->switch('is_top', '是否置顶');
            $form->switch('is_archived', '是否归档');
            $form->editor('content', '内容详情')->required();
        });
    }
}
