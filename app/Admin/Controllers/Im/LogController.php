<?php

namespace App\Admin\Controllers\Im;

use App\Models\ImPushLog;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class LogController extends AdminController
{

    protected string $title = '操作日志';

    public function grid(): Grid
    {
        return Grid::make(new ImPushLog(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableBatchActions();
            $grid->disableActions();

            $grid->model()->latest('created_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('status')->select(ImPushLog::STATUS_MAP);
            });

            $grid->column('id', '#ID#');
            $grid->column('url', 'URL');
            $grid->column('query', 'url参数');
            $grid->column('params', '参数')
                ->display(function () {
                    return $this->getParams();
                });
            $grid->column('result', '结果');
            $grid->column('status', '状态')
                ->using(ImPushLog::STATUS_MAP);
            $grid->column('created_at');
        });
    }

}
