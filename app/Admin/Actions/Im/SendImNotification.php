<?php

namespace App\Admin\Actions\Im;

use App\Models\ImNotice;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;

class SendImNotification extends RowAction
{
    protected string $title = '发送推送';

    public function handle(): Response
    {
        try {
            $content = ImNotice::find($this->getKey());
            $content->send();
            return $this->response()->success('操作成功')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
    }

    public function confirm(): array
    {
        return [
            $this->title,
            "确定要{$this->title}么?"
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}