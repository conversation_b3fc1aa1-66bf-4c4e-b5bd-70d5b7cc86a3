<?php

namespace App\Admin\Actions\Support;

use App\Admin\Forms\Support\ReplyFeedbackForm;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;

class ReplyAction extends RowAction
{
    protected string $title = '回复反馈';

    public function render(): string
    {
        return Modal::make()
            ->xl()
            ->centered()
            ->title('回复反馈')
            ->body(ReplyFeedbackForm::make([], $this->getKey())->payload([
                'id' => $this->getKey(),
            ]))
            ->button($this->title);
    }

}