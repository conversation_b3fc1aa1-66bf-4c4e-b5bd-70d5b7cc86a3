<?php

namespace App\Admin\Actions\Bailian\Knowledge;

use App\Models\BailianKnowledge;
use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use Dcat\Admin\Grid\RowAction;

class SubmitIndexAddDocumentsJobAction extends RowAction
{
    protected string $title = '追加分类下的文档';

    public function handle()
    {
        $knowledge  = BailianKnowledge::find($this->getKey());
        $categories = $knowledge->categories()->pluck('bailian_id')->toArray();
        if (empty($categories)) {
            return $this->response()->error('该知识没有分类');
        }

        $res = app(KnowledgeTool::class)->submitIndexAddDocumentsJob(
            $knowledge->knowledge_id,
            $categories,
            $knowledge->workspace_id,
            $knowledge->source_type,
        );
        if ($res->status != 200) {
            return $this->response()->error($res->message);
        }
        $config           = $knowledge->config;
        $config['job_id'] = $res->data->id;
        $knowledge->update([
            'config' => $config
        ]);
        return $this->response()->success('提交成功，请等待结果')->refresh();
    }

}