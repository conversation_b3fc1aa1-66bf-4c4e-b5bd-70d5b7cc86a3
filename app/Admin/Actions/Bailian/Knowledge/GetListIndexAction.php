<?php

namespace App\Admin\Actions\Bailian\Knowledge;

use App\Models\BailianKnowledge;
use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use Dcat\Admin\Grid\RowAction;

class GetListIndexAction extends RowAction
{
    protected string $title = '查询索引列表';

    public function handle()
    {
        $knowledge = BailianKnowledge::find($this->getKey());

        $res = app(KnowledgeTool::class)->getListIndices(
            $knowledge->workspace_id,
        );
        dd($res);
        return $this->response()->success('提交成功，请等待结果')->refresh();
    }

}