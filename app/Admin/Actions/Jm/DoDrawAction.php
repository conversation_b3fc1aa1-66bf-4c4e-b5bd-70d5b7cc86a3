<?php

namespace App\Admin\Actions\Jm;

use App\Jobs\Plugin\JmDrawJob;
use App\Models\AiUnifyAsset;
use App\Models\PluginJmDraw;
use Dcat\Admin\Grid\RowAction;

class DoDrawAction extends RowAction
{
    protected string $title = '执行任务(重新)';

    public function handle()
    {
        $id            = $this->getKey();
        $model         = PluginJmDraw::find($id);
        $model->status = AiUnifyAsset::STATUS_INIT;
        $model->save();
        JmDrawJob::dispatch($model);
        return $this->response()->success('任务以加入队列')->refresh();
    }

    public function confirm()
    {
        return ['确定执行任务吗？', '执行任务后，视频会重新生成'];
    }
}