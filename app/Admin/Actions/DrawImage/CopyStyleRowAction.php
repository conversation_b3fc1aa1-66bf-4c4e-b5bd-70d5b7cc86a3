<?php

namespace App\Admin\Actions\DrawImage;

use App\Models\DrawImageStyle;
use Dcat\Admin\Grid\RowAction;

class CopyStyleRowAction extends RowAction
{
    protected string $title = '复制风格';

    public function handle()
    {
        $item = DrawImageStyle::find($this->getKey());
        $copy = $item->replicate()->fill([
            'name'   => $item->name.'_copy',
            'status' => 0,
        ]);
        if ($copy->save()) {
            return $this->response()->success('复制成功')->refresh();
        } else {
            return $this->response()->error('复制失败');
        }
    }

    public function confirm()
    {
        return '确认复制该风格';
    }
}