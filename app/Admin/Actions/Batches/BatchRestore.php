<?php

namespace App\Admin\Actions\Batches;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class BatchRestore extends BatchAction
{
    protected string $title = '批量恢复';

    public function __construct(protected ?string $model = null)
    {
        parent::__construct();
    }

    public function handle(Request $request): Response
    {
        $model = $request->get('model');

        foreach ((array) $this->getKey() as $key) {
            $model::withTrashed()->findOrFail($key)->restore();
        }

        return $this->response()->success('已恢复')->refresh();
    }

    public function confirm(): array
    {
        return [
            '恢复数据',
            '确定批量恢复选中的数据么？'
        ];
    }

    public function parameters(): array
    {
        return [
            'model' => $this->model,
        ];
    }
}