<?php

namespace App\Admin\Actions\Modules;

use App\Models\Configuration;
use App\Models\ModuleLog;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\RowAction;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Nwidart\Modules\Laravel\Module;

class DisableModule extends RowAction
{
    protected string $title = '禁用';

    public function handle(): Response
    {
        try {
            $module = app('modules')->find($this->getKey());
            $class  = sprintf('\\%s\\%s\\Bootstrap', config('modules.namespace'), $module->getName());
            if (class_exists($class)) {
                call_user_func([$class, 'uninstall']);
            }
            $version = $module->get('version');
            $module->disable();

            Configuration::ofModule($module->getName())->delete();

            Artisan::call('modelCache:clear');

            ModuleLog::create([
                'name'    => $module->getName(),
                'user'    => Admin::user(),
                'action'  => 'disable',
                'version' => $version,
            ]);
            $this->updateCurrentVersion($module);

            return $this->response()
                ->success($this->getKey().'模块禁用成功')
                ->refresh();
        } catch (Exception $exception) {
            return $this->response()
                ->error($exception->getMessage())
                ->refresh();
        }
    }

    public function updateCurrentVersion(Module $module): void
    {
        $file = app('files')->get(base_path('modules_versions.json'));
        $json = json_decode($file, true);

        unset($json[$module->getName()]);

        app('files')->put(base_path('modules_versions.json'), json_encode($json, JSON_PRETTY_PRINT));
    }

    public function confirm(): array
    {
        return [
            '确定要禁用[ '.$this->getRow()->name.' ]模块么？',
            '禁用模块后可能会导致模块内所有数据的丢失，请谨慎操作！',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}
