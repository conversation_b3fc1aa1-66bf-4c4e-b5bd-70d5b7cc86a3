<?php

namespace App\Admin\Actions\Modules;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SqlExportModule extends RowAction
{
    protected string $title = '导出';

    public function handle(): Response
    {
        try {
            $module     = Str::lower($this->getKey());
            $moduleFile = $this->saveFile($module);

            return $this->response()
                ->success($this->getKey().'模块数据导出成功')
                ->locationToIntended($moduleFile);
        } catch (Exception $exception) {
            return $this->response()
                ->error($exception->getMessage())
                ->refresh();
        }
    }

    private function saveFile(string $module): string
    {
        $db   = config('database.connections.mysql.database');
        $file = "sql/$module/{$module}_".date('YmdHis').".sql";
        if (Storage::exists($file)) {
            Storage::delete($file);
        }

        $tables    = Schema::getAllTables();
        $dbNameKey = 'Tables_in_'.$db;

        foreach ($tables as $tab) {
            if (Str::startsWith($tab->{$dbNameKey}, $module)) {
                $table    = $tab->{$dbNameKey};
                $list     = DB::table($table)->get();
                $fields   = Schema::getColumnListing($table);
                $fieldStr = "`".implode("`,`", $fields)."`";
                foreach ($list as $item) {
                    $values = [];
                    foreach ($item as $value) {
                        if (is_null($value)) {
                            $values[] = 'null';
                        } elseif (is_string($value)) {
                            $values[] = "'".$value."'";
                        } else {
                            $values[] = $value;
                        }
                    }
                    $valueStr = implode(",", $values);
                    $sql      = "INSERT INTO `$db`.`$table` ($fieldStr) VALUES ($valueStr);";
                    Storage::append($file, $sql);
                }
            }
        }
        return Storage::url($file);
    }

    public function confirm(): array
    {
        return [
            '确定要导出[ '.$this->getRow()->name.' ]模块数据么？',
            'export',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}
