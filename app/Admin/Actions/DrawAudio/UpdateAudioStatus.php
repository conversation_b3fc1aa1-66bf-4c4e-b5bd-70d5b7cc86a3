<?php

namespace App\Admin\Actions\DrawAudio;

use App\Jobs\Audio\AudioQueryJob;
use App\Models\DrawAudio;
use Dcat\Admin\Grid\RowAction;

class UpdateAudioStatus extends RowAction
{
    protected string $title = '更新状态';

    public function handle()
    {
        $audio = DrawAudio::find($this->getKey());
        AudioQueryJob::dispatch($audio);
        return $this->response()->success('执行成功')->refresh();
    }

}