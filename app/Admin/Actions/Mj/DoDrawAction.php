<?php

namespace App\Admin\Actions\Mj;

use App\Models\AiUnifyAsset;
use App\Models\PluginMjDraw;
use Dcat\Admin\Grid\RowAction;

class DoDrawAction extends RowAction
{
    protected string $title = '执行任务(重新)';

    public function handle()
    {
        $id                   = $this->getKey();
        $model                = PluginMjDraw::find($id);
        $model->error_message = null;
        $model->status        = AiUnifyAsset::STATUS_INIT;
        $model->save();
        return $this->response()->success('重置状态成功,等待排队')->refresh();
    }

    public function confirm()
    {
        return ['确定执行任务吗？', '执行任务后，任务会重新生成'];
    }
}