<?php

namespace App\Admin\Actions\Review;

use App\Admin\Forms\ReviewForm;
use App\Enums\ApplyStatus;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;

class ApplyReview extends RowAction
{
    protected string $title = '审核';

    public function __construct(protected string $model)
    {
        parent::__construct($this->title);
    }

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->title($this->title)
            ->centered()
            ->button($this->title)
            ->body(ReviewForm::make([], $this->getKey())->payload(['model' => $this->model, 'id' => $this->getKey()]));
    }
}