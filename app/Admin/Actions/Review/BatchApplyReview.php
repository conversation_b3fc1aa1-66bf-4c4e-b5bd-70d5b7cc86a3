<?php

namespace App\Admin\Actions\Review;

use App\Admin\Forms\BatchReviewForm;
use App\Admin\Forms\ReviewForm;
use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Widgets\Modal;

class BatchApplyReview extends BatchAction
{
    protected string $title = '批量审核';

    public function __construct(protected ?string $model = null)
    {
        parent::__construct();
    }

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->title($this->title)
            ->centered()
            ->button($this->title)
            ->body(BatchReviewForm::make([], $this->getKey())->payload(['model' => $this->model]))
            ->onLoad($this->getModalScript());
    }

    protected function getModalScript(): string
    {
        return <<<JS
var key = {$this->getSelectedKeysScript()}
$('#batch-review-id').val(key);
JS;
    }
}