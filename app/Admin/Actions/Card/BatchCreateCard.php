<?php

namespace App\Admin\Actions\Card;

use App\Admin\Forms\Card\BatchCreateCardForm;
use App\Models\CardBatch;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;

class BatchCreateCard extends RowAction
{
    protected string $title = '批次生成卡';

    public function render(): string
    {
        $batch = CardBatch::find($this->getKey());
        return Modal::make()
            ->size('free')
            ->title("批次生成卡片【{$batch->name}】")
            ->body(BatchCreateCardForm::make([], $this->getKey())->payload(['batch_id' => $this->getKey()]))
            ->button($this->title);
    }
}