<?php

namespace App\Admin\Tables;

use App\Models\User;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use Illuminate\Support\Facades\DB;
use Modules\User\Models\UserSign;

class UserSignTable extends LazyRenderable
{
    public function grid(): Grid
    {
        return Grid::make(UserSign::ofUser($this->payload['key'])
            ->with(['user', 'info', 'rewards'])
            ->orderByDesc('sign_at'), function (Grid $grid) {
            $grid->disableActions();
            $grid->disableRowSelector();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->between('sign_at', '签到时间')->datetime();
            });
            $grid->column('user.username', '用户')->display(fn() => $this->user->showAllName);
            $grid->column('sign_at', '签到时间');
            $grid->column('continuous_day', '连续签到');
            $grid->column('message', '奖励')->display(fn() => str_replace('\n', '<br>', $this->message));
        });
    }
}