<?php

namespace App\Admin\Forms\Bailian;

use App\Models\BailianKnowledge;
use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class GetRetrieveForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $knowledge = BailianKnowledge::find($this->payload['id']);

        $query    = $input['query'];
        $images   = $input['image'];
        $response = app(KnowledgeTool::class)
            ->getRetrieve(
                $knowledge->knowledge_id,
                $knowledge->workspace_id,
                $query,
                [$images]
            );
        dd($response);
        return $this->response()->success('操作成功')->refresh();
    }

    public function form(): void
    {
        $this->textarea('query', '检索内容');
        $this->image('image', '检索图片')
            ->uniqueName()
            ->removable(false)
            ->retainable()
            ->downloadable()
            ->autoUpload()
            ->thumbnail('thumb', config('admin.thumb_size'), config('admin.thumb_size'))
            ->accept('jpg,png,gif,jpeg')
            ->move(date('Y/m/d'))
            ->override()
            ->saveFullUrl(true);
    }

}