<?php

namespace App\Admin\Forms\Card;

use App\Models\Card;
use App\Models\CardBatch;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;

class BatchCreateCardForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        extract($input);
        if ($number <= 0) {
            return $this->response()->error('生成的卡数量要大于0');
        }
        $batch     = CardBatch::find($this->payload['batch_id']);
        $data      = [];
        $prefix    = $batch->prefix;
        $prefix    = ($prefix ?: '').date('ymd');
        $start     = Card::where('no', 'like', "{$prefix}%")->count() + 1;
        $end       = $start + $number;
        $failureAt = $time > 0 ? now()->addDays($time)->endOfDay()->toDateTimeString() : null;
        if ($end >= 1000000) {
            return $this->response()->error('今日该批次生成超量');
        }

        for ($start = 1; $start < $end; $start++) {
            $data[] = [
                'batch_id'   => $batch->id,
                'score'      => $score,
                'no'         => $prefix.sprintf('%06d', $start),
                'secret'     => $this->generateRandomString(10),
                'failure_at' => $failureAt,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('cards')->insert($data);
        return $this->response()->success('创建完成')->refresh();
    }

    protected function generateRandomString($length = 8)
    {
//        $characters       = 'abcdefghijkmnpqrstuvwxyzABCDEFGHIJKLMNPQRSTUVWXYZ23456789';
        $characters       = 'abcdefghijkmnpqrstuvwxyz23456789';
        $randomString     = '';
        $charactersLength = strlen($characters);
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[random_int(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    public function form(): void
    {
        $batch = CardBatch::find($this->payload['batch_id']);
        $this->number('number', '生成数量')->default(100);
        $this->text('score', '积分')->default($batch->def_score);
        $this->text('time', '有效（天）')->default($batch->def_time);
    }

    public function confirm(?string $title = null, ?string $content = null)
    {
        return [
            'title'   => '确认操作',
            'content' => '确认生成该批次的兑换卡？',
        ];
    }
}