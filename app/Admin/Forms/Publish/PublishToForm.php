<?php

namespace App\Admin\Forms\Publish;

use App\Models\PublishCategory;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Exception;

class PublishToForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $itemId    = $this->payload['item_id'];
        $itemClass = $this->payload['item_class'];
        if (blank($itemId) || blank($itemClass)) {
            throw new Exception('参数不正确');
        }
        $model = new $itemClass();
        $item  = $model->find($itemId);
        if (! $item) {
            throw new Exception('资源不存在');
        }
        if (method_exists($item, 'publishTo')) {
            $item->publishTo($input['title'], $input['category_id'], $input['description'], $input['tags']);
            return $this->response()->success('发布成功')->refresh();
        } else {
            return $this->response()->error('模型不支持发布');
        }
    }

    public function form(): void
    {
        $itemId    = $this->payload['item_id'];
        $itemClass = $this->payload['item_class'];
        if (blank($itemId) || blank($itemClass)) {
            throw new Exception('参数不正确');
        }
        $model = new $itemClass();
        $item  = $model->find($itemId);
        if (! $item) {
            throw new Exception('资源不存在');
        }
        $this->text('发布类型')->disable()->value($item->getPublishType());
        $this->text('title', '发布标题')->required();
        $this->select('category_id', '发布到分类')
            ->options(function () {
                return PublishCategory::ofEnabled()->pluck('name', 'id')->toArray();
            })
            ->required();
        $this->list('tags', '标签');
        $this->textarea('description', '发布描述')
            ->default(method_exists($item, 'getPublishDescription') ? $item->getPublishDescription() : '')
            ->required();
    }
}