<?php

use App\Admin\Controllers\AiChat\AudioController;
use App\Admin\Controllers\AiChat\AudioRoleController;
use App\Admin\Controllers\AiChat\AudioRoomController;
use App\Admin\Controllers\AiChat\AudioSingerController;
use App\Admin\Controllers\AiChat\AudioStyleController;
use App\Admin\Controllers\AiChat\ConfigController;
use App\Admin\Controllers\AiChat\DrawImageController;
use App\Admin\Controllers\AiChat\EngineController;
use App\Admin\Controllers\AiChat\ImageSizeController;
use App\Admin\Controllers\AiChat\StyleController;
use App\Admin\Controllers\AiChat\VideoController;
use App\Admin\Controllers\AiChat\VideoSizeController;
use App\Admin\Controllers\AiChat\VideoStyleController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
], function (Router $router) {
    $router->get('ai/config/get_engine',
        [ConfigController::class, 'ajaxEngine']);
    $router->resource('ai/chat/config', ConfigController::class);
    $router->resource('ai/chat/engine', EngineController::class);
    $router->resource('ai/image/size', ImageSizeController::class);
    $router->resource('ai/image/style', StyleController::class);
    $router->resource('ai/video/style', VideoStyleController::class);
    $router->resource('ai/video/size', VideoSizeController::class);
    $router->resource('ai/audio/style', AudioStyleController::class);
    $router->resource('ai/images', DrawImageController::class);
    $router->resource('ai/videos', VideoController::class);
    $router->resource('ai/audios', AudioController::class);
    $router->resource('ai/singer', AudioSingerController::class);

    $router->resource('ai/room/index',
        AudioRoomController::class);
    $router->resource('ai/room/role',
        AudioRoleController::class);
});
