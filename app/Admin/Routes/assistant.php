<?php

use App\Admin\Controllers\AiChat\AudioController;
use App\Admin\Controllers\AiChat\AudioRoleController;
use App\Admin\Controllers\AiChat\AudioRoomController;
use App\Admin\Controllers\AiChat\AudioSingerController;
use App\Admin\Controllers\AiChat\AudioStyleController;
use App\Admin\Controllers\AiChat\ConfigController;
use App\Admin\Controllers\AiChat\DrawImageController;
use App\Admin\Controllers\AiChat\EngineController;
use App\Admin\Controllers\AiChat\ImageSizeController;
use App\Admin\Controllers\AiChat\StyleController;
use App\Admin\Controllers\AiChat\VideoController;
use App\Admin\Controllers\AiChat\VideoSizeController;
use App\Admin\Controllers\AiChat\VideoStyleController;
use App\Admin\Controllers\Assistant\ChatController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
], function (Router $router) {
    $router->resource('assistant/chat_list', ChatController::class);
});
