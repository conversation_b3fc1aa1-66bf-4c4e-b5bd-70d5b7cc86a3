<?php

use App\Admin\Controllers\Vidu\ViduCategoryController;
use App\Admin\Controllers\Vidu\ViduDrawController;
use App\Admin\Controllers\Vidu\ViduTemplateController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
], function (Router $router) {
    $router->resource('vidu_categories', ViduCategoryController::class);
    $router->resource('vidu_templates', ViduTemplateController::class);
    $router->resource('vidu_draws', ViduDrawController::class);
});
