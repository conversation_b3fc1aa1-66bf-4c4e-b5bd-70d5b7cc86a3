<?php

use App\Admin\Controllers\Voice\Ali\SystemController;
use App\Admin\Controllers\Voice\Ali\UserController;
use App\Admin\Controllers\Voice\Volcengine\AudioTtsController;
use App\Admin\Controllers\Voice\Volcengine\AudioVolcengineRoleController;
use App\Admin\Controllers\Voice\Xunfei\SystemController as XfSystemController;
use App\Admin\Controllers\Voice\Xunfei\UserController as XfUserController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'prefix' => 'voice'
], function (Router $router) {
    $router->resource('ali/system', SystemController::class);
    $router->resource('ali/user', UserController::class);
    $router->resource('xunfei/system', XfSystemController::class);
    $router->resource('xunfei/user', XfUserController::class);
    $router->resource('huoshan/audio_tts', AudioTtsController::class);
    $router->resource('huoshan/role', AudioVolcengineRoleController::class);
});
