<?php

namespace App\Admin\Traits;

use Dcat\Admin\Form;
use Dcat\Admin\Widgets\Form as WidgetForm;

trait WithUploads
{
    public function cover(
        Form|WidgetForm $form,
        string $field = 'cover',
        string $label = '封面图',
        bool $fullUrl = false
    ): Form\Field\Image {
        return $form->image($field, $label)
            ->uniqueName()
            ->removable(false)
            ->retainable()
            ->downloadable()
            ->autoUpload()
            ->thumbnail('thumb', config('admin.thumb_size'), config('admin.thumb_size'))
            ->accept('jpg,png,gif,jpeg')
            ->move(date('Y/m/d'))
            ->override()
            ->saveFullUrl($fullUrl);
    }

    public function pictures(
        Form|WidgetForm $form,
        string $field = 'pictures',
        string $label = '轮播图'
    ): Form\Field\MultipleImage {
        return $form->multipleImage($field, $label)
            ->uniqueName()
            ->removable(false)
            ->retainable()
            ->downloadable()
            ->autoUpload()
            ->sortable()
            ->thumbnail('thumb', config('admin.thumb_size'), config('admin.thumb_size'))
            ->accept('jpg,png,gif,jpeg')
            ->move(date('Y/m/d'))
            ->override();
    }

    public function file(
        Form|WidgetForm $form,
        string $field = 'file',
        string $label = '文件'
    ): Form\Field\File {
        return $form->file($field, $label)
            ->uniqueName()
            ->removable()
            ->retainable()
            ->downloadable()
            ->autoUpload()
            ->move(date('Y/m/d'))
            ->override();
    }

    public function attachments(
        Form|WidgetForm $form,
        string $field = 'attachments',
        string $label = '附件'
    ): Form\Field\MultipleFile {
        return $form->multipleFile($field, $label)
            ->uniqueName()
            ->removable()
            ->retainable()
            ->downloadable()
            ->autoUpload()
            ->move(date('Y/m/d'))
            ->override();
    }

    public function videos(
        Form|WidgetForm $form,
        string $field = 'videos',
        string $label = '视频'
    ): Form\Field\MultipleFile {
        return $form->multipleFile($field, $label)
            ->uniqueName()
            ->removable(false)
            ->retainable()
            ->downloadable()
            ->autoUpload()
            ->sortable()
            ->accept('mp4,avi,mov,wmv')
            ->move(date('Y/m/d'))
            ->override();
    }
}