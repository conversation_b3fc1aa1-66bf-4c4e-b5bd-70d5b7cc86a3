<?php

namespace App\Admin\Renders\Vidu;

use App\Models\PluginViduTemplate;
use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Table;

class InputInstructionData extends LazyRenderable
{
    public function render(): string
    {
        $id               = $this->payload['key'];
        $info             = PluginViduTemplate::find($id);
        $inputInstruction = $info->input_instruction;
        $data             = [];
        $keyName          = [
            'attention'          => '注意事项',
            'image_count'        => '图片数量',
            'image_url'          => '图片',
            'person_count'       => '人物数量',
            'effect_boundary'    => '效果边界',
            'prompt_instruction' => '提示词',
        ];
        foreach ($inputInstruction as $key => $value) {
            $data[] = [
                'key'   => $key,
                'name'  => $keyName[$key] ?? $key,
                'value' => is_array($value) ? implode(', ', $value) : $value,
            ];
        }
        return Table::make(['参数', '名称', '介绍'], $data)->withBorder()->render();
    }
} 