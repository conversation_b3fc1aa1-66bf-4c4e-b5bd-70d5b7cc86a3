<?php

namespace App\Admin\Renders\Bailian\Knowledge;

use App\Models\BailianKnowledge;
use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Table;
use Illuminate\Support\Str;

class GetIndexDocumentsTable extends LazyRenderable
{
    /**
     * 定义静态资源
     */
    protected static $js = [];

    protected static $css = [];

    /**
     * 渲染表格
     *
     * @return string
     */
    public function render(): string
    {
        // 添加loading效果
//        $loading = $this->renderLoading();

        // 获取数据
        $data = $this->fetchData();
        if (! $data['success']) {
            return $this->renderError($data['message']);
        }

        // 处理数据
        $tableData = $this->processData($data['documents'] ?? []);

        // 渲染表格
        return $this->renderTable($tableData);
    }

    /**
     * 获取数据
     *
     * @return array
     */
    protected function fetchData(): array
    {
        try {
            $knowledge = BailianKnowledge::find($this->payload['key'] ?? 0);

            if (! $knowledge) {
                return ['success' => false, 'message' => '未找到知识库数据'];
            }

            $res = app(KnowledgeTool::class)->ListIndexDocuments(
                $knowledge->knowledge_id,
                $knowledge->workspace_id,
            );

            if (! $res->success) {
                return ['success' => false, 'message' => $res->message ?? '获取文档列表失败'];
            }

            return [
                'success'   => true,
                'documents' => $res->data->documents ?? []
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => '系统异常: '.$e->getMessage()];
        }
    }

    /**
     * 处理文档数据
     *
     * @param  array  $documents
     * @return array
     */
    protected function processData($documents): array
    {
        $data = [];

        foreach ($documents as $document) {
            // 格式化文件大小
            $size = $this->formatBytes($document->size ?? 0);

            // 为长文本添加工具提示
            $id           = $this->formatCell($document->id ?? '');
            $name         = $this->formatCell($document->name ?? '');
            $message      = $this->formatCell($document->message ?? '');
            $status       = $this->formatStatus($document->status ?? '');
            $documentType = $document->documentType;

            $data[] = [
                $id,
                $name,
                $size,
                $status,
                $message,
                $documentType,
            ];
        }

        return $data;
    }

    /**
     * 格式化单元格内容
     *
     * @param  string  $content
     * @return string
     */
    protected function formatCell($content): string
    {
        $text      = htmlspecialchars($content);
        $shortened = Str::limit($text, 50);

        return '<span title="'.$text.'" class="content-text">'.$shortened.'</span>';
    }

    /**
     * 格式化状态显示
     *
     * @param  string  $status
     * @return string
     */
    protected function formatStatus($status): string
    {
        $class = 'badge badge-';

        switch (strtolower($status)) {
            case 'completed':
                $class .= 'success';
                break;
            case 'failed':
                $class .= 'danger';
                break;
            case 'processing':
                $class .= 'info';
                break;
            default:
                $class .= 'secondary';
        }

        return "<span class=\"{$class}\">{$status}</span>";
    }

    /**
     * 格式化文件大小
     *
     * @param  int  $bytes
     * @return string
     */
    protected function formatBytes($bytes): string
    {
        if (function_exists('formatBytes')) {
            return formatBytes($bytes);
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow   = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow   = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2).' '.$units[$pow];
    }

    /**
     * 渲染表格
     *
     * @param  array  $data
     * @return string
     */
    protected function renderTable(array $data): string
    {
        $title = ['文档ID', '文档名称', '大小', '状态', '消息', '文档类型'];

        // 将表格包装在可滚动容器中
        $table = '<div class="table-container" id="documents-table-wrapper">'.
            Table::make($title, $data)
                ->withBorder()
                ->tableClass('table-documents table-hover')
                ->depth(1)
                ->render().
            '</div>';

        return $table.$this->renderScript();
    }

    /**
     * 渲染错误信息
     *
     * @param  string  $message
     * @return string
     */
    protected function renderError($message): string
    {
        return <<<HTML
<div class="alert alert-danger">
    <i class="feather icon-alert-circle"></i> {$message}
</div>
HTML;
    }

    /**
     * 渲染加载动画
     *
     * @return string
     */
    protected function renderLoading(): string
    {
        return <<<HTML
<div id="documents-loading" class="text-center p-3">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">加载中...</span>
    </div>
    <p class="mt-2">正在加载文档列表...</p>
</div>
HTML;
    }

    /**
     * 渲染JS脚本
     *
     * @return string
     */
    protected function renderScript(): string
    {
        return <<<EOT
<style>
.table-container {
    max-height: 400px;
    overflow: auto;
    margin-bottom: 0;
    position: relative;
}
.table-documents {
    width: auto;
    min-width: 100%;
}
.table-documents th {
    position: sticky;
    top: 0;
    background: #f8f9fa;
    z-index: 10;
    font-weight: bold;
}
.table-documents th, .table-documents td {
    padding: 8px !important;
    font-size: 12px;
    white-space: nowrap;
}
.content-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    max-width: 300px;
}
</style>
<script>
Dcat.ready(function() {
    // 隐藏加载动画
    $('#documents-loading').hide();
    $('#documents-table-wrapper').show();
});
</script>
EOT;
    }
}
