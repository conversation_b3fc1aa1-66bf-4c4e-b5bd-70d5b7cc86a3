<?php

namespace App\Jobs\User;

use App\Jobs\BaseJob;
use App\Models\UserIdentityScore;

class SendVipPageJob extends BaseJob
{
    public function __construct(protected int $page)
    {
    }

    public function handle(): void
    {
        $logs = UserIdentityScore::query()->where('status', 0)
            ->where('send_at', '<=', now())
            ->orderBy('id')
            ->paginate(1000, ['*'], 'page', $this->page);
        foreach ($logs as $log) {
            SendVipScoreJob::dispatch($log);
        }
        if ($logs->hasMorePages()) {
            SendVipPageJob::dispatch($this->page + 1);
        }
    }

}