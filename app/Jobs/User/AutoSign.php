<?php

namespace App\Jobs\User;

use App\Jobs\BaseJob;
use App\Models\User;
use Modules\User\Models\UserSignLog;
use Modules\User\Models\UserSignRule;

class AutoSign extends BaseJob
{
    public string $queue = 'AutoSign';

    public function __construct(protected User $user)
    {
    }

    public function handle(): void
    {
        if (UserSignLog::query()->ofUser($this->user)->ofDay(now())
            ->doesntExist()) {
            $last          = $this->user->signs()->ofDay(now()->subDay())->value('continuous_day') ?: 0;
            $continuousDay = $last + 1;

            $rules = UserSignRule::ofEnabled()
                ->whereRaw("MOD({$continuousDay}, day) = 0")
                ->get();
            $score = $rules->sum('score');
            if ($score > 0) {
                $this->user->account->exec('activity_score', $score, now()->endOfDay()->toDateTimeString(), [
                    'type'   => 'sign',
                    'remark' => '签到积分',
                    'date'   => now()->toDateString(),
                ]);
            }
            $log = $this->user->signs()->create([
                'sign_at'        => now(),
                'continuous_day' => 1,
                'message'        => '积分×'.((int) $score),
            ]);
            UserSignLog::addLogs($log, $rules, $this->user);
        }
    }
}