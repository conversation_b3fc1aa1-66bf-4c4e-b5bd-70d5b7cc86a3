<?php

namespace App\Jobs\BaiLian;

use App\Jobs\BaseJob;
use App\Models\Model;
use Exception;

class JoinKnowledgeJob extends BaseJob
{
    public string $queue   = 'BaiLian';
    public int    $timeout = 120;
    public int    $tries   = 3;   // 尝试次数
    public int    $backoff = 5;   // 失败后等待时间（秒）

    /**
     * 创建一个新的任务实例
     *
     * @param  \App\Models\Model  $item
     * @param  int  $knowledgeId
     */
    public function __construct(
        protected Model $item,
        protected int $knowledgeId,
        protected int|null $knowledgeItemId = null
    ) {
    }

    /**
     * 执行任务
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            $this->item->addToKnowledgeBase($this->knowledgeId, null, [
                'parent_id' => $this->knowledgeItemId,
            ]);
        } catch (Exception $e) {
            // 重新抛出异常，触发重试机制
            throw $e;
        }
    }

}