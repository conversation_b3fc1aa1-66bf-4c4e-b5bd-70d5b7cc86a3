<?php

namespace App\Jobs\BaiLian;

use App\Jobs\BaseJob;
use App\Models\BailianArticle;
use App\Services\KnowledgeItemStrategies\KnowledgeItemStrategyFactory;
use Exception;
use Illuminate\Support\Facades\Log;

class ArticleUpdateJob extends BaseJob
{
    public string $queue   = 'BaiLian';
    public int    $timeout = 120;
    public int    $tries   = 3; // 尝试次数
    public int    $backoff = 5; // 失败后等待时间（秒）

    /**
     * 创建一个新的任务实例
     *
     * @param  \App\Models\BailianArticle  $article  文章模型
     */
    public function __construct(protected BailianArticle $article)
    {
    }

    /**
     * 执行任务
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            $article = $this->article;

            // 检查文章是否在知识库中
            if (! $article->isInKnowledge()) {
                Log::info('文章不在知识库中，跳过更新', [
                    'article_id' => $article->id
                ]);
                return;
            }

            // 在handle方法中创建策略对象，避免序列化问题
            $strategy = KnowledgeItemStrategyFactory::createForModel($article);

            // 使用优化的异步同步流程
            $strategy->handleContentUpdate($article);
        } catch (Exception $e) {
            Log::error('文章更新任务失败', [
                'article_id' => $this->article->id,
                'error'      => $e->getMessage()
            ]);
            // 重新抛出异常，触发重试机制
            throw $e;
        }
    }

    /**
     * 处理任务失败
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function failed(Exception $exception): void
    {
        Log::error('更新文章任务最终失败', [
            'article_id' => $this->article->id,
            'error'      => $exception->getMessage()
        ]);
    }
}