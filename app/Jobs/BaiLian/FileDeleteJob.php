<?php

namespace App\Jobs\BaiLian;

use App\Jobs\BaseJob;
use App\Models\BailianFile;
use Exception;
use Illuminate\Support\Facades\Log;

class FileDeleteJob extends BaseJob
{
    public string $queue   = 'BaiLian';
    public int    $timeout = 120;
    public int    $tries   = 3; // 尝试次数
    public int    $backoff = 5; // 失败后等待时间（秒）

    /**
     * 创建一个新的任务实例
     *
     * @param  \App\Models\BailianFile  $file
     */
    public function __construct(protected BailianFile $file, protected $self_destruct = true)
    {
    }

    /**
     * 执行任务
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            $file = $this->file;
            $file->removeFromKnowledge($this->self_destruct);
        } catch (Exception $e) {
            // 重新抛出异常，触发重试机制
            throw $e;
        }
    }

    /**
     * 处理任务失败
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function failed(Exception $exception): void
    {
        Log::error('删除文档任务最终失败', [
            'file_id' => $this->file->id,
            'error'   => $exception->getMessage()
        ]);
    }
}