<?php

namespace App\Jobs\BaiLian;

use App\Jobs\BaseJob;
use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeItem;
use App\Services\KnowledgeItemStrategies\KnowledgeItemStrategyFactory;
use Exception;
use Log;

class SyncKnowledgeItemToAlibabaJob extends BaseJob
{
    public string $queue   = 'BaiLian';
    public int    $timeout = 300;   // 5分钟超时
    public int    $tries   = 3;     // 尝试次数
    public int    $backoff = 10;    // 失败后等待时间（秒）

    /**
     * 创建一个新的任务实例
     *
     * @param  BailianKnowledgeItem  $knowledgeItem
     */
    public function __construct(protected BailianKnowledgeItem $knowledgeItem)
    {
    }

    /**
     * 执行任务
     *
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        try {
            // 刷新模型状态，确保获取最新数据
            $this->knowledgeItem->refresh();

            // 检查知识库项目是否存在
            if (! $this->knowledgeItem->exists) {
                return;
            }

            // 检查是否需要同步
            if (! $this->knowledgeItem->needsSync()) {
                return;
            }

            // 标记为同步中
            $this->knowledgeItem->markAsSyncing();

            // 获取关联的实际项目（文章、文件等）
            $item = $this->knowledgeItem->itemable;
            if (! $item) {
                throw new Exception('关联的项目不存在');
            }

            // 获取知识库
            $knowledge = $this->knowledgeItem->baiLianKnowledge;
            if (! $knowledge) {
                throw new Exception('知识库不存在');
            }

            // 获取分类ID
            $categoryId = $this->resolveCategoryId($item, $knowledge);

            // 获取策略并执行同步到阿里云的操作
            $strategy = KnowledgeItemStrategyFactory::createForModel($item);
            $strategy->syncToAlibaba($item, $knowledge, $categoryId);

            // 标记为同步成功
            $this->knowledgeItem->markAsSynced();

            // 触发知识库内容更新事件
            if (in_array($item->getMorphClass(), ['bailian_article', 'bailian_file'])) {
                $item->fireKnowledgeContentUpdatedEvent($knowledge, 'created');
            }
        } catch (Exception $e) {
            // 检查是否是最后一次尝试
            $isLastAttempt = $this->attempts() >= $this->tries;

            if ($isLastAttempt) {
                Log::error('知识库项目同步最终失败', [
                    'knowledge_item_id' => $this->knowledgeItem->id,
                    'attempts'          => $this->attempts(),
                    'max_tries'         => $this->tries,
                    'error'             => $e->getMessage(),
                    'trace'             => $e->getTraceAsString()
                ]);
            } else {
                // 还有重试机会，记录警告日志
                Log::warning('知识库项目同步失败，将重试', [
                    'knowledge_item_id' => $this->knowledgeItem->id,
                    'attempt'           => $this->attempts(),
                    'max_tries'         => $this->tries,
                    'error'             => $e->getMessage()
                ]);
            }
            // 最后一次尝试失败，标记为同步失败
            $this->knowledgeItem->markAsSyncFailed($e->getMessage());
            // 重新抛出异常，触发重试机制
            throw $e;
        }
    }

    /**
     * 解析分类ID
     */
    protected function resolveCategoryId($item, BailianKnowledge $knowledge): string
    {
        // 如果项目有分类，使用项目的分类
        if (method_exists($item, 'category') && $item->category) {
            return $item->category->bailian_id;
        }

        // 否则使用知识库的默认分类
        $defaultCategory = $knowledge->categories()->first();
        if (! $defaultCategory) {
            throw new Exception('知识库没有可用的分类');
        }

        return $defaultCategory->bailian_id;
    }

    /**
     * 任务失败时的处理
     */
    public function failed(Exception $exception): void
    {
        Log::error('知识库项目同步任务最终失败', [
            'knowledge_item_id' => $this->knowledgeItem->id,
            'error'             => $exception->getMessage(),
            'attempts'          => $this->attempts()
        ]);

        // 确保状态被标记为失败
        if ($this->knowledgeItem) {
            $this->knowledgeItem->markAsSyncFailed($exception->getMessage());
        }
    }
}
