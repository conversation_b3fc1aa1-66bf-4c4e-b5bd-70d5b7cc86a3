<?php

namespace App\Jobs\BaiLian;

use App\Jobs\BaseJob;
use App\Models\BailianFile;
use Illuminate\Support\Facades\Log;
use Throwable;

class FileDeleteByIdJob extends BaseJob
{
    public string $queue   = 'BaiLian';
    public int    $timeout = 120;
    public int    $tries   = 3;   // 尝试次数
    public int    $backoff = 5;   // 失败后等待时间（秒）

    protected int  $fileId;
    protected int  $storageId;
    protected bool $self_destruct = false;

    /**
     * 创建一个新的任务实例
     *
     * @param  int  $fileId
     * @param  int  $storageId
     * @param  bool  $self_destruct
     */
    public function __construct(int $fileId, int $storageId, bool $self_destruct = true)
    {
        $this->fileId        = $fileId;
        $this->storageId     = $storageId;
        $this->self_destruct = $self_destruct;
        Log::info('FileDeleteByIdJob created', [
            'file_id'       => $this->fileId,
            'storage_id'    => $this->storageId,
            'self_destruct' => $this->self_destruct,
        ]);
    }

    /**
     * 执行任务
     *
     * @return void
     * @throws \Throwable // 改变这里，如果 handle 内部也可能抛出 Error
     */
    public function handle(): void
    {
        Log::info('FileDeleteByIdJob handle', [
            'file_id'       => $this->fileId,
            'storage_id'    => $this->storageId,
            'self_destruct' => $this->self_destruct,
        ]);
        try {
            $file = new BailianFile();
            $file->removeDocumentFromKnowledgeById($this->fileId, $this->storageId, $this->self_destruct);
        } catch (Throwable $e) { // 捕获 Throwable，包括 Error 和 Exception
            // 记录原始错误信息，以帮助调试
            Log::error('FileDeleteByIdJob handle error', [
                'file_id'    => $this->fileId,
                'storage_id' => $this->storageId,
                'error'      => $e->getMessage(),
                'trace'      => $e->getTraceAsString(), // 打印堆栈跟踪
                'type'       => get_class($e), // 打印错误类型
            ]);
            // 重新抛出异常，触发重试机制或failed方法
            throw $e;
        }
    }

    /**
     * 处理任务失败
     *
     * @param  \Throwable  $exception  // 必须是 Throwable
     * @return void
     */
    public function failed(Throwable $exception): void
    {
        Log::error('删除文档任务最终失败', [
            'file_id'    => $this->fileId,
            'storage_id' => $this->storageId,
            'error'      => $exception->getMessage(),
            'type'       => get_class($exception) // 打印错误类型
        ]);
    }
}
