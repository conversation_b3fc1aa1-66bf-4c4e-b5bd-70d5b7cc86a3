<?php

namespace App\Jobs\Vidu;

use App\Jobs\BaseJob;
use App\Models\PluginViduDraw;
use Exception;
use Illuminate\Support\Facades\Log;

class ViduQueryJob extends BaseJob
{
    public string $queue   = 'Asset';
    public int    $timeout = 120;

    public function __construct(protected PluginViduDraw $task)
    {
    }

    public function handle(): void
    {
        try {
            $this->task->videoQuery();
        } catch (Exception $e) {
            Log::error('Vidu视频生成任务查询失败', [
                'error'   => $e->getMessage(),
                'trace'   => $e->getTraceAsString(),
                'task_id' => $this->task->task_id,
            ]);
        }
    }
} 