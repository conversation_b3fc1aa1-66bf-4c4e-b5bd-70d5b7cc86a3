<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class ChatGroup extends Model
{
    use SoftDeletes, BelongsToUser, HasCovers;

    const BgColor = [
        '#c4f2cb',
        '#fcddbe',
        '#cbe3fd',
        '#fbecb1',
    ];

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub
        self::creating(function (Model $model) {
            $model->bgcolor = Arr::random(ChatGroup::BgColor);
        });
        self::forceDeleted(function ($model) {
            ChatLog::where('group_id', $model->id)->forceDelete();
        });
        self::saving(function ($model) {
            $model->name        = Str::limit($model->name, 50, '');
            $model->description = Str::limit($model->description, 200, '');
        });
    }

    public function config(): BelongsTo
    {
        return $this->belongsTo(AiChatConfig::class, 'channel', 'key');
    }

    public function hasImage()
    {
        return $this->logs()
            ->where(function (Builder $builder) {
                $builder->whereRaw('JSON_LENGTH(images) > 0')
                    ->orWhereNotNull('asset_no');
            })
            ->exists();
    }

    public function logs(): HasMany
    {
        return $this->hasMany(ChatLog::class, 'group_id', 'id');
    }

    public function getLogoAttribute()
    {
        return $this->parseImageUrl($this->config?->coverUrl);
    }
}
