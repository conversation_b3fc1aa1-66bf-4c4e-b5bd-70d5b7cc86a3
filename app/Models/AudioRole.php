<?php

namespace App\Models;

use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Spatie\EloquentSortable\Sortable;
use <PERSON>tie\EloquentSortable\SortableTrait;

class AudioRole extends Model implements Sortable
{
    use HasEasyStatus, SortableTrait, Cachable, HasCovers;

    protected array $sortable = [
        'order_column_name'  => 'order',
        'sort_when_creating' => true,
    ];

    protected static function boot()
    {
        parent::boot(); //
        self::saving(function ($model) {
            if ($model->is_default) {
                AudioRole::where('id', '!=', $model->id)
                    ->where('is_default', 1)
                    ->update(['is_default' => 0]);
            }
        });
    }

}
