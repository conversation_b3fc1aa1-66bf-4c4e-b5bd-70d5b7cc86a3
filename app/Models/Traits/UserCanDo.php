<?php

namespace App\Models\Traits;

use App\Exceptions\ValidatorException;
use App\Models\Enums\FastgptAppLevelEnum;
use App\Models\Enums\FastgptKnowledgeLevelEnum;
use App\Models\FastgptApp;
use App\Models\FastgptKnowledge;
use Modules\Company\Models\Company;
use Modules\Company\Models\Role;

trait UserCanDo
{

    /**
     * Notes: 只有超管和子管理员才能分配角色
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 10:53
     * @return bool
     */
    public function canAssignRoles(): bool
    {
        return $this->isManagementRole();
    }

    /**
     * Notes: 是否可以管理知识库
     *  超管 子管理 知识库管理 部门主管
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 15:08
     * @param  int  $company_id
     * @param  int|array  $department_id
     * @return bool
     */
    public function canManageKnowledge(int $company_id, int|array $department_id): bool
    {
        return $this->isManagementRole($company_id) ||
            $this->companyDepartmentRoles()
                ->whereIn('department_id', $this->departmentIdToArray($department_id))
                ->whereHas('role', fn($q) => $q->whereIn('label', Role::KNOWLEDGE_MANAGEMENT_ROLES))
                ->exists();
    }

    /**
     * Notes: 是否可以添加FastGpt支付库
     *
     * @Author: 玄尘
     * @Date: 2024/12/27 13:45
     */
    public function canCreateKnowledge(string $level, ?int $department_id = null, ?int $company_id = null): bool
    {
        $company = $this->getCompany($company_id);

        return match ($level) {
            FastgptKnowledgeLevelEnum::ALL->value, FastgptKnowledgeLevelEnum::COMPANY->value => $this->isManagementRole($company->id),
            FastgptKnowledgeLevelEnum::DEPARTMENT->value => $this->canManageKnowledge($company->id, $department_id),
            FastgptKnowledgeLevelEnum::USER->value => $this->checkUserHasPrivateKnowledge(),
            default => false,
        };
    }

    /**
     * Notes: 是否可以创建app
     *
     * @Author: 玄尘
     * @Date: 2024/12/20 17:18
     * @param $level
     * @param  int|null  $department_id
     * @param  int|null  $company_id
     * @return bool
     * @throws \App\Exceptions\ValidatorException
     */
    public function canCreateApp($level, ?int $department_id = null, ?int $company_id = null): bool
    {
        $company = $this->getCompany($company_id);

        return match ($level) {
            FastgptAppLevelEnum::ALL->value, FastgptAppLevelEnum::COMPANY->value => $this->isManagementRole($company->id),
            FastgptAppLevelEnum::DEPARTMENT->value => $this->canManageApp($company->id, $department_id),
            FastgptAppLevelEnum::USER->value => $this->checkUserHasPrivateApp(),
            default => false,
        };
    }

    /**
     * Notes: 是否可以分配职位
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 10:28
     * @param $company_id
     * @return bool
     */
    public function canManageRole($company_id): bool
    {
        return $this->isManagementRole($company_id);
    }

    /**
     * Notes: 是否可以管理部门
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 10:29
     * @param $company_id
     * @return bool
     */
    public function canManageDepartment($company_id): bool
    {
        return $this->isManagementRole($company_id);
    }

    /**
     * Notes: 是否可用管理应用
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 13:50
     * @param $company_id
     * @param  int|array  $department_id
     * @return bool
     */
    public function canManageApp($company_id, int|array $department_id): bool
    {
        return $this->isManagementRole($company_id) ||
            $this->companyDepartmentRoles()
                ->whereIn('department_id', $this->departmentIdToArray($department_id))
                ->whereHas('role', fn($q) => $q->whereIn('label', Role::KNOWLEDGE_MANAGEMENT_ROLES))
                ->exists();
    }

    /**
     * Notes: 获取权限
     *
     * @Author: 玄尘
     * @Date: 2024/12/20 17:00
     * @param  int  $company_id
     * @param  int|array  $department_ids
     * @return bool
     */
    public function canManageStaff(int $company_id, int|array $department_ids): bool
    {
        if ($this->isManagementRole($company_id)) {
            return true;
        }

        if (blank($department_ids)) {
            return false;
        }

        return $this->isManager($department_ids);
    }

    /**
     * Notes: 是否是超级管理员
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 10:57
     * @param  int|null  $company_id
     * @return bool
     */
    public function isAdministrator(?int $company_id = null): bool
    {
        return $this->companyDepartmentRoles()
            ->when($company_id, fn($q) => $q->where('company_id', $company_id))
            ->whereHas('role', fn($q) => $q->where('label', Role::LABEL_ADMINISTRATOR))
            ->exists();
    }

    /**
     * Notes: 是否是子管理员
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 10:57
     * @param  int|null  $company_id
     * @return bool
     */
    public function isSubAdministrator(?int $company_id = null): bool
    {
        return $this->companyDepartmentRoles()
            ->when($company_id, fn($q) => $q->where('company_id', $company_id))
            ->whereHas('role', fn($q) => $q->where('label', Role::LABEL_SUB_ADMINISTRATOR))
            ->exists();
    }

    /**
     * Notes: 是否是管理员--超管和子管
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 10:58
     * @param  int|null  $company_id
     * @return bool
     */
    public function isManagementRole(?int $company_id = null): bool
    {
        return $this->companyDepartmentRoles()
            ->when($company_id, fn($q) => $q->where('company_id', $company_id))
            ->whereHas('role', fn($q) => $q->whereIn('label', Role::MANAGEMENT_ROLES))
            ->exists();
    }

    /**
     * Notes: 是否是知识库管理员
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 11:00
     * @param  int|null  $department_id
     * @return bool
     */
    public function isKnowledge(?int $department_id = null): bool
    {
        return $this->companyDepartmentRoles()
            ->when($department_id, function ($query) use ($department_id) {
                $query->whereIn('department_id', $this->departmentIdToArray($department_id));
            })
            ->whereHas('role', fn($q) => $q->where('label', Role::LABEL_KNOWLEDGE))
            ->exists();
    }

    /**
     * Notes: 是否是部门管理员
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 11:01
     * @param  int|null  $department_id
     * @return bool
     */
    public function isManager(int|null $department_id = null): bool
    {
        if (empty($department_id)) {
            return false;
        }

        return $this->companyDepartmentRoles()
            ->whereIn('department_id', $this->departmentIdToArray($department_id))
            ->whereHas('role', fn($q) => $q->where('label', Role::LABEL_MANAGER))
            ->exists();
    }

    /**
     * Notes: 是否是部门主管和知识库管理员
     *
     * @Author: 玄尘
     * @Date: 2024/12/20 17:07
     * @param  int|null  $department_id
     * @return bool
     */
    public function isManagementOfKnowledge(?int $department_id = null): bool
    {
        return $this->companyDepartmentRoles()
            ->when($department_id, function ($q) use ($department_id) {
                $q->whereIn('department_id', $this->departmentIdToArray($department_id));
            })
            ->whereHas('role', fn($q) => $q->whereIn('label', Role::KNOWLEDGE_MANAGEMENT_ROLES))
            ->exists();
    }

    /**
     * Notes: 是否可用管理部门应用
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 13:51
     * @param  int|null  $department_id
     * @return bool
     */
    public function isManagementOfApp(?int $department_id = null): bool
    {
        return $this->companyDepartmentRoles()
            ->whereIn('department_id', $this->departmentIdToArray($department_id))
            ->whereHas('role', fn($q) => $q->whereIn('label', Role::KNOWLEDGE_MANAGEMENT_ROLES))
            ->exists();
    }

    /**
     * Notes: 组织id转化成数组
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 14:25
     * @param  int|array  $department_id
     * @return array
     */
    public function departmentIdToArray(int|array $department_id): array
    {
        return is_integer($department_id) ? [$department_id] : $department_id;
    }

    /**
     * Notes: 获取公司
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 14:23
     * @param  int|null  $company_id
     * @return \Modules\Company\Models\Company
     * @throws \App\Exceptions\ValidatorException
     */
    public function getCompany(?int $company_id = null): Company
    {
        if ($company_id) {
            $company = Company::find($company_id);
        } else {
            $company = $this->company;
        }

        if (! $company) {
            throw new ValidatorException('您没有所属公司');
        }

        if (! $this->isMyCompany($company->id)) {
            throw new ValidatorException('您不在此公司');
        }

        return $company;
    }

    /**
     * Notes: 检查是否已有一个私人知识库
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 16:57
     * @return bool
     * @throws \App\Exceptions\ValidatorException
     */
    public function checkUserHasPrivateKnowledge(): bool
    {
        $exists = FastgptKnowledge::where('user_id', $this->id)
            ->where('level', FastgptKnowledgeLevelEnum::USER->value)
            ->exists();
        if ($exists) {
            throw new ValidatorException('每个人只能添加一个私人知识库');
        }

        return true;
    }

    /**
     * Notes: 检查是否有私人应用app
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 16:57
     * @return bool
     * @throws \App\Exceptions\ValidatorException
     */
    public function checkUserHasPrivateApp(): bool
    {
        $exists = FastgptApp::where('user_id', $this->id)
            ->where('level', FastgptAppLevelEnum::USER->value)
            ->exists();
        if ($exists) {
            throw new ValidatorException('每个人只能添加一个私人智能体');
        }
        return true;
    }
}