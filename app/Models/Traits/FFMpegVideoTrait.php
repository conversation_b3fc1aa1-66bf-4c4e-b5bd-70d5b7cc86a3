<?php

namespace App\Models\Traits;

use App\Jobs\FFMpge\FFMpegDoJob;
use Exception;
use FFMpeg\Coordinate\TimeCode;
use FFMpeg\FFMpeg;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

trait FFMpegVideoTrait
{
    public function refVideoRadio()
    {
        $ext = $this->ext ?? [];

        if (blank($ext['radio'] ?? '')) {
            if (! blank($ext['width'] ?? '') && ! blank($ext['height'] ?? '')) {
                $width        = $this->ext['width'];
                $height       = $this->ext['height'];
                $ext['radio'] = $this->getFFRadio((int) $width, (int) $height);
                $this->ext    = $ext;
                $this->save();
            } else {
                $videoStream     = $this->getFFExt($this->videoUrlAttr);
                $width           = $videoStream->get('width');
                $height          = $videoStream->get('height');
                $duration        = $videoStream->get('duration');
                $ext['radio']    = $this->getFFRadio((int) $width, (int) $height);
                $ext['width']    = $width;
                $ext['height']   = $height;
                $ext['duration'] = $duration;
                $ext['ext']      = 'mp4';
                $this->ext       = $ext;
                $this->save();
            }
        }
    }

    protected function getFFRadio(int $width, int $height)
    {
        $commonRatios = [
            '16:9' => 16 / 9,    // 1.777...
            '4:3'  => 4 / 3,      // 1.333...
            '1:1'  => 1 / 1,      // 1.0
            '5:4'  => 5 / 4,      // 1.25
            '3:2'  => 3 / 2,      // 1.5
            '21:9' => 21 / 9,    // 2.333...
            '9:16' => 9 / 16,    // 0.5625（竖屏）
        ];
        $currentRatio = $width / $height;
        $closestRatio = null;
        $minDiff      = PHP_FLOAT_MAX;
        foreach ($commonRatios as $ratioName => $ratioValue) {
            $diff = abs($currentRatio - $ratioValue);
            if ($diff < $minDiff) {
                $minDiff      = $diff;
                $closestRatio = $ratioName;
            }
        }
        return $closestRatio;
    }

    public function getFFExt(string $url, string $fileName)
    {
        $ext = Str::before(pathinfo($url, PATHINFO_EXTENSION), '?');
        if (Str::startsWith($url, 'http')) {
            $client   = new Client();
            $response = $client->request('GET', $url, [
                'verify' => false,
            ]);
            if ($response->getStatusCode() == 200) {
                $content = $response->getBody()->getContents();
                return $this->getFFStreamExt($content, $fileName, $ext);
            } else {
                return [];
            }
        } else {
            return $this->getFFStreamExt(Storage::get($url), $fileName, $ext);
        }
    }

    public function getFFStreamExt(string $content, string $fileName, string $fileExt)
    {
        $tempPath = $fileName.'.'.$fileExt;
        if (! Storage::disk('public')->exists($tempPath)) {
            Storage::disk('public')->put($tempPath, $content);
        }
        $tempPath = Storage::disk('public')->path($tempPath);

        $ffmpeg = FFMpeg::create([
            'ffmpeg.binaries'  => '/usr/bin/ffmpeg',
            'ffprobe.binaries' => '/usr/bin/ffprobe',
        ]);
        $stream = $ffmpeg->getFFProbe()->streams($tempPath)->videos()->first();
        unlink($tempPath);
        return $stream;
    }

    public function genFFUrl(string $url, string $path, string $fileName): array
    {
        $ext = Str::afterLast(Str::contains($url, '?') ? Str::before($url, '?') : $url, '.');
        if (Str::startsWith($url, 'http')) {
            $client   = new Client();
            $response = $client->request('GET', $url, [
                'verify' => false,
            ]);
            if ($response->getStatusCode() == 200) {
                $content = $response->getBody()->getContents();
                $data    = $this->genFFStream($content, $path, $fileName, $ext);
            } else {
                return [];
            }
        } else {
            $data = $this->genFFStream(Storage::get($url), $path, $fileName, $ext);
        }
        $data[0] = $url;
        FFMpegDoJob::dispatch($fileName, $path, $ext);
        return $data;
    }

    public function genFFStream(string $content, string $path, string $fileName, string $fileExt): array
    {
        $tempPath  = $fileName.'.'.$fileExt;
        $coverPath = $fileName.'.jpg';
        if (! Storage::disk('public')->exists($tempPath)) {
            Storage::disk('public')->put($tempPath, $content);
        }
        $tempPath  = Storage::disk('public')->path($tempPath);
        $coverPath = Storage::disk('public')->path($coverPath);
        try {
            $ffmpeg      = FFMpeg::create([
                'ffmpeg.binaries'  => '/usr/bin/ffmpeg',
                'ffprobe.binaries' => '/usr/bin/ffprobe',
            ]);
            $video       = $ffmpeg->open($tempPath);
            $videoStream = $ffmpeg->getFFProbe()->streams($tempPath)->videos()->first();
            $width       = $videoStream->get('width');
            $height      = $videoStream->get('height');
            $duration    = $videoStream->get('duration');

            $cover = $video->frame(TimeCode::fromSeconds(0));
            $cover->save($coverPath);
            $outputHDVideo = $path.'/'.$fileName.'.'.$fileExt;
            $outputVideo   = $path.'/'.$fileName.'_s.'.$fileExt;
            $outputImage   = $path.'/'.$fileName.'.jpg';
            Storage::put($outputHDVideo, $content);
            Storage::put($outputImage, fopen($coverPath, 'r'));
            unlink($coverPath);
            return [
                $outputVideo,
                $outputHDVideo,
                $outputImage,
                $width,
                $height,
                $duration,
                'mp4'
            ];
        } catch (Exception $exception) {
            Log::channel('FFMpeg')->info($exception);
            return [];
        }
    }
}