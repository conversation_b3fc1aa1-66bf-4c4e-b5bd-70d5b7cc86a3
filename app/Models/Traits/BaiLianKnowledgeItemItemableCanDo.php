<?php

namespace App\Models\Traits;

trait BaiLianKnowledgeItemItemableCanDo
{

    /**
     * Notes: 获取用户可执行的操作
     *
     * @Author: 玄尘
     * @Date: 2025/5/8 10:17
     * @param $user
     * @return array
     */
    public function getCan($user): array
    {
        return [
            'edit'           => $this->canEdit($user),
            'delete'         => $this->canDelete($user),
            'move_knowledge' => $this->canMoveKnowledge($user)
        ];
    }

    public function canEdit($user): bool
    {
        return $this->isMy($user);
    }

    public function canDelete($user): bool
    {
        return $this->isMy($user) && $this->canDeleteFromKnowledge();
    }

    /**
     * 检查是否可以从知识库角度删除
     *
     * @return bool
     */
    public function canDeleteFromKnowledge(): bool
    {
        // 如果不在知识库中，可以删除
        if (! $this->knowledgeItem) {
            return true;
        }

        // 如果在知识库中，只有已同步的才能删除
        return $this->knowledgeItem->isSynced();
    }

    /**
     * 获取删除被阻止的原因
     *
     * @param $user
     * @return string|null
     */
    public function getDeleteBlockReason($user): ?string
    {
        if (! $this->isMy($user)) {
            return '您没有权限删除此内容';
        }

        if (! $this->canDeleteFromKnowledge()) {
            if ($this->knowledgeItem && $this->knowledgeItem->isSyncing()) {
                return '知识库正在同步中，请稍后再试';
            }
            if ($this->knowledgeItem && $this->knowledgeItem->needsSync()) {
                return '知识库尚未同步完成，请稍后再试';
            }
            if ($this->knowledgeItem && $this->knowledgeItem->isSyncFailed()) {
                return '知识库同步失败，请先重试同步或联系管理员';
            }
        }

        return null;
    }

    public function canMoveKnowledge($user): bool
    {
        return $this->isMy($user) && ! $this->isInKnowledge();
    }

    public function canRemoveKnowledge($user): bool
    {
        return $this->isMy($user) && $this->isInKnowledge();
    }

    public function isMy($user): bool
    {
        return $this->user_id == $user?->id;
    }

}