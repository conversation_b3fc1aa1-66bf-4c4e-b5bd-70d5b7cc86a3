<?php

namespace App\Models\Traits;

trait CanKnowledgeArticle
{

    public function getCan($user): array
    {
        return [
            'edit'             => $this->canEdit($user),
            'delete'           => $this->canDelete($user),
            'move_knowledge'   => $this->canMoveKnowledge($user),
            'change_knowledge' => $this->canChangeKnowledge($user),
            'remove_knowledge' => $this->canRemoveKnowledge($user),
        ];
    }

    public function canMoveKnowledge($user)
    {
        return ! $this->knowledgeSet && $this->isMy($user);
    }

    /**
     * Notes: 是否可以转移知识库
     *
     * @Author: 玄尘
     * @Date: 2025/4/3 09:52
     * @param $user
     */
    public function canChangeKnowledge($user)
    {
        return (bool) $this->knowledgeSet && $this->isMy($user);
    }

    public function canRemoveKnowledge($user)
    {
        return (bool) $this->knowledgeSet && $this->isMy($user);
    }

    public function isMy($user)
    {
        return $this->user_id == $user?->id;
    }

    public function canEdit($user): bool
    {
        return $this->isMy($user);
    }

    public function canDelete($user)
    {
        return $this->isMy($user);
    }

}