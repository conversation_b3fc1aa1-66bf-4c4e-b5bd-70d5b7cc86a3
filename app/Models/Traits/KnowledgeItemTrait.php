<?php

namespace App\Models\Traits;

use App\Models\BailianArticle;
use App\Models\BailianDirectory;
use App\Models\BailianFile;
use App\Services\KnowledgeItemService;
use Exception;

/**
 * 知识库项目相关功能的聚合Trait
 *
 * 此Trait聚合了所有知识库项目相关的功能，包括：
 * - 关联关系管理
 * - 基础操作（添加、移除）
 * - 工具方法
 */
trait KnowledgeItemTrait
{
    use KnowledgeItemRelationTrait;
    use KnowledgeItemOperationTrait;
    use KnowledgeItemUtilityTrait;

    /**
     * 根据文章ID从知识库中移除文章
     *
     * @param int $articleId 文章ID
     * @param int|null $storageId 存储ID
     * @param bool $is_delete_self 是否删除文章本身
     * @return bool
     * @throws Exception
     */
    public function removeArticleFromKnowledgeById(int $articleId, ?int $storageId = null, bool $is_delete_self = false): bool
    {
        $service = app(KnowledgeItemService::class);
        return $service->removeFromKnowledgeById(BailianArticle::class, $articleId, $storageId, $is_delete_self);
    }

    /**
     * 根据文档ID从知识库中移除文档
     *
     * @param int $documentId 文档ID
     * @param int|null $storageId 存储ID
     * @param bool $is_delete_self 是否删除文档本身
     * @return bool
     * @throws Exception
     */
    public function removeDocumentFromKnowledgeById(int $documentId, ?int $storageId = null, bool $is_delete_self = false): bool
    {
        $service = app(KnowledgeItemService::class);
        return $service->removeFromKnowledgeById(BailianFile::class, $documentId, $storageId, $is_delete_self);
    }

    /**
     * 根据目录ID从知识库中移除目录
     *
     * @param int $directoryId 目录ID
     * @param bool $is_delete_self 是否删除目录本身
     * @return bool
     * @throws Exception
     */
    public function removeDirectoryFromKnowledgeById(int $directoryId, bool $is_delete_self = false): bool
    {
        $service = app(KnowledgeItemService::class);
        return $service->removeFromKnowledgeById(BailianDirectory::class, $directoryId, null, $is_delete_self);
    }
}