<?php

namespace App\Models\Traits;

trait CanKnowledge
{

    /**
     * Notes: 获取可操作权限
     *
     * @Author: 玄尘
     * @Date: 2024/12/27 14:21
     * @param $user
     * @return true[]
     */
    public function getCan($user): array
    {
        return [
            'content' => $this->canSetData($user),//维护内容
            'edit'    => $this->canEdit($user),
            'delete'  => $this->canDelete($user),
            'status'  => $this->canStatus($user),
        ];
    }

    public function canEdit($user)
    {
        return $this->isMyKnowledge($user);

//        $collaboratorIds = $this->fastgptCollaborators()
//            ->where('is_manager', 1)
//            ->pluck('id')
//            ->toArray();
//
//        // 将资源的所有者加入有权限的用户列表
//        $allowedUserIds = array_unique(array_merge($collaboratorIds, [$this->user_id]));
//
//        // 判断当前用户是否在有权限的用户列表中
//        return in_array($user->id, $allowedUserIds);
    }

    public function canStatus($user)
    {
        return $this->canEdit($user);
    }

    /**
     * Notes: description
     *
     * @Author: 玄尘
     * @Date: 2024/12/31 09:49
     * @param $user
     * @return true
     */
    public function canView($user): bool
    {
        return true;
    }

    /**
     * Notes: description
     *
     * @Author: 玄尘
     * @Date: 2024/12/31 09:49
     * @param $user
     * @return bool|mixed
     */
    public function canDelete($user)
    {
        return $this->isMyKnowledge($user);
    }

    /**
     * Notes: 是否可以投喂
     *
     * @Author: 玄尘
     * @Date: 2024/12/20 10:52
     */
    public function canSetData($user)
    {
        return $this->isMyKnowledge($user);
    }

    public function isMyKnowledge($user)
    {
        return $this->user->is($user);
    }

}