<?php

namespace App\Models\Traits;

trait CanApp
{

    public function getCan($user): array
    {
        return [
            'edit'   => $this->canEdit($user),
            'delete' => $this->canDelete($user),
            'status' => $this->canStatus($user),
        ];
    }

    public function canEdit($user)
    {
        return $this->user_id == $user->id;
    }

    public function canView($user): bool
    {
        return true;
    }

    public function canDelete($user)
    {
        return $this->user_id == $user->id;
    }

    public function canStatus($user)
    {
        return $this->canEdit($user);
    }
}