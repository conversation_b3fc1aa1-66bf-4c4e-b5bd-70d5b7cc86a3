<?php

namespace App\Models;

use App\Models\Traits\CanKnowledgeArticle;
use App\Models\Traits\InteractionTrait;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\HasOne;

class FastgptKnowledgeArticle extends Model
{
    use BelongsToUser,
        CanKnowledgeArticle,
        InteractionTrait;

    /**
     * Notes: 关联集合
     *
     * @Author: 玄尘
     * @Date: 2025/3/26 16:23
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function knowledgeSet(): HasOne
    {
        return $this->hasOne(FastgptKnowledgeSet::class, 'fastgpt_knowledge_article_id');
    }

    /**
     * Notes: 获取详情，方便后期扩展
     *
     * @Author: 玄尘
     * @Date: 2025/3/26 16:23
     * @return \Illuminate\Database\Eloquent\HigherOrderBuilderProxy|mixed
     */
    public function getContent()
    {
        return $this->content;
    }

}
