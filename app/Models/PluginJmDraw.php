<?php

namespace App\Models;

use App\Events\PluginJmDrawStatusChangedEvent;
use App\Exceptions\ValidatorException;
use App\Jobs\Plugin\JmQueryJob;
use App\Models\Traits\AssetTrait;
use App\Models\Traits\FFMpegVideoTrait;
use App\Packages\AlibabaCloud\Alibaba;
use App\Packages\JiMeng\JiMeng;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Storage;

class PluginJmDraw extends Model
{
    use BelongsToUser, AutoCreateOrderNo, HasCovers, AssetTrait, FFMpegVideoTrait;

    const TYPE_TEXT_VIDEO     = 'plugin_text_video';
    const TYPE_IMAGE_VIDEO    = 'plugin_image_video';
    const TYPE_NORMAL_VIDEO   = 'cogvideox-flash';
    const TYPE_TEXT_SEEDANCE  = 'plugin_text_seedance';
    const TYPE_IMAGE_SEEDANCE = 'plugin_image_seedance';
    const TYPE_SYNC_LIP_IMAGE = 'plugin_sync_lip_image';
    const TYPE_SYNC_LIP_VIDEO = 'plugin_sync_lip_video';
    const TYPE_TEXT_IMAGE     = 'plugin_text_image';
    const TYPE_IMAGE_IMAGE    = 'plugin_image_image';
    const TYPE_TEXT_ZT2I      = 'plugin_text_zt2i';

    const TYPE_NORMAL_IMAGE = 'cogview-3-flash';

    const SCORE_LIP   = [
        'image' => 0.5,
        'video' => 1,
    ];
    const SCORE_IMAGE = [
        'normal' => 0,
        'pro'    => 2,
        'pro-v'  => 2,
    ];
    const SCORE_ViDEO = [
        'normal' => [
            's5'  => [
                1 => 5,//1:1
                2 => 5,//3:4
                3 => 5,//16:9
                4 => 5,//4:3
                5 => 5,//9:16
                6 => 5,//2:3
                7 => 5,//3:2
                //                8 => 5,//21:9
            ],
            's10' => [
                1 => 5,//1:1
                2 => 5,//3:4
                3 => 5,//16:9
                4 => 5,//4:3
                5 => 5,//9:16
                6 => 5,//2:3
                7 => 5,//3:2
                //                8 => 5,//21:9
            ],
        ],
        'pro'    => [
            's5'  => [
                1 => 20,//1:1
                2 => 35,//3:4
                3 => 40,//16:9
                4 => 35,//4:3
                5 => 40,//9:16
                6 => 35,//2:3
                7 => 35,//3:2
                //                8 => 5,//21:9
            ],
            's10' => [
                1 => 45,//1:1
                2 => 60,//3:4
                3 => 80,//16:9
                4 => 60,//4:3
                5 => 80,//9:16
                6 => 60,//2:3
                7 => 60,//3:2
                //                8 => 5,//21:9
            ],
        ],
        'pro-v'  => [
            's5'  => [
                1 => 20,//1:1
                2 => 35,//3:4
                3 => 40,//16:9
                4 => 35,//4:3
                5 => 40,//9:16
                6 => 35,//2:3
                7 => 35,//3:2
                //                8 => 5,//21:9
            ],
            's10' => [
                1 => 45,//1:1
                2 => 60,//3:4
                3 => 80,//16:9
                4 => 60,//4:3
                5 => 80,//9:16
                6 => 60,//2:3
                7 => 60,//3:2
                //                8 => 5,//21:9
            ],
        ]
    ];
    const TYPE_PLUGIN = [
        self::TYPE_TEXT_VIDEO     => '文生视频',
        self::TYPE_IMAGE_VIDEO    => '图生视频',
        self::TYPE_SYNC_LIP_IMAGE => '对口型（图片）',
        self::TYPE_SYNC_LIP_VIDEO => '对口型（视频）',
        self::TYPE_TEXT_IMAGE     => '文生图',
        self::TYPE_NORMAL_IMAGE   => '瓦特1.0生图',
        self::TYPE_NORMAL_VIDEO   => '瓦特1.0视频',
        self::TYPE_IMAGE_IMAGE    => '图生图',
        self::TYPE_TEXT_SEEDANCE  => '文生视频2.5',
        self::TYPE_IMAGE_SEEDANCE => '图生视频2.5',
        self::TYPE_TEXT_ZT2I      => '文生图2.5',
    ];
    const WHERE_TYPE  = [
        'image' => [
            self::TYPE_TEXT_IMAGE,
            self::TYPE_NORMAL_IMAGE,
            self::TYPE_IMAGE_IMAGE,
            self::TYPE_TEXT_ZT2I,
        ],
        'video' => [
            self::TYPE_TEXT_VIDEO,
            self::TYPE_IMAGE_VIDEO,
            self::TYPE_NORMAL_VIDEO,
            self::TYPE_SYNC_LIP_IMAGE,
            self::TYPE_SYNC_LIP_VIDEO,
            self::TYPE_TEXT_SEEDANCE,
            self::TYPE_IMAGE_SEEDANCE,
        ],
    ];
    const ModelImage  = [
        [
            'key'    => 'normal',
            'title'  => '瓦特1.0',
            'remark' => '瓦特绘图1.0版本,快速简单的绘制图片',
            'cans'   => [
                'text'  => true,
                'image' => false,
            ]
        ],
        [
            'key'    => 'pro',
            'title'  => '瓦特2.0',
            'remark' => '瓦特绘图2.0版本,增加很多细节的处理,绘制图片更细腻',
            'cans'   => [
                'text'  => true,
                'image' => true
            ]
        ],
        [
            'key'    => 'pro-v',
            'title'  => '瓦特2.5',
            'remark' => '瓦特绘图2.5版本,细节精确：模型能够根据文本描述生成高度细致和精确的图像。灵活表达：模型支持广泛的创意输入，能够处理复杂和抽象的文本描述，并将其转化为具有创意和吸引力的视觉作品。持续更新：定期对模型进行优化和更新，以包含最新的AI研究成果和技术进步，确保客户始终使用行业前沿的技术。',
            'cans'   => [
                'text'  => true,
                'image' => false
            ]
        ]
    ];
    const ModelVideo  = [
        [
            'key'    => 'normal',
            'title'  => '瓦特1.0',
            'remark' => '瓦特视频1.0版本,快速简单的制作你想要的视频',
            'cans'   => [
                'text'  => true,
                'image' => true,
            ]
        ],
        [
            'key'    => 'pro',
            'title'  => '瓦特2.0',
            'remark' => '瓦特视频2.0版本,增加很多细节的处理,制作视频效果更好',
            'cans'   => [
                'text'  => true,
                'image' => true,
            ]
        ],
        [
            'key'    => 'pro-v',
            'title'  => '瓦特2.5',
            'remark' => '瓦特视频2.5版本,具备强大的语义理解与指令遵循能力，可精细控制人物外貌气质、衣着风格、表情动作，在多主体动作解析、嵌入式文本响应、程度副词和镜头切换响应方面，也展现出了绝对优势。运镜能力强，支持环绕、航拍、变焦、平移、跟随、手持等多种镜头语言。生成的视频兼容多种风格，具备细腻高清的基础画质和影视级美感',
            'cans'   => [
                'text'  => true,
                'image' => true,
            ]
        ],
    ];
    const ModelType   = [
        'image' => self::ModelImage,
        'video' => self::ModelVideo,
    ];
    public string $orderNoPrefix = 'PJ';
    protected     $casts         = [
        'inputs'   => 'json',
        'params'   => 'json',
        'res'      => 'json',
        'pictures' => 'json',
        'over_at'  => 'datetime',
        'start_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub
        self::creating(function (PluginJmDraw $model) {
            $score = match ($model->type) {
                self::TYPE_NORMAL_IMAGE => 0,
                self::TYPE_TEXT_IMAGE => 2,
                self::TYPE_IMAGE_IMAGE => 2,
                self::TYPE_TEXT_ZT2I => 2,
                self::TYPE_NORMAL_VIDEO => 5,
                self::TYPE_TEXT_VIDEO, self::TYPE_IMAGE_VIDEO, self::TYPE_TEXT_SEEDANCE, self::TYPE_IMAGE_SEEDANCE => match ((int) $model->params['duration']) {
                    5 => match ($model->params['aspect_ratio'] ?? '1:1') {
                        '1:1' => 20,//1:1
                        '3:4', '4:3', '2:3', '3:2' => 35,//3:4
                        "16:9", "9:16" => 40,//16:9
                    },
                    10 => match ($model->params['aspect_ratio'] ?? '1:1') {
                        '1:1' => 45,//1:1
                        '3:4', '4:3', '2:3', '3:2' => 60,//3:4
                        "16:9", "9:16" => 80,//16:9
                    },
                    default => 0,
                },
                self::TYPE_SYNC_LIP_IMAGE => bcmul($model->params['duration'], self::SCORE_LIP['image'], 1),
                self::TYPE_SYNC_LIP_VIDEO => bcmul($model->params['duration'], self::SCORE_LIP['video'], 1),
                default => 0,
            };
            $user  = User::find($model->user_id);
            if (! $user) {
                throw new ValidatorException('用户尚未登陆');
            }
            if ($model->score > 0 && $user->account->score < $score) {
                throw new ValidatorException('积分不足');
            }
            $model->score = $score;
        });

        self::updated(function (PluginJmDraw $model) {
            if ($model->wasChanged('status')) {
                event(new PluginJmDrawStatusChangedEvent(
                    $model,
                    $model->getOriginal('status'),
                    $model->status
                ));
            }
        });
    }

    public function getMediaTypeAttribute(): string
    {
        return match ($this->type) {
            self::TYPE_TEXT_VIDEO,
            self::TYPE_IMAGE_VIDEO,
            self::TYPE_SYNC_LIP_IMAGE,
            self::TYPE_SYNC_LIP_VIDEO,
            self::TYPE_NORMAL_VIDEO,
            self::TYPE_IMAGE_SEEDANCE,
            self::TYPE_TEXT_SEEDANCE => 'video',
            default => 'image',
        };
    }

    public function getExtAttribute()
    {
        return $this->res;
    }

    public function setExtAttribute(array $value)
    {
        $this->attributes['res'] = json_encode($value);
    }

    public function getTypeTextAttribute()
    {
        return self::TYPE_PLUGIN[$this->type] ?? '';
    }

    public function coverUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('cover'))
        );
    }

    public function pictureUrlAttr(): Attribute
    {
        return new Attribute(
            get: function () {
                $pictures = $this->getAttribute('pictures');
                if (empty($pictures)) {
                    return [];
                }
                return collect($pictures)->map(function ($picture) {
                    return $this->parseImageUrl($picture);
                })->toArray();
            }
        );
    }

    public function getSize()
    {
        return match ($this->type) {
            self::TYPE_TEXT_VIDEO => $this->params['aspect_ratio'] ?? '',
            self::TYPE_TEXT_IMAGE => $this->params['ratio'] ?? '',
            default => '',
        };
    }

    public function videoUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('video'))
        );
    }

    public function canResetVideo()
    {
        return blank($this->hd_video) && ! blank($this->video) && $this->status == AiUnifyAsset::STATUS_SUCCESS;
    }

    public function resetVideo()
    {
        $orgVideo = $this->video;
        list($video, $hdVideo, $cover, $width, $height, $duration, $ext) = $this->genFFUrl($orgVideo,
            $this->getOssPath(),
            $this->no);
        $this->video    = $video;
        $this->hd_video = $hdVideo;
        $this->cover    = $cover;
        $this->res      = [
            'duration' => (int) $duration,
            'width'    => $width,
            'height'   => $height,
            'ext'      => $ext,
        ];
        $this->save();
        Storage::delete($orgVideo);
    }

    public function getOssPath(): string
    {
        return sprintf("plugin-jm/%s/%s/%s",
            date('Y'),
            date('m'),
            date('d')
        );
    }

    public function cans(): array
    {
        return $this->asset->cans();
    }

    public function canPolling(): bool
    {
        return in_array($this->status, [
            AiUnifyAsset::STATUS_ING,
            AiUnifyAsset::STATUS_INIT,
        ]);
    }

    public function canDelete(): bool
    {
        return in_array($this->status, [
            AiUnifyAsset::STATUS_INIT,
            AiUnifyAsset::STATUS_ERROR,
            AiUnifyAsset::STATUS_SUCCESS,
        ]);
    }

    public function getStatusTextAttribute()
    {
        return AiUnifyAsset::STATUS[$this->status] ?? '';
    }

    public function draw()
    {
        if ($this->canDraw()) {
            if ($this->score > 0) {
                $this->user->aiSpend(
                    ruleName: 'create_work',
                    model: $this,
                    score: -$this->score,
                    source: [
                        'remark' => '创作内容【'.$this->type_text.'】',
                    ],
                );
            }
            $this->setIng();
            try {
                match ($this->type) {
                    self::TYPE_IMAGE_IMAGE, self::TYPE_TEXT_IMAGE, self::TYPE_TEXT_ZT2I => $this->drawImage(),
                    self::TYPE_NORMAL_IMAGE => $this->drawBigModelImage(),
                    self::TYPE_NORMAL_VIDEO => $this->drawBigModelVideo(),
                    self::TYPE_SYNC_LIP_IMAGE => $this->syncLipImage(),
                    self::TYPE_SYNC_LIP_VIDEO => $this->syncLipVideo(),
                    default => $this->drawVideo(),
                };
            } catch (Exception $e) {
                $this->error($e->getMessage());
            }
        } else {
            throw new Exception('当前状态不可执行');
        }
    }

    public function canDraw(): bool
    {
        return $this->status == AiUnifyAsset::STATUS_INIT;
    }

    public function setIng()
    {
        $this->status   = AiUnifyAsset::STATUS_ING;
        $this->start_at = now();
        $this->save();
    }

    public function drawImage()
    {
        $result              = match ($this->type) {
            self::TYPE_TEXT_IMAGE, self::TYPE_TEXT_ZT2I => JiMeng::volcengine()->textImage($this),
            self::TYPE_IMAGE_IMAGE => JiMeng::volcengine()->imageImage($this),
        };
        $this->cover         = $this->saveStorage($result['image_urls'][0], 'jpeg', 1);
        $size                = getimagesize(Storage::url($this->cover)); // 直接传递流资源
        $width               = $size[0] ?? 0;
        $height              = $size[1] ?? 0;
        $radio               = $this->getFFRadio($width, $height);
        $this->llm_result    = $result['llm_result'] ?? '';
        $this->over_at       = now();
        $this->res           = [
            'width'  => $width,
            'height' => $height,
            'radio'  => $radio,
        ];
        $this->status        = AiUnifyAsset::STATUS_SUCCESS;
        $this->error_message = '';
        $this->save();
        return true;
    }

    public function saveStorage(string $url, string $ext, string $no = ''): string
    {
        $client   = new Client();
        $response = $client->request('GET', $url, [
            'verify' => false,
        ]);
        if ($response->getStatusCode() == 200) {
            $content  = $response->getBody()->getContents();
            $filename = $this->no.'.'.$ext;
            $path     = $this->getOssPath();
            if (Storage::put($this->getOssPath().'/'.$filename, $content)) {
                return $path.'/'.$filename;
            } else {
                return '';
            }
        } else {
            return '';
        }
    }

    public function drawBigModelImage()
    {
        $result = JiMeng::bigmodel()->genImage($this);
        if (isset($result['data'][0]['url'])) {
            $ext                 = pathinfo($result['data'][0]['url'], PATHINFO_EXTENSION);
            $cover               = $this->saveStorage($result['data'][0]['url'], $ext, $this->no);
            $size                = getimagesize(Storage::url($cover)); // 直接传递流资源
            $width               = $size[0] ?? 0;
            $height              = $size[1] ?? 0;
            $radio               = $this->getFFRadio($width, $height);
            $this->cover         = $cover;
            $this->pictures      = [$cover];
            $this->over_at       = now();
            $this->res           = [
                'width'  => $width,
                'height' => $height,
                'radio'  => $radio,
            ];
            $this->status        = AiUnifyAsset::STATUS_SUCCESS;
            $this->error_message = '';
            $this->save();
        } else {
            throw new Exception('生成失败');
        }
    }

    public function drawBigModelVideo()
    {
        $result = JiMeng::bigmodel()->genVideo($this);
        if ($result['id'] ?? false) {
            $this->task_id = $result['id'];
            $this->save();
            $this->jobQuery();
        } else {
            throw new Exception('生成失败');
        }
    }

    public function jobQuery()
    {
        JmQueryJob::dispatch($this)->delay(now()->addSeconds(10));
    }

    public function syncLipImage()
    {
        try {
            $data = Alibaba::bailian()->livePortrait()->imageToVideo($this);
            if ($data) {
                $this->status   = AiUnifyAsset::STATUS_ING;
                $this->start_at = now();
                $this->task_id  = $data['output']['task_id'];
                $this->save();
                $this->jobQuery();
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    public function error(string $message)
    {
        $this->error_message = $message;
        $this->status        = AiUnifyAsset::STATUS_ERROR;
        $this->over_at       = now();
        $this->save();
        if ($this->score) {
            $this->user->aiSpend(
                ruleName: 'create_work_error',
                model: $this,
                score: $this->score,
                source: [
                    'remark' => '创作内容失败['.$this->type_text.']',
                ],
            );
        }
    }

    public function syncLipVideo()
    {
        try {
            $data = Alibaba::bailian()->livePortrait()->videoToVideo($this);
            if ($data) {
                $this->status   = AiUnifyAsset::STATUS_ING;
                $this->start_at = now();
                $this->task_id  = $data['output']['task_id'];
                $this->save();
                $this->jobQuery();
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    protected function drawVideo()
    {
        $result = JiMeng::volcengine()->drawVideo($this);
        if ($result['id'] ?? false) {
            $this->task_id = $result['id'];
            $this->save();
            $this->videoQuery();
        } elseif ($result['error'] ?? false) {
            throw new Exception($result['error']['message']);
        } else {
            throw new Exception('位置错误');
        }
    }

    public function videoQuery()
    {
        if ($this->status != AiUnifyAsset::STATUS_ING) {
            throw new Exception('轮训已结束');
        }
        $result = JiMeng::volcengine()->videoQuery($this);
        $status = strtolower($result['status'] ?? '');
        if (in_array($status, [
            'queued',
            'running',
        ])) {
            $this->jobQuery();
            return;
        }
        if ($status == 'cancelled') {
            $this->error('任务已取消');
            return;
        }
        if ($status == 'failed') {
            $this->error($result['error']['message'] ?? '');
            return;
        }
        if ($status == 'succeeded') {
            list($video, $hdVideo, $cover, $width, $height, $duration, $ext) = $this->genFFUrl($result['content']['video_url'],
                $this->getOssPath(),
                $this->no);
            $radio               = $this->getFFRadio($width, $height);
            $this->video         = $video;
            $this->hd_video      = $hdVideo;
            $this->cover         = $cover;
            $this->res           = [
                'duration' => (int) $duration,
                'width'    => $width,
                'height'   => $height,
                'radio'    => $radio,
                'ext'      => $ext,
            ];
            $this->over_at       = now();
            $this->status        = AiUnifyAsset::STATUS_SUCCESS;
            $this->error_message = '';
            $this->save();
            return;
        }
        $this->jobQuery();
    }

    public function saveStorageBase64(string $content, string $ext = '', string $no = '')
    {
        $path      = sprintf("plugin-jm/%s/%s/%s",
            date('Y'),
            date('m'),
            date('d')
        );
        $filename  = $this->no.'_'.$no.'.'.$ext;
        $imageBody = base64_decode($content);
        if (Storage::put($path.'/'.$filename, $imageBody)) {
            return $path.'/'.$filename;
        } else {
            return '';
        }
    }

    public function taskQuery()
    {
        $result = match ($this->type) {
            self::TYPE_TEXT_IMAGE, self::TYPE_IMAGE_IMAGE => JiMeng::task()->queryHistory($this),
            default => JiMeng::task()->query($this),
        };
        if ($result->isSuccess()) {
            $taskRes  = match ($this->type) {
                self::TYPE_TEXT_IMAGE, self::TYPE_IMAGE_IMAGE => $result->toArray()[$this->serial_no],
                default => $result->task_map[$this->task_id],
            };
            $taskData = $taskRes['task'];
            if ($taskData['status'] == 30) {
                $this->error('CODE: '.$taskRes['fail_code']);
                return;
            }
            if ($taskData['status'] == 50) {
                if (in_array($this->type, [self::TYPE_TEXT_IMAGE, self::TYPE_IMAGE_IMAGE])) {
                    $pictures = [];
                    $i        = 1;
                    foreach ($taskRes['item_list'] as $item) {
                        $pictures[] = $this->saveStorage($item['image']['large_images'][0]['image_url'], '', $i);
                        $i++;
                    }
                    $this->cover = $pictures[0] ?? '';
                    $width       = $taskRes['item_list'][0]['image']['large_images'][0]['width'];
                    $height      = $taskRes['item_list'][0]['image']['large_images'][0]['height'];
                    $radio       = $this->getFFRadio($width, $height);

                    $this->pictures = $pictures;
                    $this->res      = [
                        'width'  => $width,
                        'height' => $height,
                        'radio'  => $radio,
                    ];
                } else {
                    $item = $taskRes['item_list'][0];
                    list($video, $hdVideo, $cover, $width, $height, $duration, $ext) = $this->genFFUrl($item['video']['video_model'],
                        $this->getOssPath(),
                        $this->no);
                    $radio          = $this->getFFRadio($width, $height);
                    $this->cover    = $cover;
                    $this->video    = $video;
                    $this->hd_video = $hdVideo;
                    $this->res      = [
                        'duration' => (int) $duration,
                        'width'    => $width,
                        'height'   => $height,
                        'ext'      => $ext,
                        'radio'    => $radio,
                    ];
                }
                $this->status  = AiUnifyAsset::STATUS_SUCCESS;
                $this->over_at = now();
                $this->save();
                $this->jmDelete();
                return;
            }
            $this->jobQuery();
        } else {
            $this->error($result->getMessage());
        }
    }

    public function jmDelete()
    {
        $result = JiMeng::task()->delete($this);
        if ($result->isSuccess()) {
            return true;
        } else {
            return false;
        }
    }

    public function bailianQuery()
    {
        try {
            $result = Alibaba::bailian()->query()->query($this->task_id);
            $output = $result['output'];

            switch (strtolower($output['task_status'])) {
                case 'pending':
                    $this->jobQuery();
                    break;
                case 'running':
                    $this->jobQuery();
                    break;
                case 'succeeded':
                    $video = match ($this->type) {
                        self::TYPE_SYNC_LIP_IMAGE => $output['results']['video_url'],
                        self::TYPE_SYNC_LIP_VIDEO => $output['video_url'],
                        default => '',
                    };
                    if (blank($video)) {
                        throw new Exception('解析视频地址错误');
                    }
                    list($video, $hdVideo, $cover, $width, $height, $duration, $ext) = $this->genFFUrl($video,
                        $this->getOssPath(),
                        $this->no);
                    $radio               = $this->getFFRadio($width, $height);
                    $this->video         = $video;
                    $this->hd_video      = $hdVideo;
                    $this->cover         = $cover;
                    $this->res           = [
                        'duration' => (int) $duration,
                        'width'    => $width,
                        'height'   => $height,
                        'radio'    => $radio,
                        'ext'      => $ext,
                    ];
                    $this->over_at       = now();
                    $this->status        = AiUnifyAsset::STATUS_SUCCESS;
                    $this->error_message = '';
                    $this->save();
                    break;
                case 'failed':
                    $this->error($output['message'] ?? '未知错误');
                    break;
                case 'unknown':
                    $this->error($output['message'] ?? '未知错误');
                    break;
            }
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
        }
    }

    public function videoBigQuery()
    {
        try {
            $result = JiMeng::bigmodel()->videoBigQuery($this);
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
            return;
        }
        $status = $result['task_status'] ?? '';
        if (blank($status)) {
            throw new Exception('没有找到任务结果');
        }
        if (strtoupper($status) == 'PROCESSING') {
            $this->jobQuery();
            return;
        }
        if (strtoupper($status) == 'FAIL') {
            $this->error($result['message'] ?? '未知错误');
            return;
        }
        if (strtoupper($status) == 'SUCCESS') {
            $videoResult = $result['video_result'][0];
            $video       = $videoResult['url'];
            list($video, $hdVideo, $cover, $width, $height, $duration, $ext) = $this->genFFUrl($video,
                $this->getOssPath(),
                $this->no);
            $radio               = $this->getFFRadio($width, $height);
            $this->video         = $video;
            $this->hd_video      = $hdVideo;
            $this->cover         = $cover;
            $this->res           = [
                'duration' => (int) $duration,
                'width'    => $width,
                'height'   => $height,
                'radio'    => $radio,
                'ext'      => $ext,
            ];
            $this->over_at       = now();
            $this->status        = AiUnifyAsset::STATUS_SUCCESS;
            $this->error_message = '';
            $this->save();
            return;
        }
        $this->jobQuery();
    }
}
