<?php

namespace App\Models;

use App\Enums\ConfigType;
use Exception;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Config;

/**
 * @method static Configuration ofModule(string $string)
 */
class Configuration extends Model
{
    use Cachable,
        SoftDeletes;

    protected $casts = [
        'source' => 'json',
        'type'   => ConfigType::class,
        'ext'    => 'json'
    ];

    public function values(): Attribute
    {
        return new Attribute(
            get: fn($value, $attribute) => $this->getValue($value),
            set: fn($value) => $this->setValue($value),
        );
    }

    private function getValue($value): mixed
    {
        return match (true) {
            $this->type == ConfigType::NUMBER => (int) $value,
            $this->type == ConfigType::RATE => (float) $value,
            $this->type == ConfigType::MULTIPLE_SELECT,
                $this->type == ConfigType::CHECKBOX,
                $this->type == ConfigType::ARRAY => json_decode($value, true),
            strtolower($value) == 'false' => false,
            strtolower($value) == 'true' => true,
            default => $value,
        };
    }

    private function setValue($value): string|false
    {
        if (is_array($value)) {
            return json_encode(array_filter($value), JSON_UNESCAPED_UNICODE);
        } else {
            return $value;
        }
    }

    /**
     * Notes   : 注册模块配置
     *
     * @Date   : 2023/2/28 11:12
     * <AUTHOR> <Jason.C>
     * @param  string  $moduleName
     */
    public static function registerModuleConfig(string $moduleName): void
    {
        try {
            Config::set(
                strtolower($moduleName),
                Configuration::ofModule($moduleName)
                    ->get()
                    ->pluck('values', 'key')
                    ->toArray()
            );
        } catch (Exception) {
        }
    }

    /**
     * Notes   : 限定查询作用域
     *
     * @Date   : 2023/2/28 10:44
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $module
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfModule(Builder $query, string $module): Builder
    {
        return $query->where('module', $module);
    }

    public static function getConfigValue(string $key, ?string $module): string|array|bool
    {
        return Configuration::where('key', $key)
            ->when($module, function (Builder $builder, string $module) {
                $builder->where('module', $module);
            })
            ->value('values');
    }

    /**
     * Notes   : 获取配置的source信息
     *
     * @Date   : 2023/3/29 13:25
     * <AUTHOR> <Jason.C>
     * @param  string  $key
     * @param  string|null  $module
     * @return array
     */
    public static function getConfigSource(string $key, ?string $module): array
    {
        return Configuration::where('key', $key)
            ->when($module, function (Builder $builder, string $module) {
                $builder->where('module', $module);
            })
            ->value('source');
    }
}
