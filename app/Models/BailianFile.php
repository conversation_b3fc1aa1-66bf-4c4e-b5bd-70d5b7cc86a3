<?php

namespace App\Models;

use App\Interfaces\BaiLianKnowledgeItemItemableInterface;
use App\Models\Traits\BaiLianKnowledgeItemItemableCanDo;
use App\Models\Traits\KnowledgeItemTrait;
use App\Services\FileTypeService;
use App\Services\KnowledgeItemStrategies\KnowledgeItemStrategyFactory;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Storage\Models\Upload;

class BailianFile extends Model implements BaiLianKnowledgeItemItemableInterface
{
    use BelongsToUser, BaiLianKnowledgeItemItemableCanDo, KnowledgeItemTrait;

    protected $casts = [
        'tags' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        self::deleted(function ($model) {
            // 如果文件在知识库中，删除知识库相关数据
            if ($model->isInKnowledge()) {
                try {
                    $strategy = KnowledgeItemStrategyFactory::createForModel($model);
                    $strategy->removeFromKnowledgeById($model->id, $model->storage_id, false);
                } catch (\Exception $e) {
                    // 记录错误但不阻止删除
                    \Log::error('删除文件时清理知识库数据失败', [
                        'file_id'    => $model->id,
                        'storage_id' => $model->storage_id,
                        'error'      => $e->getMessage()
                    ]);
                }
            }
        });
    }

    /**
     * 获取文件的存储信息
     */
    public function storage(): BelongsTo
    {
        return $this->belongsTo(Upload::class, 'storage_id');
    }

    /**
     * 获取文件的URL
     */
    public function getUrlAttribute()
    {
        return $this->storage ? $this->storage->path_url : null;
    }

    /**
     * 获取文件的路径
     */
    public function getPathAttribute()
    {
        return $this->storage ? $this->storage->path : null;
    }

    /**
     * 获取文件的原始名称
     */
    public function getOriginalNameAttribute()
    {
        return $this->storage ? $this->storage->original : null;
    }

    /**
     * 获取文件的类型
     */
    public function getTypeAttribute()
    {
        return $this->storage ? $this->storage->type : null;
    }

    /**
     * 获取文件的大小（属性访问器）
     */
    public function getSizeAttribute()
    {
        return $this->storage ? $this->storage->size : null;
    }

    /**
     * 获取文件大小（知识库接口方法）
     * 重写 trait 中的方法以确保返回正确的类型
     */
    public function getSize(): int
    {
        if ($this->storage) {
            return (int) $this->storage->size;
        }
        return 0;
    }

    public function getFileTypes(): array
    {
        $type = $path_url = '';
        if ($this->storage) {
            $type     = $this->storage->type;
            $path_url = $this->storage->path_url;
        }
        return app(FileTypeService::class)->getFileTypeInfo($type, $path_url);
    }

    public function scopeOfType($builder, $type, $user)
    {
        return match ($type) {
            // 我的
            'my' => $builder->where('user_id', $user->id),
            // 精选的
            'featured' => $builder->where('is_featured', 1)->where('is_public', 1),
            // 公开的
            'public' => $builder->where('is_public', 1),

            default => $builder,
        };
    }

    /**
     * 获取文件标题
     */
    public function getTitle(): string
    {
        // 优先使用自定义标题
        if ($this->title) {
            return $this->title;
        }

        // 回退到文件原始名称
        if ($this->storage && $this->storage->original) {
            return $this->storage->original;
        }

        // 最后回退到默认值
        return '未命名文件';
    }

    /**
     * 获取文件描述
     */
    public function getDescription(): string
    {
        return $this->description ?? '';
    }
}
