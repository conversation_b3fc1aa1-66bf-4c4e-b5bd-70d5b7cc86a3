<?php

namespace App\Models;

use App\Jobs\Audio\SingerQueryJob;
use App\Packages\Suno\Suno;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AudioSinger extends Model
{
    use BelongsToUser, AutoCreateOrderNo, HasCovers, SoftDeletes;

    const STATUS_INIT    = 0;
    const STATUS_ING     = 1;
    const STATUS_SUCCESS = 2;
    const STATUS_ERROR   = 9;
    const STATUS         = [
        self::STATUS_INIT    => '待处理',
        self::STATUS_ING     => '处理中',
        self::STATUS_SUCCESS => '已完成',
        self::STATUS_ERROR   => '错误',
    ];
    public string $orderNoPrefix = 'AS';
    protected     $casts         = [
        'over_at' => 'datetime',
    ];

    public function cans(): array
    {
        return [
            'polling' => $this->canPolling(),
            'delete'  => $this->canDelete(),
        ];
    }

    public function canPolling(): bool
    {
        return in_array($this->status, [
            self::STATUS_ING,
        ]);
    }

    public function canDelete(): bool
    {
        return in_array($this->status, [
            self::STATUS_INIT,
            self::STATUS_ERROR,
            self::STATUS_SUCCESS,
        ]);
    }

    public function scopeOfIng(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_ING);
    }

    public function scopeOfInit(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_INIT);
    }

    public function getAudioUrlAttribute(): string
    {
        if (blank($this->audio)) {
            return '';
        } else {
            return $this->parseImageUrl($this->audio);
        }
    }

    public function querySuno()
    {
        $result = Suno::tools()->querySinger($this);
        if ($result->isSuccess()) {
            $data = $result->toArray();
            if ($data['status'] == 2) {
                $this->cover   = $this->saveStorage($data['image']);
                $this->audio   = $this->saveStorage($data['audio_preview'], 'audio');
                $this->status  = self::STATUS_SUCCESS;
                $this->over_at = now();
                $this->save();
            } elseif ($data['status'] == 3) {
                $this->error('训练失败');
            } else {
                SingerQueryJob::dispatch($this)->delay(now()->addSeconds(10));
            }
        } else {
            $this->error($result->getMessage());
        }
    }

    public function saveStorage(string $url, string $type = 'image'): string
    {
        $client   = new Client();
        $response = $client->request('GET', $url, [
            'verify' => false,
        ]);
        if ($response->getStatusCode() == 200) {
            $content  = $response->getBody()->getContents();
            $filename = Str::afterLast($url, '/');
            if (Str::contains($filename, '?')) {
                $filename = Str::before($filename, '?');
            }
            $path = sprintf("ai-audio/%s/%s/%s/%s",
                $type,
                date('Y'),
                date('m'),
                date('d')
            );
            if (Storage::put($path.'/'.$filename, $content)) {
                return $path.'/'.$filename;
            } else {
                return '';
            }
        } else {
            return '';
        }
    }

    public function error(string $message = '未知错误'): void
    {
        if (! in_array($this->status, [
            self::STATUS_SUCCESS,
        ])) {
            $this->status        = self::STATUS_ERROR;
            $this->error_message = $message;
            $this->save();

            if ($this->score > 0) {
                $this->user->aiSpend('create_work_error', $this, $this->score, [
                    'remark' => '创作歌手失败【'.$this->prompt.'】',
                ]);
            }
        } else {
            throw new Exception('状态不正确-不可将模型指定为错误状态');
        }
    }

    public function draw()
    {
        if ($this->canDraw()) {
            $result = Suno::tools()->createSinger($this);
            if ($result->isSuccess()) {
                $data = $result->toArray();
                if ($data['status'] == 0) {
                    if ($this->score > 0) {
                        $this->user->aiSpend('create_work', $this, -$this->score, [
                            'remark' => '创作歌手【'.$this->prompt.'】',
                        ]);
                    }
                    $this->job_id = $data['serial_no'];
                    $this->source = $data;
                    $this->save();
                    SingerQueryJob::dispatch($this);
                }
            } else {
                $this->error($result->getMessage());
            }
        }
    }

    public function canDraw(): bool
    {
        return $this->status == self::STATUS_INIT;
    }
}
