<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use App\Traits\HasEasyStatus;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class PublishScrollText extends Model
{
    use BelongsToUser, SoftDeletes, HasEasyStatus;

    public const UPDATED_AT = null;

    public function publish(): BelongsTo
    {
        return $this->belongsTo(AiUnifyAsset::class, 'publish_id', 'id');
    }
}
