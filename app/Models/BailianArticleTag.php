<?php

namespace App\Models;

use App\Packages\BailianAssistant\BLAssistant;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BailianArticleTag extends Model
{

    /**
     * Notes: 关联笔记
     *
     * @Author: 玄尘
     * @Date: 2025/5/29 19:02
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function article(): BelongsTo
    {
        return $this->belongsTo(BailianArticle::class, 'article_id');
    }

    /**
     * Notes: 关联标签
     *
     * @Author: 玄尘
     * @Date: 2025/5/29 19:02
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tag(): BelongsTo
    {
        return $this->belongsTo(BailianTag::class, 'tag_id');
    }

    public static function getTagsByText($text)
    {
        $res = app(BLAssistant::class)->understanding()->generateTagsByText($text);
        if (! $res['status']) {
            throw new Exception($res['message']);
        }

        $output = json_decode($res['data']['output'], true);
        return $output['tags'] ?? '';
    }
}
