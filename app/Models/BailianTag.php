<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class BailianTag extends Model
{
    use BelongsToUser;

    /**
     * Notes: 获取标签关联的笔记
     *
     * @Author: 玄尘
     * @Date: 2025/5/29 19:02
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function articles(): BelongsToMany
    {
        return $this->belongsToMany(BailianArticle::class, 'bailian_article_tags', 'tag_id', 'article_id');
    }
}
