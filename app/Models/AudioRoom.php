<?php

namespace App\Models;

use AlibabaCloud\SDK\ICE\V20201109\ICE;
use AlibabaCloud\SDK\Rtc\V20180111\Models\DeleteChannelRequest;
use AlibabaCloud\SDK\Rtc\V20180111\Rtc;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use App\Packages\Volcengine\RtcBase;
use App\Traits\BelongsToUser;
use Darabonba\OpenApi\Models\Config;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class AudioRoom extends Model
{
    use BelongsToUser;

    const ALIRTC       = 'ali';
    const BIGMODEL     = 'bigmodel';
    const VOLCENGINE   = 'volcengine';
    const STATUS_ING   = 0;
    const STATUS_OVER  = 9;
    const STATUS_CLOSE = 11;
    const STATUS       = [
        self::STATUS_ING   => '进行中',
        self::STATUS_OVER  => '结束',
        self::STATUS_CLOSE => '确认关闭',
    ];
    protected      $casts      = [
        'voice'         => 'json',
        'expiration_at' => 'datetime',
        'ext'           => 'json',
    ];
    private string $volcAppId  = '';
    private string $volcAppKey = '';

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->volcAppId  = env('VOLCENGINE_RTC_APPID');
        $this->volcAppKey = env('VOLCENGINE_RTC_APPKEY');
    }

    public static function getClient()
    {
        $config           = new Config([
            "accessKeyId"     => 'LTAI5tDfxDkXFXqvqn1h1sg9',
            "accessKeySecret" => '******************************'
        ]);
        $config->endpoint = "ice.cn-hangzhou.aliyuncs.com";
        return new ICE($config);
    }

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub
        self::created(function ($model) {
            if ($model->score > 0) {
                $model->user->aiSpend('create_work', $model, -$model->score, [
                    'remark' => '创建实时语音视频通话',
                ]);
            }
            if ($model->expiration_at) {
            }
        });
    }

    public function closeRoom()
    {
        switch ($this->type) {
            case self::BIGMODEL:

                break;
            case self::VOLCENGINE:
                $this->dismissRoom();
                break;
            case self::ALIRTC:
                $client               = AudioRoom::getRtcClient();
                $deleteChannelRequest = new DeleteChannelRequest([
                    "appId"     => $this->ext['appid'],
                    "channelId" => $this->ext['channelid']
                ]);
                $runtime              = new RuntimeOptions([
                    "ignoreSSL" => true
                ]);
                try {
                    $client->deleteChannelWithOptions($deleteChannelRequest,
                        $runtime);
                    $this->status = self::STATUS_OVER;
                    $this->save();
                } catch (TeaError $error) {
                    info($error);
                    $this->status = self::STATUS_OVER;
                    $this->save();
                    throw new Exception($error->message);
                }
                break;
            default:
                throw new Exception('未知的语音通话类型');
                break;
        }
    }

    public function dismissRoom(bool $bootRemove = false)
    {
        $baseClient = $this->getRtcBase();
        if ($bootRemove) {
            try {
                $this->kickUser();
            } catch (Exception $exception) {
            }
        }
        $response = $baseClient->dismissRoom([
            'verify' => false,
            'query'  => [
                'AppId'  => $this->volcAppId,
                'RoomId' => $this->room_id,
            ],
        ]);
        $data     = json_decode($response, true);
        if (isset($data['ResponseMetadata']['Error'])) {
            if ($data['ResponseMetadata']['Error']['Code'] == 'UnknownRoom') {
                $this->status = self::STATUS_CLOSE;
                $this->save();
            }
        } else {
            if ($data['Result']['message'] == 'success') {
                $this->status = self::STATUS_CLOSE;
                $this->save();
            }
        }
    }

    private function getRtcBase(): RtcBase
    {
        $RtcClient = new RtcBase();
        $RtcClient->setAccessKey(env('DOUBAO_APPID', ''));
        $RtcClient->setSecretKey(env('DOUBAO_SECRET', ''));
        return $RtcClient;
    }

    public function kickUser()
    {
        $RtcClient = new RtcBase();
        $RtcClient->setAccessKey(env('DOUBAO_APPID', ''));
        $RtcClient->setSecretKey(env('DOUBAO_SECRET', ''));
        $RtcClient->kickUser([
            'verify' => false,
            'query'  => [
                'Action'  => 'KickUser',
                'Version' => '2020-12-01',
            ],
            'json'   => [
                'AppId'  => $this->ext['app_id'],
                'RoomId' => $this->ext['room_id'],
                'UserId' => $this->ext['user_id'],
            ],
        ]);
    }

    public static function getRtcClient()
    {
        $config           = new Config([
            "accessKeyId"     => 'LTAI5tDfxDkXFXqvqn1h1sg9',
            "accessKeySecret" => '******************************'
        ]);
        $config->endpoint = "rtc.aliyuncs.com";
        return new Rtc($config);
    }

    public function checkOnlineUser()
    {
        $baseClient = $this->getRtcBase();

        $response = $baseClient->getRoomOnlineUsers([
            'verify' => false,
            'query'  => [
                'AppId'  => $this->volcAppId,
                'RoomId' => $this->room_id,
            ],
        ]);
        $response = json_decode($response, true);
        if ($response['ResponseMetadata']['Error'] ?? false) {
        } else {
            $result = $response['Result'];
            if ($result['RoomExists']) {
                $VisibleUserList = $result['VisibleUserList'];
                $hasUser         = Str::contains(implode(',', $VisibleUserList), 'user_'.$this->user_id);
                if (! $hasUser) {
                    $this->dismissRoom();
                }
            } else {
                $this->dismissRoom();
            }
        }
    }

    /**
     * 更新指令
     * type 可选值
     * interrupt：打断智能体。
     * function：传回工具调用信息指令。
     * ExternalTextToSpeech ： 传入文本信息供 TTS 音频播放。
     * ExternalPromptsForLLM：传入自定义文本与用户问题拼接后送入 LLM。
     * ExternalTextToLLM：传入外部问题送入 LLM。
     * FinishSpeechRecognition：触发新一轮对话。
     *
     * @param  string  $message
     * @param  string  $type
     * @param  int  $level
     * @return void
     */
    public function NoticeToRTC(
        string|array $message = '',
        string $type = 'interrupt',
        int $level = 1,
        string $toolId = ''
    ) {
        $baseClient = $this->getRtcBase();
        if ($toolId) {
            $message = json_encode([
                'ToolCallID' => $toolId,
                'Content'    => $message,
            ], JSON_UNESCAPED_UNICODE);
        } elseif (is_array($message)) {
            $message = json_encode($message, JSON_UNESCAPED_UNICODE);
        }
        $params = [
            'AppId'         => $this->ext['app_id'],
            'RoomId'        => $this->ext['room_id'],
            'TaskId'        => $this->ext['task_id'],
            'Command'       => $type,
            "Message"       => $message,
            "InterruptMode" => $level
        ];
        info($params);
        $result = $baseClient->updateVoiceChat([
            'verify' => false,
            'query'  => [
                'Action'  => 'UpdateVoiceChat',
                'Version' => '2024-12-01',
            ],
            'json'   => $params,
        ]);
    }

    public function events(): HasMany
    {
        return $this->hasMany(AudioRoomsCallback::class, 'room_id');
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(AudioRole::class, 'role_id');
    }
}
