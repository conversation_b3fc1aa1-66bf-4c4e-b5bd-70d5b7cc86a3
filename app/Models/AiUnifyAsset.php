<?php

namespace App\Models;

use App\Http\Resources\InteractionResourceTrait;
use App\Models\Traits\InteractionTrait;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class AiUnifyAsset extends Model
{
    use BelongsToUser, AutoCreateOrderNo, InteractionTrait;
    use InteractionResourceTrait;

    const STATUS_INIT    = 0;
    const STATUS_ING     = 1;
    const STATUS_SUCCESS = 2;
    const STATUS_ERROR   = 9;
    const STATUS         = [
        self::STATUS_INIT    => '待处理',
        self::STATUS_ING     => '处理中',
        self::STATUS_SUCCESS => '已完成',
        self::STATUS_ERROR   => '错误',
    ];
    const TYPE_IMAGES    = [
        'mmu_txt2img_model',
        'mmu_img2img_aitryon',
        PluginMjDraw::TYPE_PLUGIN_FACE,
        PluginMjDraw::TYPE_PLUGIN_EXPAND,
        PluginMjDraw::TYPE_PLUGIN_ANIME,
        PluginMjDraw::TYPE_PLUGIN_LINEDRAFT,
        PluginMjDraw::TYPE_PLUGIN_ARTISTICFONT,
        PluginMjDraw::TYPE_PLUGIN_QRCODE,
        PluginMjDraw::TYPE_PLUGIN_BLEND,
        PluginMjDraw::TYPE_PLUGIN_TRANSFER,
        PluginMjDraw::TYPE_PLUGIN_MIRROR,
        PluginMjDraw::TYPE_PRODUCT_GEN,
        PluginMjDraw::TYPE_PRODUCT_CAPTURE,
        PluginMjDraw::TYPE_EDIT_REDRAW,
        PluginMjDraw::TYPE_EDIT_RECANCEL,
        PluginMjDraw::TYPE_EDIT_MAGNIFY,
        PluginMjDraw::TYPE_EDIT_ENHANCE,
        PluginMjDraw::TYPE_EDIT_DEFINITION,
        PluginMjDraw::TYPE_EDIT_REPAIR,
        PluginMjDraw::TYPE_EDIT_COLOUR,
        PluginMjDraw::TYPE_EDIT_IMG_LINE,
        PluginMjDraw::TYPE_EDIT_IMG_SKETCH,
        PluginMjDraw::TYPE_EDIT_ANALYSIS,
        PluginMjDraw::TYPE_EDIT_JIGSAW,
        PluginJmDraw::TYPE_TEXT_IMAGE,
        PluginJmDraw::TYPE_IMAGE_IMAGE,
        PluginJmDraw::TYPE_NORMAL_IMAGE,
    ];
    const TYPE_VIDEO     = [
        'm2v_txt2video_hq',
        'm2v_txt2video',
        'm2v_img2video_hq',
        'm2v_img2video',
        'm2v_img2video_se_hq',
        'm2v_video_lip_sync',
        PluginJmDraw::TYPE_TEXT_VIDEO,
        PluginJmDraw::TYPE_IMAGE_VIDEO,
        PluginJmDraw::TYPE_SYNC_LIP_VIDEO,
        PluginJmDraw::TYPE_SYNC_LIP_IMAGE,
        PluginJmDraw::TYPE_NORMAL_VIDEO,
        PluginViduDraw::TYPE_VIDU,
    ];
    const TYPE_AUDIOS    = [
        'suno',
    ];
    const TYPE_TEXTS     = [
        PluginMjDraw::TYPE_EDIT_ANALYSIS,
    ];
    public string $orderNoPrefix = 'AUA';
    protected     $casts         = [
        'ext' => 'json'
    ];

    protected static function boot()
    {
        parent::boot();
        self::created(function (AiUnifyAsset $model) {
            if ($model->is_asset) {
                $group = BailianAssistant::firstOrCreate([
                    'user_id' => $model->user_id,
                ]);
                $group->logs()->create([
                    'user_id'           => $model->user_id,
                    'prompt'            => $model->description,
                    'ai_unify_asset_id' => $model->id,
                ]);
            }
        });
        self::deleted(function ($model) {
            if ($model->is_asset) {
                $group = BailianAssistant::firstOrCreate([
                    'user_id' => $model->user_id,
                ]);
                $group->logs()->where('user_id', $model->user_id)
                    ->where('ai_unify_asset_id', $model->id)
                    ->delete();
            }
            if ($model->assetable) {
                $model->assetable->delete();
            }
            if ($model->activityMiddle) {
                $model->activityMiddle->delete();
            }
        });
    }

    public function scopeNoShow(Builder $query): void
    {
        $query->whereNotIn('type', [
            PluginMjDraw::TYPE_EDIT_ANALYSIS,
        ]);
    }

    public function assetable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scrollTexts(): HasMany
    {
        return $this->hasMany(PublishScrollText::class, 'publish_id', 'id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(PublishCategory::class, 'category_id');
    }

    public function joinActivity(AiUnifyActivity|int $activity)
    {
        $activity = is_int($activity) ? AiUnifyActivity::find($activity) : $activity;
        if ($this->checkJoinActivity($activity)) {
            AiUnifyActivityItem::create([
                'activity_id' => $activity->id,
                'asset_id'    => $this->id,
            ]);
        }
    }

    public function checkJoinActivity(AiUnifyActivity|int $activity)
    {
        $activity = is_int($activity) ? AiUnifyActivity::find($activity) : $activity;
        if ($this->activity && $this->activity->id != $activity->id) {
            throw new Exception('该作品已经加入了别的活动');
        }
        return true;
    }

    public function exitActivity()
    {
        if ($this->activity && $this->activity->status != AiUnifyActivity::STATUS_FINISH) {
            AiUnifyActivityItem::where([
                'activity_id' => $this->activity->id,
                'asset_id'    => $this->id,
            ])->delete();
        }
    }

    public function cans(): array
    {
        return [
            'polling' => $this->canPolling(),
            'delete'  => $this->canDelete(),
            'publish' => $this->canPublishTo(),
            'down'    => $this->canPublishDown(),
        ];
    }

    public function canPolling(): bool
    {
        return in_array($this->status, [
            self::STATUS_ING,
            self::STATUS_INIT,
        ]);
    }

    public function canDelete(): bool
    {
        return in_array($this->status, [
            self::STATUS_INIT,
            self::STATUS_ERROR,
            self::STATUS_SUCCESS,
        ]);
    }

    public function canPublishTo()
    {
        return $this->platform === 0 && $this->status === self::STATUS_SUCCESS;
    }

    public function canPublishDown()
    {
        return $this->platform === 1 && $this->status === self::STATUS_SUCCESS;
    }

    public function activityMiddle(): HasOne
    {
        return $this->hasOne(AiUnifyActivityItem::class, 'asset_id');
    }

    // 新增活动关联方法
    public function activity(): HasOneThrough
    {
        return $this->hasOneThrough(
            AiUnifyActivity::class,
            AiUnifyActivityItem::class,
            'asset_id',
            'id',
            'id',
            'activity_id'
        );
    }

    public function getInteractionTitle(): string
    {
        return $this->assetable->prompt;
    }

    public function getInteractionCover(): string
    {
        return $this->assetable->coverUrlAttr;
    }

    public function getInteractionTarget(): array
    {
        return [
            'model' => $this->getMorphClass(),
            'id'    => $this->id,
        ];
    }

    public function chatLog(): HasOne
    {
        return $this->hasOne(ChatLog::class, 'asset_no', 'no');
    }

    public function bailianAssistantLog(): HasOne
    {
        return $this->hasOne(BailianAssistantLog::class, 'ai_unify_asset_id');
    }
}
