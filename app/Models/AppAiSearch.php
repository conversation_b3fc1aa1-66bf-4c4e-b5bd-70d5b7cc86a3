<?php

namespace App\Models;

use App\Contracts\AiLogIndexInterface;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class AppAiSearch extends Model implements AiLogIndexInterface
{
    use BelongsToUser;

    protected $casts = [
        'text' => 'json'
    ];

    protected static function boot()
    {
        parent::boot();
        self::created(function ($model) {
            if ($model->user_id > 0) {
                $model->logIndex()->firstOrCreate([
                    'user_id' => $model->user_id,
                ]);
            }
        });
        self::deleting(function ($model) {
            $model->logIndex()->delete();
        });
    }

    public function logIndex(): MorphOne
    {
        return $this->morphOne(AiLogIndex::class, 'target');
    }

    public function getExtend(): array
    {
        return [
            'text' => $this->text
        ];
    }

    public function cans(): array
    {
        return [
        ];
    }

    public function getSystemMessage(): array
    {
        return [
            'message' => $this->content,
            'image'   => '',
            'video'   => '',
        ];
    }

    public function getLogType(): string
    {
        return 'search';
    }

    public function getUserMessage(): array
    {
        return [
            'message' => $this->title,
            'image'   => '',
        ];
    }

    public function getLogStatus(): int
    {
        return 2;
    }

    public function getLogStatusText(): string
    {
        return '已完成';
    }
}