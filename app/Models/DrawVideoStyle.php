<?php

namespace App\Models;

use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class DrawVideoStyle extends Model implements Sortable
{
    use HasEasyStatus, SortableTrait, Cachable;

    protected array $sortable = [
        'order_column_name'  => 'order',
        'sort_when_creating' => true,
    ];
    
}
