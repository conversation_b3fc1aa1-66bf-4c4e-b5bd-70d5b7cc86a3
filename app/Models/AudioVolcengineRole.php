<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use App\Traits\HasEasyStatus;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AudioVolcengineRole extends Model
{
    use BelongsToUser, HasEasyStatus;

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub
        self::saved(function ($model) {
            if ($model->is_default && $model->user_id === 0) {
                AudioVolcengineRole::where('id', '!=', $model->id)
                    ->where('user_id', 0)
                    ->where('is_default', 1)
                    ->update([
                        'is_default' => 0
                    ]);
            }
        });
    }

    public function voice(): BelongsTo
    {
        return $this->belongsTo(AudioVolcengineTts::class, 'voice_id', 'voice_type');
    }
}
