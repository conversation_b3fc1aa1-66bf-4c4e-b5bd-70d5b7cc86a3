<?php

namespace App\Models;

use App\Events\ViduDrawStatusChangedEvent;
use App\Exceptions\ValidatorException;
use App\Jobs\Vidu\ViduQueryJob;
use App\Models\Traits\AssetTrait;
use App\Models\Traits\FFMpegVideoTrait;
use App\Packages\ViDu\ViDu;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Log;

class PluginViduDraw extends Model
{
    use BelongsToUser,
        AutoCreateOrderNo,
        HasCovers,
        AssetTrait,
        FFMpegVideoTrait,
        SoftDeletes;

    const SCORE_VIDEO = 10;

    //生成视频使用的积分
    const STATUS_INIT = AiUnifyAsset::STATUS_INIT;

    // 状态常量
    const STATUS_ING     = AiUnifyAsset::STATUS_ING;
    const STATUS_SUCCESS = AiUnifyAsset::STATUS_SUCCESS;
    const STATUS_ERROR   = AiUnifyAsset::STATUS_ERROR;
    const TYPE_VIDU      = 'vidu';
    public string $orderNoPrefix = 'VD';
    // 订单号前缀
    protected $casts = [
        'inputs'   => 'json',
        'res'      => 'json',
        'over_at'  => 'datetime',
        'start_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        self::creating(function (PluginViduDraw $model) {
            // 计算积分
            $score = self::SCORE_VIDEO;
            if ($model->template->score > 0) {
                $score = $model->template->score;
            }
            $model->score = $score;

            $user = request()->kernel->guestUser();
            if (! $user) {
                throw new ValidatorException('用户尚未登录');
            }

            if ($model->score > 0 && $user->account->score < $score) {
                throw new ValidatorException('积分不足');
            }
        });

        self::updated(function (PluginViduDraw $model) {
            if ($model->wasChanged('status')) {
                event(new ViduDrawStatusChangedEvent(
                    $model,
                    $model->status
                ));
            }
        });
    }

    public function getMediaTypeAttribute(): string
    {
        return 'video';
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(PluginViduTemplate::class, 'scene', 'scene');
    }

    public function getExtAttribute()
    {
        return $this->res;
    }

    public function coverUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('cover'))
        );
    }

    public function setExtAttribute(array $value)
    {
        $this->attributes['res'] = json_encode($value);
    }

    public function getTypeAttribute()
    {
        return self::TYPE_VIDU;
    }

    public function getTypeTextAttribute()
    {
        return $this->template->name ?? '特效模板视频';
    }

    public function videoUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('video'))
        );
    }

    public function cans(): array
    {
        return $this->asset->cans();
    }

    public function getStatusTextAttribute()
    {
        return AiUnifyAsset::STATUS[$this->status] ?? '';
    }

    public function draw()
    {
        if ($this->canDraw()) {
            if ($this->score > 0) {
                $this->user->aiSpend(
                    ruleName: 'create_work',
                    model: $this,
                    score: -$this->score,
                    source: [
                        'remark' => '创作内容【'.$this->type_text.'】',
                    ],
                );
            }
            $this->setIng();
            try {
                $this->drawVideo();
            } catch (Exception $e) {
                $this->error($e->getMessage());
            }
        } else {
            throw new Exception('当前状态不可执行');
        }
    }

    public function canDraw(): bool
    {
        return $this->status == AiUnifyAsset::STATUS_INIT ||
            $this->status == AiUnifyAsset::STATUS_ERROR;
    }

    public function setIng()
    {
        $this->status   = AiUnifyAsset::STATUS_ING;
        $this->start_at = now();
        $this->save();
    }

    public function drawVideo()
    {
        $result = ViDu::task()->createTask($this);
        if ($result->isSuccess()) {
            $this->task_id = $result->task_id;
            $this->save();
            $this->jobQuery();
        } else {
            Log::info('生成视频错误', $result->toArray());
            $this->error($result->getMessage());
            throw new Exception($result->getMessage());
        }
    }

    public function jobQuery()
    {
        ViduQueryJob::dispatch($this)->delay(now()->addSeconds(3));
    }

    public function error(string $message)
    {
        $this->error_message = $message;
        $this->status        = self::STATUS_ERROR;
        $this->save();
    }

    public function videoQuery()
    {
        $result = ViDu::task()->creations($this->task_id);

        if ($result->isSuccess()) {
            if (in_array($result->state, [
                'created',
                'queueing',
                'processing',
            ])) {
                $this->jobQuery();
                return;
            }

            if ($result->state === 'success') {
                $cover_url = $result->creations[0]['cover_url'];
                $video_url = $result->creations[0]['url'];

                $coverInfo = $this->saveStorage($cover_url);

                list($video, $hdVideo, $cover, $width, $height, $duration, $ext) = $this->genFFUrl($video_url,
                    $this->getOssPath(),
                    $this->no);

                $this->status        = self::STATUS_SUCCESS;
                $this->cover         = $coverInfo;
                $this->video         = $video;
                $this->hd_video      = $hdVideo;
                $this->over_at       = now();
                $this->res           = [
                    'cover_url' => $cover_url,
                    'width'     => $width,
                    'height'    => $height,
                    'video_url' => $video_url,
                ];
                $this->error_message = null;
                $this->save();
            } elseif ($result->state === 'failed') {
                $this->error($result->getMessage() ?? '生成失败');
            }
        } else {
            Log::error('API调用失败', ['message' => $result->getMessage()]);
            $this->error($result->getMessage());
        }
    }

    public function saveStorage(string $url): string
    {
        $client   = new Client();
        $response = $client->request('GET', $url, [
            'verify' => false,
        ]);
        if ($response->getStatusCode() == 200) {
            $content  = $response->getBody()->getContents();
            $filename = $this->no.'.jpeg';
            $path     = $this->getOssPath();
            if (Storage::put($path.'/'.$filename, $content)) {
                return $path.'/'.$filename;
            } else {
                return '';
            }
        } else {
            return '';
        }
    }

    public function getOssPath(): string
    {
        return sprintf("vidu/%s/%s/%s",
            date('Y'),
            date('m'),
            date('d')
        );
    }

    public function getStoragePath()
    {
        return $this->getOssPath();
    }

    public function getPermissions()
    {
        return [
            'can_polling' => $this->canPolling(),
            'can_delete'  => $this->canDelete(),
        ];
    }

    public function canPolling(): bool
    {
        return in_array($this->status, [
            self::STATUS_INIT,
            self::STATUS_ING,
        ]);
    }

    public function canDelete(): bool
    {
        return in_array($this->status, [
            self::STATUS_INIT,
            self::STATUS_ERROR,
            self::STATUS_SUCCESS,
        ]);
    }
}
