<?php

namespace App\Models;

use App\Models\Enums\FastgptAppLevelEnum;
use App\Models\Traits\CanApp;
use App\Models\Traits\FastGptScope;
use App\Traits\BelongsToUser;
use App\Traits\HasEasyStatus;
use Modules\Company\Models\Traits\BelongsToCompany;

class FastgptApp extends Model
{
    use BelongsToUser,
        CanApp,
        FastGptScope,
        BelongsToCompany,
        HasEasyStatus;

    protected $casts = [
        'dataset_ids' => 'json',
        'status'      => 'integer',
        'level'       => FastgptAppLevelEnum::class
    ];

    public function chats()
    {
        return $this->hasMany(FastgptChat::class);
    }

    public function getKnowledges()
    {
        if (! $this->dataset_ids) {
            return collect();
        }
        return FastgptKnowledge::whereIn('dataset_id', $this->dataset_ids)->get();
    }

}
