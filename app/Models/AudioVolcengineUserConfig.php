<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AudioVolcengineUserConfig extends Model
{

    public    $incrementing = false;
    protected $primaryKey   = 'user_id';
    protected $casts        = [
        'tts' => 'json',
    ];

    public static function setRoleConfig(User $user, AudioVolcengineRole $role)
    {
        $config          = self::getConfig($user);
        $config->role_id = $role->id;
        $config->tts     = [
            'audio' => [
                'voice_type'  => $role->voice_id ?? 'zh_male_wennuanahu_moon_bigtts',
                'pitch_rate'  => $role->pitch_rate ?? 0,//语调  -12  ~ 12
                'speech_rate' => $role->speech_rate ?? 0,//语速-50  ~ 100
            ]
        ];
        $config->save();
        return $config;
    }

    public static function getConfig(User $user)
    {
        $default = AudioVolcengineRole::where('user_id', 0)->first();
        $config  = self::firstOrCreate([
            'user_id' => $user->id,
        ], [
            'role_id'     => $default->id ?? null,
            'tts'         => [
                'audio' => [
                    'voice_type'  => $default->voice_id ?? 'zh_male_wennuanahu_moon_bigtts',
                    'pitch_rate'  => $default->pitch_rate ?? 0,//语调  -12  ~ 12
                    'speech_rate' => $default->speech_rate ?? 0,//语速-50  ~ 100
                ]
            ],
            'image_model' => 'normal',
        ]);
        return $config;
    }

    public static function setImageModel(User $user, string $imageModel)
    {
        $config              = self::getConfig($user);
        $config->image_model = $imageModel;
        $config->save();
        return $config;
    }

    public static function setTtsAudio(User $user, string $voice_type, float $pitch_rate, float $speech_rate)
    {
        $config      = self::getConfig($user);
        $config->tts = [
            'audio' => [
                'voice_type'  => $voice_type,
                'pitch_rate'  => $pitch_rate,//语调  -12  ~ 12
                'speech_rate' => $speech_rate,//语速-50  ~ 100
            ]
        ];
        $config->save();
        return $config;
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(AudioVolcengineRole::class, 'role_id');
    }

    public function getVoice()
    {
        return AudioVolcengineTts::where('voice_type', $this->tts['audio']['voice_type'])->first();
    }
}
