<?php

namespace App\Models;

use App\Exceptions\ValidatorException;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyRole extends Model
{
    use SoftDeletes;

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'company_department_role')
            ->withPivot(['position'])
            ->withTimestamps();
    }

    public function companyDepartmentRoles(): HasMany
    {
        return $this->hasMany(CompanyDepartmentRole::class, 'company_role_id');
    }

    /**
     * Notes: 是否可以增加
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 14:41
     * @param $user
     * @param $audience
     * @param $departmentId
     * @return bool
     * @throws \App\Exceptions\ValidatorException
     */
    public function canJoin($user, $audience, $departmentId): bool
    {
        if ($this->label == 'administrator') {
            throw new ValidatorException('不可分配此角色');
        }

        if ($this->label == 'sub_administrator' && ! $user->isAdministrator()) {
            throw new ValidatorException('您不是超级管理员不可分配此职位');
        }

        if ($this->lable == 'manager') {
            if ($this->companyDepartmentRoles()->exists()) {
                throw new ValidatorException('部门主管只能有一位');
            }
        }

        $exists = $audience->companyDepartmentRoles()
            ->where('department_id', $departmentId)
            ->where('company_role_id', $this->id)
            ->exists();
        if ($exists) {
            throw new ValidatorException('此员工已有此角色');
        }
        return true;
    }

    public function canLeave($user, $audience, $departmentId): true
    {
        if ($audience->isAdministrator()) {
            throw new ValidatorException('您没有权限移除此人权限');
        }

        if ($this->label == 'administrator') {
            throw new ValidatorException('您没有权限移除此权限');
        }

        if ($this->label == 'sub_administrator' && ! $user->isAdministrator()) {
            throw new ValidatorException('您不是超级管理员不可操作此权限');
        }

        return true;
    }

    public function getDepartmentId($departmentId): int
    {
        return $this->label == 'sub_administrator' ? 0 : $departmentId;
    }
}
