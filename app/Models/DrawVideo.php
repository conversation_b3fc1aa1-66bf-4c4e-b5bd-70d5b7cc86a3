<?php

namespace App\Models;

use App\Contracts\AiLogIndexInterface;
use App\Jobs\Video\NormalVideoJob;
use App\Jobs\Video\VipVideoJob;
use App\Packages\DrawImage\DrawImage as DrawImagePackage;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DrawVideo extends Model implements AiLogIndexInterface
{
    use AutoCreateOrderNo, BelongsToUser, HasCovers, SoftDeletes;

    const STATUS_INIT    = 0;
    const STATUS_ING     = 1;
    const STATUS_SUCCESS = 2;
    const STATUS_ERROR   = 9;
    const STATUS         = [
        self::STATUS_INIT    => '待处理',
        self::STATUS_ING     => '处理中',
        self::STATUS_SUCCESS => '已完成',
        self::STATUS_ERROR   => '错误',
    ];
    const QUALITY_ARRAY  = [
        'quality' => '高质量',
        'speed'   => '速度'
    ];
    public string $orderNoPrefix = 'DV';
    protected     $casts         = [
        'over_at' => 'datetime',
        'source'  => 'json',
        'params'  => 'json',
    ];

    protected static function boot()
    {
        parent::boot();
        self::created(function ($model) {
            $model->logIndex()->firstOrCreate([
                'user_id' => $model->user_id,
            ]);
        });
        self::deleting(function ($model) {
            $model->logIndex()->delete();
        });
    }

    public function logIndex(): MorphOne
    {
        return $this->morphOne(AiLogIndex::class, 'target');
    }

    public function size(): BelongsTo
    {
        return $this->belongsTo(DrawVideoSize::class, 'size_id');
    }

    public function style(): BelongsTo
    {
        return $this->belongsTo(DrawVideoStyle::class, 'style_id');
    }

    public function getQualityTextAttribute()
    {
        return self::QUALITY_ARRAY[$this->quality] ?? '高质量';
    }

    public function getStatusTextAttribute()
    {
        return self::STATUS[$this->status] ?? '';
    }

    public function cans(): array
    {
        return [
            'polling' => $this->canPolling(),
            'delete'  => $this->canDelete(),
        ];
    }

    public function canPolling(): bool
    {
        return in_array($this->status, [
            self::STATUS_ING,
            self::STATUS_INIT,
        ]);
    }

    public function canDelete(): bool
    {
        return in_array($this->status, [
            self::STATUS_INIT,
            self::STATUS_ERROR,
            self::STATUS_SUCCESS,
        ]);
    }

    public function setOver($cover = '', $serverUrl = '')
    {
        if ($this->status == self::STATUS_ING) {
            $this->status       = self::STATUS_SUCCESS;
            $this->server_image = $serverUrl;
            $this->cover        = $cover;
            $this->over_at      = now();
            $this->save();
        }
    }

    public function scopeNoVip(Builder $query): Builder
    {
        return $query->where('is_vip', 0);
    }

    public function scopeIsVip(Builder $query): Builder
    {
        return $query->where('is_vip', 1);
    }

    public function scopeOfIng(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_ING);
    }

    public function scopeOfInit(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_INIT);
    }

    public function draw()
    {
        try {
            if ($this->status == self::STATUS_INIT) {
                if ($this->score > 0) {
                    $this->user->aiSpend('create_work', $this, -$this->score, [
                        'remark' => '生成视频【'.$this->prompt.'】',
                    ]);
                }
                $driver = DrawImagePackage::BigModelVideo();
                $result = $driver->draw($this);
                if ($result->isSuccess()) {
                    $data         = $result->toArray();
                    $this->job_id = $data['id'];
                    $this->status = self::STATUS_ING;
                    $this->source = $data;
                    $this->save();
                    $this->startListener();
                } else {
                    $this->error($result->getMessage());
                }
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    public function startListener()
    {
        if ($this->is_vip) {
            VipVideoJob::dispatch($this)->delay(now()->addSeconds(5));
        } else {
            NormalVideoJob::dispatch($this)->delay(now()->addSeconds(5));
        }
    }

    public function error(string $message = '未知错误'): void
    {
        if (! in_array($this->status, [
            self::STATUS_ERROR,
            self::STATUS_SUCCESS,
        ])) {
            $this->status        = self::STATUS_ERROR;
            $this->error_message = $message;
            $this->save();
            if ($this->score > 0) {
                $this->user->aiSpend('create_work_error', $this, $this->score, [
                    'remark' => '生成视频失败【'.$this->prompt.'】',
                ]);
            }
        } else {
            throw new Exception('状态不正确-不可将模型指定为错误状态');
        }
    }

    public function queryDone()
    {
        if ($this->status == self::STATUS_ING) {
            $driver = DrawImagePackage::BigModelVideo();
            $result = $driver->query($this->job_id);
            if ($result->isSuccess()) {
                $data = $result->toArray();
                if ($data['task_status'] == 'SUCCESS') {
                    $url = $data['video_result'][0]['url'];
                    if ($url) {
                        $content            = file_get_contents($url);
                        $filename           = Str::afterLast($url, '/');
                        $this->server_video = $url;
                        $path               = sprintf("ai-video/%s/%s/%s",
                            date('Y'),
                            date('m'),
                            date('d')
                        );
                        if (Storage::put($path.'/'.$filename, $content)) {
                            $this->cover   = $path.'/'.$filename;
                            $this->status  = self::STATUS_SUCCESS;
                            $this->over_at = now();
                            $this->save();
                        } else {
                            $this->error('保存视频失败');
                        }
                    } else {
                        $this->error('未返回资源');
                    }
                } elseif ($data['task_status'] == 'PROCESSING') {
                    $this->startListener();
                } elseif ($data['task_status'] == 'FAIL') {
                    $this->error('制作视频失败');
                } else {
                    $this->startListener();
                }
            } else {
                $this->error($result->getMessage());
            }
        }
    }

    public function setIng()
    {
        if ($this->status == self::STATUS_INIT) {
            $this->status = self::STATUS_ING;
            $this->save();
        }
    }

    public function getExtend(): array
    {
        return [
            'no' => $this->no,
        ];
    }

    public function getSystemMessage(): array
    {
        return [
            'message' => '',
            'image'   => '',
            'video'   => $this->cover_url,
        ];
    }

    public function getLogType(): string
    {
        return 'video';
    }

    public function getUserMessage(): array
    {
        return [
            'message' => $this->prompt,
            'image'   => $this->image_url,
        ];
    }

    public function getLogStatus(): int
    {
        return $this->status;
    }

    public function getLogStatusText(): string
    {
        return $this->status_text;
    }
}
