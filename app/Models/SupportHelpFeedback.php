<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SupportHelpFeedback extends Model
{
    use BelongsToUser, HasCovers;

    protected $casts = [
        'pictures' => 'json'
    ];

    protected static function boot()
    {
        parent::boot();
        self::deleted(function (SupportHelpFeedback $model) {
            foreach ($model->children as $child) {
                $child->delete();
            }
        });
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'feedback_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'feedback_id');
    }
}
