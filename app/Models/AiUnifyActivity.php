<?php

namespace App\Models;

use App\Traits\HasCovers;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;

class AiUnifyActivity extends Model
{
    use SoftDeletes, HasCovers;

    const TYPE_HOT = 'hot';
    const TYPES    = [
        self::TYPE_HOT => '热门作品'
    ];

    const STATUS_INIT   = 0;
    const STATUS_ON     = 1;
    const STATUS_FINISH = 2;
    const STATUS_CLOSE  = 9;
    const STATUS        = [
        self::STATUS_INIT   => '未开始',
        self::STATUS_ON     => '进行中',
        self::STATUS_FINISH => '活动结束',
        self::STATUS_CLOSE  => '已关闭',
    ];
    protected $casts = [
        'start_at' => 'datetime',
        'end_at'   => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub
        self::saving(function ($model) {
            $model->start_at = $model->start_at->startOfDay();
            $model->end_at   = $model->end_at->endOfDay();
        });
    }

    public function getTypeTextAttribute(): string
    {
        return self::TYPES[$this->type] ?? '';
    }

    public function getStatusTextAttribute(): string
    {
        return self::STATUS[$this->status] ?? '';
    }

    // 定义与中间表的关联（一对多）
    public function items(): HasMany
    {
        return $this->hasMany(AiUnifyActivityItem::class, 'activity_id');
    }

    // 通过中间表关联到资源（一对多通过中间表）
    public function assets(): HasManyThrough
    {
        return $this->hasManyThrough(
            AiUnifyAsset::class,
            AiUnifyActivityItem::class,
            'activity_id',
            'id',
            'id',
            'asset_id'
        );
    }
}