<?php

namespace App\Models;

use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use <PERSON><PERSON>\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class DrawVideoSize extends Model implements Sortable
{
    use HasEasyStatus, SortableTrait, Cachable;

    protected array $sortable = [
        'order_column_name'  => 'order',
        'sort_when_creating' => true,
    ];

    protected static function boot()
    {
        parent::boot(); //
        self::saving(function ($model) {
            if ($model->is_default) {
                DrawImageSize::where('id', '!=', $model->id)
                    ->where('is_default', 1)
                    ->update(['is_default' => 0]);
            }
        });
    }

    public function getText(): string
    {
        return sprintf("%sx%s", $this->width, $this->height);
    }

}
