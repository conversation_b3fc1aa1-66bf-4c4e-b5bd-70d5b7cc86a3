<?php

namespace App\Models;

use App\Packages\BailianAssistant\BLAssistant;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BailianAssistantNode extends Model
{
    public    $timestamps   = false;
    public    $incrementing = false;
    protected $primaryKey   = 'memory_node_id';
    protected $keyType      = 'string';

    public function updateContent(string $content)
    {
        try {
            $bailian = new BLAssistant();
            $result  = $bailian->memory()->updateNode($this->memory_id, $this->memory_node_id, $content);
            if ($result->requestId) {
                $this->update(['content' => $content]);
            }
        } catch (Exception $e) {
            throw new Exception('更新记忆节点失败');
        }
    }

    public function memory(): BelongsTo
    {
        return $this->belongsTo(BailianAssistant::class, 'memory_id', 'memory_id');
    }

    public function deleteContent()
    {
        try {
            $bailian = new BLAssistant();
            $result  = $bailian->memory()->deleteNode($this->memory_id, $this->memory_node_id);
            if ($result->requestId) {
                $this->delete();
            }
        } catch (Exception $e) {
            throw new Exception('删除记忆节点失败');
        }
    }
}
