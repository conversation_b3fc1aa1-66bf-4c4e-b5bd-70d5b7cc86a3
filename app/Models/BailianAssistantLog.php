<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class BailianAssistantLog extends Model
{
    use BelongsToUser;

    protected $casts = [
        'input' => 'json',
        'usage' => 'json',
        'doc'   => 'json',
    ];

    public function group(): BelongsTo
    {
        return $this->belongsTo(BailianAssistant::class, 'group_id');
    }

    public function actions(): HasMany
    {
        return $this->hasMany(BailianAssistantLogAction::class, 'log_id')
            ->orderBy('step');
    }

    public function files(): HasMany
    {
        return $this->hasMany(BailianAssistantFile::class, 'log_id');
    }

    public function knowledges(): HasMany
    {
        return $this->hasMany(BailianAssistantKnowledge::class, 'log_id');
    }

    public function aiUnifyAsset(): BelongsTo
    {
        return $this->belongsTo(AiUnifyAsset::class, 'ai_unify_asset_id');
    }
}
