<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FastgptChat extends Model
{
    use BelongsToUser;

    public function app(): BelongsTo
    {
        return $this->belongsTo(FastgptApp::class, 'fastgpt_app_id');
    }

    public function getTitle()
    {
        if ($this->custom_title) {
            return $this->custom_title;
        } else {
            return $this->title;
        }
    }

}
