<?php

namespace App\Models;

use App\Enums\Bailian\BailianLevelEnum;
use App\Enums\Bailian\MemberStatusEnum;
use App\Jobs\BaiLian\CreateKnowledgeIndexJob;
use App\Jobs\BaiLian\DeleteKnowledgeJob;
use App\Models\Traits\CanBailianKnowledge;
use App\Models\Traits\InteractionTrait;
use App\Packages\BailianAssistant\BLAssistant;
use App\Traits\BelongsToUser;
use Cache;
use DB;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Str;

class BailianKnowledge extends Model
{
    use BelongsToUser, CanBailianKnowledge, InteractionTrait;

    protected $casts = [
        'config' => 'json',
        'level'  => BailianLevelEnum::class
    ];
    const SOURCE_TYPE_CATEGORY = 'DATA_CENTER_CATEGORY';
    const SOURCE_TYPE_FILE     = 'DATA_CENTER_FILE';
    const SOURCE_TYPES         = [
        self::SOURCE_TYPE_CATEGORY => '类目类型',
        self::SOURCE_TYPE_FILE     => '文档类型'
    ];

    const SINK_TYPE_ADB               = 'ADB';
    const SINK_TYPE_BUILT_IN          = 'BUILT_IN';
    const SINK_TYPES                  = [
        self::SINK_TYPE_BUILT_IN => '内置类型',
        self::SINK_TYPE_ADB      => 'AnalyticDB for PostgreSQL 数据库'
    ];
    const STRUCTURE_TYPE_UNSTRUCTURED = 'unstructured';
    const STRUCTURE_TYPES             = [
        self::STRUCTURE_TYPE_UNSTRUCTURED => '非结构化',
    ];

    const TYPE_HELP   = 'help';
    const TYPE_NORMAL = 'normal';
    const TYPES       = [
        self::TYPE_NORMAL => '常规',
        self::TYPE_HELP   => '帮助'
    ];

    protected static function boot()
    {
        parent::boot();
        self::creating(function ($model) {
            $bailian             = new BLAssistant();
            $model->workspace_id = $bailian->knowledge()->serviceConfig->getWorkspaceId();
            $model->bailian_name = 'wateai'.Str::random(14);
        });
        self::created(function ($model) {
            CreateKnowledgeIndexJob::dispatch($model)->delay(now()->addSeconds(2));
        });

        self::deleted(function ($model) {
            // 先分离分类关联
            $model->categories()->detach();
            $model->members()->delete();

            // 只有当知识库ID存在时才分发删除任务
            if (! empty($model->knowledge_id) && ! empty($model->workspace_id)) {
                DeleteKnowledgeJob::dispatch($model->id, $model->knowledge_id, $model->workspace_id);
            }
        });
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(
            BailianCategory::class,
            'bailian_knowledge_categories',
            'knowledge_id',
            'category_id',
        );
    }

    public function files(): HasMany
    {
        return $this->hasMany(BailianKnowledgeFile::class, 'knowledge_id');
    }

    /**
     * Notes: 成员
     *
     * @Author: 玄尘
     * @Date: 2025/5/14 09:24
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function members(): HasMany
    {
        return $this->hasMany(BailianKnowledgeMember::class, 'knowledge_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(BailianKnowledgeItem::class, 'knowledge_id');
    }

    public function getSinkTypeTextAttribute()
    {
        return self::SINK_TYPES[$this->sink_type] ?? '';
    }

    public function getStructureTypeTextAttribute()
    {
        return self::STRUCTURE_TYPES[$this->structure_type] ?? '';
    }

    public function getSourceTypeTextAttribute()
    {
        return self::SOURCE_TYPES[$this->source_type] ?? '';
    }

    public function scopeOfType($builder, $type, $user)
    {
        return match ($type) {
            // 我的知识库
            'my' => $builder->where(function ($query) use ($user) {
                $query->where('user_id', $user->id)
                    ->orWhere(function ($query) use ($user) {
                        $query->where('level', BailianLevelEnum::TEAM)
                            ->whereHas('members', function ($query) use ($user) {
                                $query->where('user_id', $user->id);
                            });
                    });
            }),

            // 精选的知识库
            'featured' => $builder->where('is_featured', 1)->where('level', BailianLevelEnum::PUBLIC),
            // 公开的知识库
            'public' => $builder->where('level', BailianLevelEnum::PUBLIC)->where('user_id', '<>', $user->id),
            // 团队的知识库
            'team' => $builder->where('level', BailianLevelEnum::TEAM)
                ->whereHas('members', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                }),
            // 订阅的知识库
            'subscribe' => $builder->whereHas('subscribable', function ($query) use ($user) {
                $query->ofUser($user);
            })->where(function ($query) use ($user) {
                $query->where('level', BailianLevelEnum::PUBLIC)
                    ->orWhere(function ($query) use ($user) {
                        $query->where('level', BailianLevelEnum::TEAM)
                            ->whereHas('members', function ($query) use ($user) {
                                $query->where('user_id', $user->id);
                            });
                    });
            }),
            // 帮助知识库
            'help' => $builder->where('type', self::TYPE_HELP),
            default => $builder,
        };
    }

    /**
     * Notes: 列表里的数据统计
     *
     * @Author: 玄尘
     * @Date: 2025/5/19 11:00
     * @param $builder
     * @return mixed
     */
    protected function scopeofCount($builder)
    {
        return $builder->withCount([
            'members',
            'items' => function ($query) {
                $query->active(); // 只统计活跃的项目，排除删除中的
            },
        ]);
    }

    /**
     * Notes: 获取数据
     *
     * @Author: 玄尘
     * @Date: 2025/5/19 11:13
     * @return array
     */
    public function getCount(): array
    {
        $counts            = $this->items()
            ->active()
            ->groupBy('itemable_type')
            ->select(DB::raw('count(*) as count, itemable_type'))
            ->get()
            ->pluck('count', 'itemable_type')
            ->toArray();
        $articles_count    = $counts[(new BailianArticle())->getMorphClass()] ?? 0;
        $files_count       = $counts[(new BailianFile())->getMorphClass()] ?? 0;
        $directories_count = $counts[(new BailianDirectory())->getMorphClass()] ?? 0;
        return [
            'items_count'       => bcadd($articles_count, $files_count),
            'articles_count'    => $articles_count,
            'files_count'       => $files_count,
            'directories_count' => $directories_count,
            'members_count'     => $this->members()->count(),
        ];
    }

    public function getReadableSizeAttribute()
    {
        return $this->size ? formatBytes($this->size) : 0;
    }

    public function getBaseSize(): array
    {
        $single_knowledge_base_capacity_gb = SystemConfig::getValue('single_knowledge_base_capacity_gb', 10);
        $single_knowledge_base_capacity    = $single_knowledge_base_capacity_gb * 1024 * 1024 * 1024;
        return [
            'single_knowledge_base_capacity_gb' => $single_knowledge_base_capacity_gb,
            'single_knowledge_base_capacity'    => $single_knowledge_base_capacity,
            'size'                              => $this->size,
        ];
    }

    /**
     * Notes: 分享链接--加入知识库
     *
     * @Author: 玄尘
     * @Date: 2025/6/5 15:16
     * @return string
     */
    public function getShareUrl(): string
    {
        return "https://watestar.com/app/knowledge/{$this->id}";
    }

    /**
     * Notes: 获取审核状态
     *
     * @Author: 玄尘
     * @Date: 2025/6/9 17:52
     * @param $user
     * @return array|string[]
     */
    public function getJoinStatus($user): array
    {
        $statusMap = [
            'pending'  => '未申请',
            'waiting'  => '待审核',
            'approved' => '通过',
            'rejected' => '驳回',
        ];
        $data      = [
            'value' => 'pending',
            'text'  => '未申请',
        ];
        if (! $user) {
            return $data;
        }
        $member = $this->members()->where('user_id', $user?->id)->first();
        if (! $member) {
            return $data;
        }
        $status = 'pending';
        if ($member->status == MemberStatusEnum::PENDING) {
            $status = 'waiting';
        }
        if ($member->status == MemberStatusEnum::APPROVED) {
            $status = 'approved';
        }
        if ($member->status == MemberStatusEnum::REJECTED) {
            $status = 'rejected';
        }
        return [
            'value' => $status,
            'text'  => $statusMap[$status],
        ];
    }

    /**
     * Notes: 是否选中的知识库，通过缓存获取
     *
     * @Author: 玄尘
     * @Date: 2025/6/12 10:36
     * @param $user
     * @return bool
     */
    public function isSelected($user): bool
    {
        $selectedIds = Cache::get("bailian_knowledge_ids:{$user->id}") ?? [];
        return in_array($this->knowledge_id, $selectedIds);
    }
}
