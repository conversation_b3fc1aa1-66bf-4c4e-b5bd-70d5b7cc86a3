<?php

namespace App\Models;

use App\Packages\ImPush\ImPush;

class ImNotice extends Model
{

    const  TYPE_ALL    = 1;
    const  TYPE_SELECT = 2;

    const  TYPE_MAP    = [
        self::TYPE_ALL    => '全部',
        self::TYPE_SELECT => '指定',
    ];
    const  STATUS_INIT = 1;
    const  STATUS_SEND = 2;
    const  STATUS_FAIL = 3;

    const  STATUS_MAP   = [
        self::STATUS_INIT => '待发',
        self::STATUS_SEND => '已发',
        self::STATUS_FAIL => '失败',
    ];
    const  STATUS_LABEL = [
        self::STATUS_INIT => 'default',
        self::STATUS_SEND => 'success',
        self::STATUS_FAIL => 'danger',
    ];

    protected $casts = [
        'user_ids' => 'array',
        'send_at'  => 'datetime'
    ];

    public function send(): bool
    {
        try {
            if ($this->type == self::TYPE_ALL) {
                ImPush::push()->push($this->description, $this->title);
            } else {
                $imUserIds = ImUser::query()
                    ->whereIn('user_id', $this->user_ids)
                    ->pluck('im_user_id')
                    ->toArray();

                ImPush::push()->batchPush($imUserIds, $this->description, $this->title);
            }

            $this->update([
                'status'  => self::STATUS_SEND,
                'remark'  => '成功',
                'send_at' => now()
            ]);

            return true;
        } catch (\Exception $exception) {
            $this->update([
                'status' => self::STATUS_FAIL,
                'remark' => $exception->getMessage()
            ]);

            throw new \Exception($exception->getMessage());
        }
    }

    public function canSend(): bool
    {
        return in_array($this->status, [self::STATUS_INIT, self::STATUS_FAIL]);
    }
}
