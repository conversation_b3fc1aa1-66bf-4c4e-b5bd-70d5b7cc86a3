<?php

namespace App\Models;

use App\Enums\Bailian\ArticleProcessingStatusEnum;
use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Interfaces\BaiLianKnowledgeItemItemableInterface;
use App\Models\Traits\ArticleBusinessTrait;
use App\Models\Traits\BaiLianKnowledgeItemItemableCanDo;
use App\Models\Traits\InteractionTrait;
use App\Models\Traits\KnowledgeItemTrait;
use App\Traits\BelongsToUser;
use App\Traits\HasEasyStatus;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Storage\Models\Upload;

class BailianArticle extends Model implements BaiLianKnowledgeItemItemableInterface
{
    use BelongsToUser,
        BaiLianKnowledgeItemItemableCanDo,
        InteractionTrait,
        HasEasyStatus,
        KnowledgeItemTrait,
        ArticleBusinessTrait;

    protected $casts = [
        'type'    => BailianArticleTypeEnum::class,
        'content' => 'json'
    ];

    public function storage(): BelongsTo
    {
        return $this->belongsTo(Upload::class, 'storage_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(BailianCategory::class, 'category_id');
    }

    public function getTitle()
    {
        if ($this->title) {
            return $this->title;
        }
        return mb_substr($this->getArticleDescription(), 0, 20);
    }

    /**
     * 重写 canMoveKnowledge 方法，添加父级文章检查
     */
    public function canMoveKnowledge($user): bool
    {
        return $this->isMy($user) && ! $this->isInKnowledge() && is_null($this->parent_id);
    }

    public function getDescription()
    {
        // 优先返回数据库中的 description 字段，如果为空则动态生成
        return $this->description ?: $this->generateDescription();
    }

}
