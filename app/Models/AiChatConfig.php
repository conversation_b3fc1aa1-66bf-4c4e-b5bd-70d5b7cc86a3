<?php

namespace App\Models;

use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\DB;

class AiChatConfig extends Model
{
    use Cachable, HasEasyStatus, HasCovers;

    const WENXIN    = 'wenxin';
    const DOUBAO    = 'doubao';
    const TONGYI    = 'tongyi';
    const ZHIPU     = 'zhipu';
    const KIMI      = 'kimi';
    const HUNYUAN   = 'hunyuan';
    const DEEPSEEK  = 'deepseek';
    const CHATGPT   = 'chatgpt4';
    const LOCALHOST = 'localhost';
    const KEYS      = [
        self::WENXIN    => '文心一言',
        self::DOUBAO    => '豆包',
        self::TONGYI    => '通义千问',
        self::ZHIPU     => '智普AI',
        self::KIMI      => 'Kimi',
        self::HUNYUAN   => '腾讯混元',
        self::DEEPSEEK  => 'Deepseek',
        self::CHATGPT   => 'ChatGpt4',
        self::LOCALHOST => '本地',
    ];

    protected $casts = [
        'params' => 'json'
    ];

    protected static function boot()
    {
        parent::boot();
        self::saving(function ($model) {
            if ($model->is_default) {
                DB::table('ai_chat_configs')
                    ->where('id', '!=', $model->id)
                    ->where('is_default', 1)
                    ->update([
                        'is_default' => 0
                    ]);
            }
        });
    }

    public function engine(): HasOne
    {
        return $this->hasOne(AiChatEngine::class, 'id', 'engine_id');
    }

    public function multiEngine(): HasOne
    {
        return $this->hasOne(AiChatEngine::class, 'id', 'engine_multi_id');
    }

    public function thinkEngine(): HasOne
    {
        return $this->hasOne(AiChatEngine::class, 'id', 'engine_think_id');
    }

    public function getToolsI($engine)
    {
        return $engine->has_tools ? '&nbsp;&nbsp;<i class="fa fa-wrench text-red"></i>' : '&nbsp;&nbsp;<i class="fa fa-wrench text-black"></i>';
    }

    public function getSystemI($engine)
    {
        return blank($engine->system) ? '&nbsp;&nbsp;<i class="fa fa-newspaper-o text-black"></i>' : '&nbsp;&nbsp;<i class="fa fa-newspaper-o text-red"></i>';
    }

    public function getSearchI($engine)
    {
        return $engine->search ? '&nbsp;&nbsp;<i class="fa fa-search text-red"></i>' : '&nbsp;&nbsp;<i class="fa fa-search text-black"></i>';
    }

}
