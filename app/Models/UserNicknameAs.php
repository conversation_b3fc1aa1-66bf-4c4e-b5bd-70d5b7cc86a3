<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;

class UserNicknameAs extends Model
{
    use BelongsToUser;

    public $timestamps = false;

    public function scopeTarget(Builder $query, User|int $user): void
    {
        if ($user instanceof User) {
            $query->where('target_id', $user->getKey());
        } else {
            $query->where('target_id', $user);
        }
    }
}
