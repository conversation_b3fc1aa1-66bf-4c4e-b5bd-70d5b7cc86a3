<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AudioRoomsCallback extends Model
{
    public    $incrementing = false;
    protected $primaryKey   = 'event_id';
    protected $keyType      = 'string';
    protected $casts        = [
        'event_data' => 'json'
    ];

    public function room(): BelongsTo
    {
        return $this->belongsTo(AudioRoom::class, 'room_id');
    }
}
