<?php

namespace App\Models;

class AudioVolcengineTts extends Model
{
    //趣味方言  通用场景 角色扮演 多语种 播报解说 有声阅读 视频配音 多情感
    const SCENE_TYPE_FUN           = 1;
    const SCENE_TYPE_COMMON        = 2;
    const SCENE_TYPE_ROLE          = 3;
    const SCENE_TYPE_MULTI         = 4;
    const SCENE_TYPE_LANGUAGE      = 5;
    const SCENE_TYPE_READ          = 6;
    const SCENE_TYPE_VOICE         = 7;
    const SCENE_TYPE_MULTI_EMOTION = 8;
    const SCENE_TYPE_MAP           = [
        self::SCENE_TYPE_FUN      => '趣味方言',
        self::SCENE_TYPE_COMMON   => '通用场景',
        self::SCENE_TYPE_ROLE     => '角色扮演',
        self::SCENE_TYPE_MULTI    => '多语种',
        self::SCENE_TYPE_LANGUAGE => '播报解说',
        self::SCENE_TYPE_READ     => '有声阅读',
        self::SCENE_TYPE_VOICE    => '视频配音',
    ];
    //中文  英语  中/英 日/西
    const LANGUAGE_TYPE_CHINESE  = 1;
    const LANGUAGE_TYPE_ENGLISH  = 2;
    const LANGUAGE_TYPE_JAPANESE = 3;
    const LANGUAGE_TYPE_KOREAN   = 4;
    const LANGUAGE_TYPE_MAP      = [
        self::LANGUAGE_TYPE_CHINESE  => '中文',
        self::LANGUAGE_TYPE_ENGLISH  => '英语',
        self::LANGUAGE_TYPE_JAPANESE => '日/西',
        self::LANGUAGE_TYPE_KOREAN   => '韩语',
    ];

    public function getLangeageTextAttribute()
    {
        return self::LANGUAGE_TYPE_MAP[$this->language];
    }

    public function getSceneTextAttribute()
    {
        return self::SCENE_TYPE_MAP[$this->scene];
    }
}
