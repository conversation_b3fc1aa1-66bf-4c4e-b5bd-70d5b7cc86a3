<?php

namespace App\Models;

use App\Exceptions\ValidatorException;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Models\AccountLog;
use Modules\Payment\Models\AccountRule;
use Modules\Payment\Models\Payment;
use Modules\Payment\Traits\HasPayment;

class Card extends Model
{
    use BelongsToUser, HasPayment;

    const STATUS_INIT    = 1;
    const STATUS_USED    = 2;
    const STATUS_FAILURE = 9;
    const STATUS         = [
        self::STATUS_INIT    => '待使用',
        self::STATUS_USED    => '已使用',
        self::STATUS_FAILURE => '失效',
    ];
    protected $casts = [
        'used_at'    => 'datetime',
        'failure_at' => 'datetime',
    ];

    public function getStatusTextAttribute(): string
    {
        return self::STATUS[$this->status] ?? '';
    }

    public function scopeInit(Builder $query): void
    {
        $query->where('status', self::STATUS_INIT);
    }

    public function scopeUsed(Builder $query): void
    {
        $query->where('status', self::STATUS_USED);
    }

    public function scopeFailure(Builder $query): void
    {
        $query->where('status', self::STATUS_FAILURE);
    }

    public function getTotalAmount()
    {
        return 0;
    }

    public function getNo()
    {
        return $this->no;
    }

    public function getOrderNumber()
    {
        return $this->score;
    }

    public function getTitle()
    {
        return '兑换卡充值';
    }

    public function useDo(User $user)
    {
        if ($this->status == self::STATUS_INIT) {
            DB::transaction(function () use ($user) {
                $this->status  = self::STATUS_USED;
                $this->user_id = $user->id;
                $this->used_at = now();
                $this->save();
                $rule        = AccountRule::where('slug', 'exchange_card')->first();
                $doesntExist = AccountLog::where('rule_id', $rule->id)
                    ->where('source->exchange_card_id', $this->id)
                    ->doesntExist();
                if ($doesntExist) {
                    $payment = Payment::createPayment($user, $this, Gateway::BALANCE->value,
                        Channel::BALANCE_SCORE->value);
                    $payment->paid(now());
                    $user->account->exec($rule, $this->score, null, [
                        'remark'           => "【{$this->batch->name}】积分卡兑换【{$this->no}】",
                        'exchange_card_id' => $this->id,
                    ]);
                } else {
                    throw new ValidatorException('已存在兑换卡使用积分记录');
                }
            });
        } else {
            throw new ValidatorException('兑换卡状态不可使用');
        }
    }

    public function batch(): BelongsTo
    {
        return $this->belongsTo(CardBatch::class, 'batch_id');
    }
}
