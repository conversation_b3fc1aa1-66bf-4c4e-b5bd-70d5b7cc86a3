<?php

namespace App\Models;

use App\Contracts\AiLogIndexInterface;
use App\Packages\DrawImage\Drivers\BigModel;
use App\Packages\DrawImage\Drivers\DouBao;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DrawImage extends Model implements AiLogIndexInterface
{
    use AutoCreateOrderNo, BelongsToUser, HasCovers, SoftDeletes;

    const STATUS_INIT    = 0;
    const STATUS_ING     = 1;
    const STATUS_SUCCESS = 2;
    const STATUS_ERROR   = 9;
    const STATUS         = [
        self::STATUS_INIT    => '待处理',
        self::STATUS_ING     => '处理中',
        self::STATUS_SUCCESS => '已完成',
        self::STATUS_ERROR   => '错误',
    ];
    public string $orderNoPrefix = 'DI';

    protected $casts = [
        'over_at' => 'datetime',
        'source'  => 'json',
    ];

    protected static function boot()
    {
        parent::boot();
        self::created(function ($model) {
            $model->logIndex()->firstOrCreate([
                'user_id' => $model->user_id,
            ]);
        });
        self::deleting(function ($model) {
            $model->logIndex()->delete();
        });
    }

    public function logIndex(): MorphOne
    {
        return $this->morphOne(AiLogIndex::class, 'target');
    }

    public function setIng()
    {
        if ($this->status == self::STATUS_INIT) {
            $this->status = self::STATUS_ING;
            $this->save();
        }
    }

    public function setOver($cover = '', $serverUrl = '')
    {
        if ($this->status == self::STATUS_ING) {
            $this->status       = self::STATUS_SUCCESS;
            $this->server_image = $serverUrl;
            $this->cover        = $cover;
            $this->over_at      = now();
            $this->save();
        }
    }

    /**
     * 关联尺寸
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function size(): BelongsTo
    {
        return $this->belongsTo(DrawImageSize::class, 'size_id');
    }

    /**
     * 关联样式库
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function style(): BelongsTo
    {
        return $this->belongsTo(DrawImageStyle::class, 'style_id');
    }

    public function canDraw(): bool
    {
        return $this->status == self::STATUS_INIT;
    }

    public function cans(): array
    {
        return [
            'polling' => $this->canPolling(),
            'delete'  => $this->canDelete(),
        ];
    }

    public function canPolling(): bool
    {
        return in_array($this->status, [
            self::STATUS_ING,
            self::STATUS_INIT,
        ]);
    }

    public function canDelete(): bool
    {
        return in_array($this->status, [
            self::STATUS_INIT,
            self::STATUS_ERROR,
            self::STATUS_SUCCESS,
        ]);
    }

    public function getExtend(): array
    {
        return [
            'no'    => $this->no,
            'size'  => $this->size->name,
            'style' => $this->style->name,
        ];
    }

    public function getStatusTextAttribute()
    {
        return self::STATUS[$this->status] ?? '';
    }

    public function draw()
    {
        try {
            $driver  = $this->style->getDriver();
            $resault = $driver->setSize($this->size)
                ->draw($this);
            if ($resault->isSuccess()) {
                $data = $resault->toArray();
                $url  = match (get_class($driver)) {
                    BigModel::class => $data['data'][0]['url'] ?? '',
                    DouBao::class => $data['data']['binary_data_base64'][0] ?? '',
                    default => '',
                };
                if (get_class($driver) == DouBao::class) {
                    unset($data['data']['binary_data_base64']);
                }
                $this->source = $data;

                if (Str::startsWith($url, ['http://', 'https://'])) {
                    $content            = file_get_contents($url);
                    $filename           = Str::afterLast($url, '/');
                    $this->server_image = $url;
                } elseif (! blank($url)) {
                    $content  = base64_decode($url);
                    $filename = Str::random(16).'.png';
                } else {
                    $this->error('生成图片无地址');
                    return false;
                }
                $path = sprintf("ai-image/%s/%s/%s",
                    date('Y'),
                    date('m'),
                    date('d')
                );
                if (Storage::put($path.'/'.$filename, $content)) {
                    $this->cover   = $path.'/'.$filename;
                    $this->status  = self::STATUS_SUCCESS;
                    $this->over_at = now();
                    $this->save();
                } else {
                    $this->error('保存图片失败');
                }
            } else {
                $this->error($resault->getMessage());
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    public function error(string $message = '未知错误'): void
    {
        if (! in_array($this->status, [
            self::STATUS_ERROR,
            self::STATUS_SUCCESS,
        ])) {
            $this->status        = self::STATUS_ERROR;
            $this->error_message = $message;
            $this->save();
            if ($this->score > 0) {
                $this->user->aiSpend('create_work_error', $this->drawImage, $this->drawImage->score, [
                    'remark' => '绘制图片失败【'.$this->drawImage->prompt.'】',
                ]);
            }
        } else {
            throw new Exception('状态不正确-不可将模型指定为错误状态');
        }
    }

    public function getSystemMessage(): array
    {
        return [
            'message' => '',
            'image'   => $this->cover_url,
            'video'   => '',
        ];
    }

    public function getLogType(): string
    {
        return 'image';
    }

    public function getUserMessage(): array
    {
        return [
            'message' => $this->prompt,
            'image'   => $this->image_url,
        ];
    }

    public function getLogStatus(): int
    {
        return $this->status;
    }

    public function getLogStatusText(): string
    {
        return $this->status_text;
    }
}
