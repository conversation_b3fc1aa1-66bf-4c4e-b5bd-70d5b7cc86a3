<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AiUnifyActivityItem extends Model
{
    // 定义与活动的关联（多对一）
    public function activity(): BelongsTo
    {
        return $this->belongsTo(AiUnifyActivity::class, 'activity_id');
    }

    // 定义与资源的关联（多对一）
    public function asset(): BelongsTo
    {
        return $this->belongsTo(AiUnifyAsset::class, 'asset_id');
    }
}