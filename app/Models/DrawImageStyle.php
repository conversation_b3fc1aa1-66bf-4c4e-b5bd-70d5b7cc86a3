<?php

namespace App\Models;

use App\Packages\DrawImage\DrawImage as DrawImagePackage;
use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class DrawImageStyle extends Model implements Sortable
{
    use HasEasyStatus, SortableTrait, Cachable;

    const ZHIPU             = 'cogview-3-plus';
    const DOUBAO_TEXT       = 'doubao_text';
    const DOUBAO_TEXT_IMAGE = 'doubao_text_image';
    const DOUBAO_IMAGE      = 'doubao_image';

    const Models = [
        self::ZHIPU             => '智普',
        self::DOUBAO_TEXT       => '豆包文生图',
        self::DOUBAO_TEXT_IMAGE => '豆包文生图/图生图',
        self::DOUBAO_IMAGE      => '豆包图生图',
    ];
    protected array $sortable = [
        'order_column_name'  => 'order',
        'sort_when_creating' => true,
    ];
    protected       $casts    = [
        'params' => 'json'
    ];

    protected static function boot()
    {
        parent::boot();
        self::saving(function ($model) {
            if ($model->text_default) {
                DrawImageStyle::where('id', '!=', $model->id)
                    ->where('text_default', 1)
                    ->update(['text_default' => 0]);
            }
            if ($model->image_default) {
                DrawImageStyle::where('id', '!=', $model->id)
                    ->where('image_default', 1)
                    ->update(['image_default' => 0]);
            }
        });
    }

    public function getDriver()
    {
        return match ($this->model_type) {
            self::ZHIPU => DrawImagePackage::BigModel($this),
            self::DOUBAO_IMAGE, self::DOUBAO_TEXT_IMAGE, self::DOUBAO_TEXT, => DrawImagePackage::DouBao($this),
        };
    }

}
