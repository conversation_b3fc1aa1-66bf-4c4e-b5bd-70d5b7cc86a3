<?php

namespace App\Models;

use App\Traits\HasCovers;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Company\Models\Certification;

class Company extends Model
{
    use HasCovers;

    protected $table = 'company';

    protected $casts = [
        'data_json' => 'json',
    ];

    const IS_CHECK       = [
        1 => '已审核',
        0 => '未审核',
    ];
    const IS_CHECK_LABEL = [
        1 => 'info',
        0 => 'pink',
    ];
    const IS_OPEN        = [
        1 => '开启',
        0 => '关闭',
    ];
    const IS_OPEN_LABEL  = [
        1 => 'info',
        0 => 'pink',
    ];

    const TYPE_ZU    = 0;
    const TYPE_IMG   = 1;
    const TYPE_VIDEO = 2;

    const TYPE_MAP = [
        self::TYPE_ZU    => '组合',
        self::TYPE_IMG   => '图片',
        self::TYPE_VIDEO => '视频'
    ];

    const TYPE_MAP_LABEL = [
        self::TYPE_ZU    => 'info',
        self::TYPE_IMG   => 'pink',
        self::TYPE_VIDEO => 'warning',
    ];

    public function staffs(): HasMany
    {
        return $this->hasMany(CompanyStaff::class);
    }

    public function users(): HasMany
    {
        return $this->hasMany(CompanyUser::class);
    }

    public function businesses(): HasMany
    {
        return $this->hasMany(Business::class);
    }

    /**
     * Notes: 关联认证
     *
     * @Author: 玄尘
     * @Date: 2024/12/25 09:43
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function certification(): HasOne
    {
        return $this->hasOne(Certification::class);
    }

    public function isCertification(): bool
    {
        return $this->certification()->where('status', 1)->exists();
    }

    /**
     * Notes: 获取企业介绍
     *
     * @Author: 玄尘
     * @Date: 2024/12/23 09:38
     * @return mixed|null
     */
    public function getCompany($channel = 'system')
    {
        switch ($this->company_type) {
            case self::TYPE_ZU:
                return $this->company;
                break;
            case self::TYPE_IMG:
            case self::TYPE_VIDEO:

                if ($channel != 'system') {
                    return $this->company;
                } else {
                    $data = $company = json_decode($this->company, true);
                    if (count($company)) {
                        $data = [];
                        foreach ($company as $item) {
                            $data[] = ltrim(parse_url($item, PHP_URL_PATH), '/');
                        }
                    }
                    return $data;
                }
                break;
            default:
                return null;
        }
    }

    /**
     * Notes: 获取业务介绍
     *
     * @Author: 玄尘
     * @Date: 2024/12/23 09:38
     * @return mixed|null
     */
    public function getBusiness($channel = 'system'): mixed
    {
        switch ($this->business_type) {
            case self::TYPE_ZU:
                return $this->business ?? '';
                break;
            case self::TYPE_IMG:
            case self::TYPE_VIDEO:
                if ($channel != 'system') {
                    return $this->business;
                } else {
                    $data = $business = json_decode($this->business, true);
                    if (count($business)) {
                        $data = [];
                        foreach ($business as $item) {
                            $data[] = ltrim(parse_url($item, PHP_URL_PATH), '/');
                        }
                    }
                    return $data;
                }
                break;
            default:
                return null;
        }
    }

    /**
     * Notes: 获取企业logo
     *
     * @Author: 玄尘
     * @Date: 2024/12/23 09:39
     * @return string
     */
    public function getLogoUrlAttribute()
    {
        $logo     = $this->logo;
        $imageUrl = $logo;

        if (filter_var($logo, FILTER_VALIDATE_URL)) {
            $thumbPath = parse_url($logo, PHP_URL_PATH);  // 仅获取路径部分
            $thumb     = Str::beforeLast($thumbPath, '.').'-thumb.'.Str::afterLast($thumbPath, '.');
        } else {
            $thumb = Str::beforeLast($logo, '.').'-thumb.'.Str::afterLast($logo, '.');
        }
        if (Storage::exists($thumb)) {
            // 如果缩略图存在，使用缩略图路径
            $imageUrl = $thumb;
        }

        return $this->parseImageUrl($imageUrl);
    }

}
