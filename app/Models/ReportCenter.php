<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ReportCenter extends Model
{
    use BelongsToUser, HasCovers;

    const STATUS_INIT = 0;
    const STATUS_ING  = 1;
    const STATUS_OVER = 2;
    const STATUS      = [
        self::STATUS_INIT => '待处理',
        self::STATUS_ING  => '处理中',
        self::STATUS_OVER => '完成',
    ];
    protected $casts = [
        'pictures' => 'json',
    ];

    public function modelable(): MorphTo
    {
        return $this->morphTo();
    }

    public function getStatusTextAttribute(): string
    {
        return self::STATUS[$this->status] ?? '未知';
    }

    public function cans()
    {
        return [
            'delete' => $this->canDelete(),
        ];
    }

    public function canDelete()
    {
        return $this->status == self::STATUS_INIT;
    }
}
