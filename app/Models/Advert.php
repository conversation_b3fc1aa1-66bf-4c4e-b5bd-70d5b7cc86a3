<?php

namespace App\Models;

use App\Traits\HasCovers;

class Advert extends Model
{
    use HasCovers;

    const TYPE_WEB          = 'web';
    const TYPE_APP_SCHEME   = 'app_scheme';
    const TYPE_IN_APP       = 'in_app';
    const TYPE_MINI_PROGRAM = 'mini_program';
    const TYPE_BANNER       = 'banner';
    const TYPE_MAP          = [
        self::TYPE_BANNER       => '图片',
        self::TYPE_WEB          => '网页',
        self::TYPE_APP_SCHEME   => 'app scheme',
        self::TYPE_IN_APP       => '程序内跳转',
        self::TYPE_MINI_PROGRAM => '小程序',
    ];
    const TYPE_LABEL_MAP    = [
        self::TYPE_WEB          => 'primary',
        self::TYPE_APP_SCHEME   => 'success',
        self::TYPE_IN_APP       => 'info',
        self::TYPE_MINI_PROGRAM => 'warning',
    ];

    const POSITION_INDEX     = 'index_top';
    const POSITION_MAP       = [
        self::POSITION_INDEX => '首页顶部',
    ];
    const POSITION_LABEL_MAP = [
        self::POSITION_INDEX => 'primary',
    ];
    protected $casts = [
        'params' => 'json'
    ];

    public function getTypeTextAttribute(): string
    {
        return self::TYPE_MAP[$this->type] ?? '';
    }

    public function getPositionTextAttribute(): string
    {
        return self::POSITION_MAP[$this->position] ?? '';
    }

}
