<?php

namespace App\Models;

use App\Packages\BailianAssistant\BLAssistant;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BailianCategory extends Model
{
    use BelongsToUser,
        HasCovers,
        HasEasyStatus;

    const TYPE_HELP   = 'help';
    const TYPE_NORMAL = 'normal';
    const TYPES       = [
        self::TYPE_NORMAL => '常规分类',
        self::TYPE_HELP   => '帮助中心'
    ];

    protected static function boot()
    {
        parent::boot();
        self::created(function (BailianCategory $model) {
            $bailian = new BLAssistant();
            $result  = $bailian->application()
                ->AddCategory($model->name, $model->parent->bailian_id ?? '');
            if ($result->success) {
                $model->update([
                    'bailian_id' => $result->data->categoryId,
                ]);
            } else {
                $model->delete();
                throw new Exception($result->message);
            }
        });
        self::deleted(function (BailianCategory $model) {
            foreach ($model->children as $child) {
                $child->delete();
            }
            if ($model->bailian_id) {
                $bailian = new BLAssistant();
                $bailian->application()->DeleteCategory($model->bailian_id);
            }
        });
    }

    public function children(): HasMany
    {
        return $this->hasMany(BailianCategory::class, 'parent_id');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(BailianCategory::class, 'parent_id');
    }

    /**
     * Notes: 关联知识库
     *
     * @Author: 玄尘
     * @Date: 2025/5/7 13:42
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function knowledges(): BelongsToMany
    {
        return $this->belongsToMany(BailianKnowledge::class, 'bailian_knowledge_category');
    }
}
