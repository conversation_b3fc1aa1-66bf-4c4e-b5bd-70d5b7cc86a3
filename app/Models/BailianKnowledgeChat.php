<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BailianKnowledgeChat extends Model
{
    use BelongsToUser;

    const IS_LIKED_UP   = 1;
    const IS_LIKED_DOWN = 0;
    const IS_LIKED_MAP  = [
        self::IS_LIKED_UP   => '点赞',
        self::IS_LIKED_DOWN => '点踩',
    ];

    protected $casts = [
        'inputs' => 'json',
        'usage'  => 'json',
    ];

    /**
     * Notes: 关联的知识库
     *
     * @Author: 玄尘
     * @Date: 2025/5/22 09:41
     */
    public function knowledge()
    {
        return $this->belongsToMany(
            BailianKnowledge::class,
            'bailian_knowledge_chat_knowledge',
            'bailian_chat_id',
            'knowledge_id'
        );
    }

    public function useKnowledge(): HasMany
    {
        return $this->hasMany(BailianKnowledgeChatKnowledge::class, 'bailian_chat_id');
    }

}
