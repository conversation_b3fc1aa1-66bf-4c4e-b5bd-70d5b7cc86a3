<?php

namespace App\Models;

use App\Enums\Bailian\ItemSyncStatusEnum;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class BailianKnowledgeItem extends Model
{
    //增加排序功能
    use  BelongsToUser;

    const ITEMABLE_TYPES      = [
        'bailian_article'   => '笔记',
        'bailian_directory' => '目录',
        'bailian_file'      => '文件'
    ];
    const ITEMABLE_TYPE_LABEL = [
        'bailian_article'   => 'info',
        'bailian_directory' => 'primary',
        'bailian_file'      => 'success',
    ];

    protected $casts = [
        'synced_at'   => 'datetime',
        'sync_status' => ItemSyncStatusEnum::class,
    ];

    public function baiLianKnowledge(): BelongsTo
    {
        return $this->belongsTo(BailianKnowledge::class, 'knowledge_id');
    }

    // 获取父级目录
    public function parent(): BelongsTo
    {
        return $this->belongsTo(BailianKnowledgeItem::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(BailianKnowledgeItem::class, 'parent_id');
    }

    /**
     * 获取关联的内容项（多态关联）
     */
    public function itemable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeOfUser($query, $user)
    {
        return $query->whereHas('baiLianKnowledge', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        });
    }

    /**
     * 查询作用域：排除删除中的项目和资源已被删除的项目
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('sync_status', [
            ItemSyncStatusEnum::DELETING,
        ])->whereHasMorph('itemable', [
            BailianArticle::class,
            BailianFile::class,
            BailianDirectory::class,
        ]);
    }

    /**
     * 查询作用域：只查询删除中的项目
     */
    public function scopeDeleting($query)
    {
        return $query->where('sync_status', ItemSyncStatusEnum::DELETING);
    }

    /**
     * 判断是否正在删除中
     */
    public function isDeleting(): bool
    {
        return $this->sync_status === ItemSyncStatusEnum::DELETING;
    }

    public function getCan($user)
    {
        return [
            'delete'           => $user ? $this->itemable?->canDelete($user) : false,
            'remove_knowledge' => $user ? $this->itemable?->canRemoveKnowledge($user) : false,
            'need_sync'        => $this->needsSync(),
        ];
    }

    /**
     * Notes: 根据类型查询
     *
     * @Author: 玄尘
     * @Date: 2025/5/19 14:00
     * @param $builder
     * @param $type
     * @param  int|null  $parent_id
     */
    public function scopeOfType($builder, $type, int $parent_id = null)
    {
        if ($type == 'file') {
            $builder->whereIn('itemable_type', [
                (new BailianFile())->getMorphClass(),
                (new BailianDirectory())->getMorphClass()
            ])->where('parent_id', $parent_id);
        } elseif ($type == 'article') {
            $builder->whereIn('itemable_type', [
                (new BailianArticle())->getMorphClass(),
            ])->when($parent_id, function ($query) use ($parent_id) {
                $query->where('parent_id', $parent_id);
            });
        } elseif ($type == 'directory') {
            $builder->where('itemable_type', (new BailianDirectory())->getMorphClass())->where('parent_id', $parent_id);
        } elseif ($type == 'all') {
            $builder->whereIn('itemable_type', [
                (new BailianArticle())->getMorphClass(),
                (new BailianFile())->getMorphClass(),
                (new BailianDirectory())->getMorphClass(),
            ])->where('parent_id', $parent_id);
        }
        return $builder;
    }

    /**
     * Notes: 获取下载地址
     *
     * @Author: 玄尘
     * @Date: 2025/5/20 10:16
     * @return string|null
     */
    public function getDownloadUrl(): ?string
    {
        $url = '';
        if ($this->itemable_type == 'bailian_file') {
            $url = $this->itemable?->getDownloadUrl();
        }
        return $url;
    }

    public function isDirectory(): bool
    {
        return $this->itemable_type == 'bailian_directory';
    }

    /**
     * 检查是否需要同步到阿里云
     */
    public function needsSync(): bool
    {
        return in_array($this->sync_status, [ItemSyncStatusEnum::PENDING, ItemSyncStatusEnum::FAILED]);
    }

    /**
     * 检查是否正在同步
     */
    public function isSyncing(): bool
    {
        return $this->sync_status === ItemSyncStatusEnum::SYNCING;
    }

    /**
     * 检查是否已同步
     */
    public function isSynced(): bool
    {
        return $this->sync_status === ItemSyncStatusEnum::SYNCED;
    }

    /**
     * 检查是否同步失败
     */
    public function isSyncFailed(): bool
    {
        return $this->sync_status === ItemSyncStatusEnum::FAILED;
    }

    /**
     * 标记为同步中
     */
    public function markAsSyncing(): void
    {
        $this->update([
            'sync_status' => ItemSyncStatusEnum::SYNCING,
            'sync_error'  => null,
        ]);
    }

    /**
     * 标记为同步成功
     */
    public function markAsSynced(): void
    {
        $this->update([
            'sync_status' => ItemSyncStatusEnum::SYNCED,
            'sync_error'  => null,
            'synced_at'   => now(),
        ]);
    }

    /**
     * 标记为同步失败
     */
    public function markAsSyncFailed(string $error): void
    {
        $this->update([
            'sync_status' => ItemSyncStatusEnum::FAILED,
            'sync_error'  => $error,
        ]);
    }

    /**
     * 获取同步状态标签
     */
    public function getSyncStatusLabel(): string
    {
        return $this->sync_status?->toString() ?? '未知';
    }
}