<?php

namespace App\Models;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\Api\Voice\XunfeiTrait;
use App\Jobs\Voice\AudioXfQueryJob;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AudioXfTimbre extends Model
{
    use BelongsToUser, SoftDeletes, HasCovers, XunfeiTrait;

    const STATUS_DOWN    = 0;
    const STATUS_SUCCESS = 1;
    const STATUS_CREATE  = 4;
    const STATUS_SUBMIT  = 5;
    const STATUS_ERROR   = 9;

    const STATUS = [
        self::STATUS_DOWN    => '停用',
        self::STATUS_SUCCESS => '正常',
        self::STATUS_CREATE  => '创建',
        self::STATUS_SUBMIT  => '提交',
        self::STATUS_ERROR   => '失败',
    ];

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub
        self::deleted(function ($model) {
            $model->logs()->delete();
        });
    }

    public function logs(): HasMany
    {
        return $this->hasMany(AudioXfTimbreLog::class, 'task_id', 'task_id');
    }

    public function cans(): array
    {
        return [
            'polling'  => $this->canPolling(),
            'continue' => $this->canContinue(),
            'delete'   => $this->canDelete(),
        ];
    }

    public function canPolling(): bool
    {
        return $this->status === self::STATUS_SUBMIT;
    }

    public function canContinue(): bool
    {
        return $this->status === self::STATUS_CREATE;
    }

    public function canDelete(): bool
    {
        return in_array($this->status, [
            self::STATUS_ERROR,
            self::STATUS_CREATE,
            self::STATUS_DOWN,
            self::STATUS_SUCCESS,
        ]);
    }

    public function getStatusTextAttribute()
    {
        return self::STATUS[$this->status] ?? '';
    }

    public function urlUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('url'))
        );
    }

    public function imageUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('image_url'))
        );
    }

    public function taskQuery()
    {
        if ($this->status === self::STATUS_SUBMIT) {
            $result = $this->getResponse('task/result', [
                'taskId' => $this->task_id,
            ]);
            if ($result['code'] === 0) {
                $data        = $result['data'];
                $trainStatus = $data['trainStatus'];
                if ($trainStatus === -1) {
                    $this->jobQuery();
                    return false;
                }
                if ($trainStatus === 0) {
                    $this->error($data['failedDesc']);
                    return false;
                }
                if ($trainStatus === 1) {
                    $assetId         = $data['assetId'];
                    $this->voice_id  = $assetId;
                    $this->people    = '训练'.$this->task_id;
                    $this->scenarios = '训练的个人音库';
                    $this->status    = self::STATUS_SUCCESS;
                    $this->save();
                }
            } else {
                throw new ValidatorException($result['desc']);
            }
        } else {
            throw new ValidatorException('任务已经完成');
        }
    }

    public function jobQuery()
    {
        AudioXfQueryJob::dispatch($this)->delay(now()->addSeconds(10));
    }

    public function error(string $message = '')
    {
        $this->update([
            'status'  => self::STATUS_ERROR,
            'message' => $message,
        ]);
    }
}
