<?php

namespace App\Exceptions;

use Exception;

class ApiBaseException extends Exception
{
    protected string $appKey = '';

    public function __construct(string $message = "", int $code = 500)
    {
        parent::__construct($message, $code);
    }

    public function getAppKey(): string
    {
        return $this->appKey ?: env('APP_DEFAULT_KEY');
    }

    public function setAppKey(string $appKey): void
    {
        $this->appKey = $appKey;
    }
}