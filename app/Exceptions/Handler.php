<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Exceptions\InvalidSignatureException;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;
use Tinywan\Jwt\Exception\JwtTokenException;

class Handler extends ExceptionHandler
{
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    public function register(): void
    {
    }

    public function render($request, Exception|Throwable $exception)
    {
        return $this->prepareJsonResponse($request, $exception);
    }

    protected function prepareJsonResponse($request, Throwable $e): JsonResponse
    {
        $json = match (true) {
            $e instanceof ThrottleRequestsException => [
                'code'    => 500,
                'message' => __('messages.to_many_request')
            ],
            $e instanceof InvalidSignatureException => [
                'code'    => 500,
                'message' => __('messages.invalid_signature')
            ],
            $e instanceof NotFoundHttpException => [
                'code'    => 500,
                'message' => __('messages.api_notfound')
            ],
            $e instanceof JwtTokenException => [
                'code'    => 401,
                'message' => '请先登录'
            ],

            default => [
                'code'    => $e->getCode() == 0 ? 500 : $e->getCode(),
                'message' => $e->getMessage()
            ]
        };
        if (Str::startsWith($request->getRequestUri(), '/admin')) {
            return response()->json([
                'status' => false,
                'data'   => [
                    'message' => $json['message'],
                    'type'    => 'error',
                ],
                'file'   => $e->getFile(),
                'line'   => $e->getLine(),
            ]);
        }
        $json['data'] = [];
        $appKey       = env('APP_DEFAULT_KEY');
        if (method_exists($e, 'getAppKey')) {
            $appKey = $e->getAppKey();
        }
        if ($request->debug != 'yes') {
            $json['RD'] = $this->encrypt(json_encode($json, JSON_INVALID_UTF8_IGNORE), $appKey);
            unset($json['data']);
        } else {
            $json['file'] = $e->getFile();
            $json['line'] = $e->getLine();
        }
        return response()->json(
            $json
        );
    }

    protected function encrypt(string $string, string $appKey): string
    {
        // openssl_encrypt
        return base64_encode(openssl_encrypt($string, 'AES-256-ECB', $appKey, OPENSSL_RAW_DATA));
    }

    protected function unauthenticated(
        $request,
        AuthenticationException $exception
    ): JsonResponse|Response|RedirectResponse {
        return $this->shouldReturnJson($request, $exception)
            ? response()->json([
                'code'    => 401,
                'message' => __('messages.not_login')
            ])
            : redirect()->guest($exception->redirectTo() ?? route('login'));
    }

    protected function invalidJson($request, ValidationException $exception): JsonResponse
    {
        return response()->json([
            'code'    => 422,
            'message' => $exception->getMessage(),
        ]);
    }
}
