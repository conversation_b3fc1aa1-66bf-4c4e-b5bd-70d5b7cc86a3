<?php

namespace App\Http\Controllers\App\Publish;

use App\Http\Controllers\Controller;
use App\Http\Resources\App\Publish\PublishScrollResource;
use App\Models\AiUnifyAsset;
use App\Models\PublishScrollText;
use App\Packages\SystemTools\SystemTool;
use Exception;
use Illuminate\Http\Request;

class ScrollController extends Controller
{
    public function send(Request $request)
    {
        $request->kernel->validate([
            'publish_id' => 'required|integer|exists:ai_unify_assets,id',
            'text'       => 'required|string|max:50',
            'duration'   => 'required|numeric|min:1|max:999'
        ]);
        $text = strip_tags($request->text);
        try {
            SystemTool::words()->sensitiveness($text);
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
        $user    = $request->kernel->user();
        $publish = AiUnifyAsset::find($request->publish_id);
        $log     = $publish->scrollTexts()->create([
            'user_id'  => $user->id,
            'text'     => $text,
            'duration' => $request->duration,
            'status'   => 1,
        ]);
        return $request->kernel->success(new PublishScrollResource($log));
    }

    public function lists(Request $request)
    {
        $request->kernel->validate([
            'publish_id' => 'required|integer|exists:ai_unify_assets,id',
        ]);
        $lists = PublishScrollText::ofEnabled()
            ->where('publish_id', $request->publish_id)
            ->orderBy('duration')
            ->orderByDesc('created_at')
            ->get();
        return $request->kernel->success(PublishScrollResource::collection($lists));
    }

}