<?php

namespace App\Http\Controllers\App;

use App\Http\Controllers\Controller;
use App\Http\Resources\App\Chat\IndexCollection;
use App\Models\AiLogIndex;
use Illuminate\Http\Request;

class ChatController extends Controller
{
    public function index(Request $request)
    {
        $user     = $request->kernel->user();
        $pageSize = $request->pagesize ?: 10;
        $logs     = AiLogIndex::ofUser($user)
            ->orderByDesc('created_at')
            ->paginate($pageSize);
        return $request->kernel->success(new IndexCollection($logs));
    }
}