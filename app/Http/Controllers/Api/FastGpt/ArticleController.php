<?php

namespace App\Http\Controllers\Api\FastGpt;

use App\Http\Controllers\Controller;
use App\Http\Requests\FastGpt\Article\AddKnowledgeArticleRequest;
use App\Http\Requests\FastGpt\Article\AddKnowledgeByNoteRequest;
use App\Http\Requests\FastGpt\Article\ArticleMoveKnowledgeRequest;
use App\Http\Requests\FastGpt\Article\ArticleRemoveKnowledgeRequest;
use App\Http\Requests\FastGpt\Article\DeleteKnowledgeArticleRequest;
use App\Http\Requests\FastGpt\Article\UpdateKnowledgeArticleRequest;
use App\Http\Resources\FastGpt\Article\ArticleAndKnowledgeCollection;
use App\Http\Resources\FastGpt\Article\ArticleBaseResource;
use App\Http\Resources\FastGpt\Article\ArticleCollection;
use App\Http\Resources\FastGpt\Article\ArticleResource;
use App\Http\Resources\FastGpt\KnowledgeSetArticleCollection;
use App\Http\Resources\FastGpt\KnowledgeSetResource;
use App\Models\Enums\FastgptKnowledgeLevelEnum;
use App\Models\Enums\FastgptKnowledgeSetEnum;
use App\Models\FastgptKnowledgeArticle;
use App\Models\FastgptKnowledgeSet;
use App\Models\Note;
use App\Models\User;
use App\Packages\FastGpt\FastGpt;
use App\Traits\FastGptTrait;
use Arr;
use DB;
use Illuminate\Http\Request;
use Modules\Interaction\Models\Browse;
use Modules\User\Http\Resources\UserBaseInfoResource;

class ArticleController extends Controller
{
    use FastGptTrait;

    public function index(Request $request)
    {
        $title        = $request->title;
        $knowledge_id = $request->knowledge_id;
        $orderBy      = $request->order_by ?? 'created_at';
        $user         = $request->kernel->user();

        $notes = FastgptKnowledgeArticle::query()
            ->when($knowledge_id, function ($query) use ($knowledge_id) {
                $query->whereHas('knowledgeSet', function ($query) use ($knowledge_id) {
                    $query->where('knowledge_id', $knowledge_id);
                });
            })
            ->when($title, function ($query, $title) {
                $query->where('title', 'like', "%$title%");
            })
            ->orderByDesc($orderBy)
            ->paginate();
        return $request->kernel->success(new ArticleCollection($notes));
    }

    /**
     * Notes: 添加文档
     *
     * @Author: 玄尘
     * @Date: 2025/3/26 08:44
     * @param  \App\Http\Requests\FastGpt\Article\AddKnowledgeArticleRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function store(AddKnowledgeArticleRequest $request)
    {
        try {
            DB::beginTransaction();
            $knowledge_id = $request->knowledge_id;
            $name         = $request->name;
            $note_ids     = $request->note_ids;
            $details      = $request->details;
            $parent_id    = $request->parent_id ?? null;
            $trainingType = $request->trainingType ?? 'qa';

            $knowledge = '';
            if ($knowledge_id) {
                $knowledge = $this->checkKnowledgeById($knowledge_id);
            }
            $user = $request->kernel->user();

            $article = FastgptKnowledgeArticle::create([
                'user_id' => $user->id,
                'title'   => $name,
                'content' => $details
            ]);

            if ($knowledge) {
                $content = $article->getContent();
                $result  = FastGpt::set()->setText($knowledge->dataset_id, $name, $content, $trainingType);

                $set = $article->knowledgeSet()
                    ->create([
                        'knowledge_id'    => $knowledge->id,
                        'collection_id'   => Arr::get($result, 'data.collectionId', ''),
                        'name'            => $name,
                        'parent_id'       => $parent_id,
                        'type'            => FastgptKnowledgeSetEnum::ARTICLE,
                        'input_data'      => [
                            'name'         => $name,
                            'text'         => $content,
                            'trainingType' => $trainingType,
                        ],
                        'training_amount' => 1,
                        'output_result'   => $result,
                    ]);
            }

            //删除小记
            if ($note_ids) {
                $note_ids = is_string($note_ids) ? explode(',', $note_ids) : $note_ids;
                Note::whereIn('id', $note_ids)->delete();
            }

            DB::commit();
            return $request->kernel->success(new ArticleBaseResource($article));
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 小记转入知识库
     *
     * @Author: 玄尘
     * @Date: 2025/3/26 08:44
     * @param  \App\Http\Requests\FastGpt\Article\AddKnowledgeByNoteRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function moveToKnowledge(AddKnowledgeByNoteRequest $request)
    {
        try {
            DB::beginTransaction();
            $knowledge_id = $request->knowledge_id;
            $note_id      = $request->note_id;
            $name         = $request->name;
            $details      = $request->details;
            $trainingType = $request->trainingType ?? 'qa';
            $user         = $request->kernel->user();

            //生成文档
            $article = FastgptKnowledgeArticle::create([
                'user_id' => $user->id,
                'title'   => $name,
                'content' => $details
            ]);

            $content   = $article->getContent();
            $knowledge = $this->checkKnowledgeById($knowledge_id);

            $this->checkPermissionData($request, $knowledge);
            $result = '';
            if (! empty($content)) {
                $result = FastGpt::set()->setText($knowledge->dataset_id, $name, $content, $trainingType);
            }

            $set = FastgptKnowledgeSet::create([
                'knowledge_id'                 => $knowledge->id,
                'fastgpt_knowledge_article_id' => $article->id,
                'collection_id'                => Arr::get($result, 'data.collectionId', ''),
                'name'                         => $name,
                'type'                         => FastgptKnowledgeSetEnum::ARTICLE,
                'input_data'                   => [
                    'name'         => $name,
                    'text'         => $content,
                    'trainingType' => $trainingType,
                ],
                'training_amount'              => 1,
                'output_result'                => $result,
            ]);

            //删除文档
            Note::where('id', $note_id)->delete();
            DB::commit();
            return $request->kernel->success(new KnowledgeSetResource($set));
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: description
     *
     * @Author: 玄尘
     * @Date: 2025/3/26 08:46
     * @param  \App\Http\Requests\FastGpt\Article\UpdateKnowledgeArticleRequest  $request
     * @return mixed
     * @throws \Throwable
     */

    public function update(UpdateKnowledgeArticleRequest $request)
    {
        try {
            DB::beginTransaction();
            $article_id   = $request->fastgpt_article_id;
            $name         = $request->name;
            $details      = $request->details;
            $trainingType = $request->trainingType ?? 'qa';

            $article      = FastgptKnowledgeArticle::findOrFail($article_id);
            $knowledgeSet = $article->knowledgeSet;

            $article->update([
                'title'   => $name,
                'content' => $details
            ]);

            if ($knowledgeSet) {
                $knowledge = $this->checkKnowledgeById($knowledgeSet->knowledge_id);
                $content   = $article->getContent();
                $result    = FastGpt::set()->deleteSet($knowledgeSet->collection_id);
                $result    = FastGpt::set()->setText($knowledge->dataset_id, $name, $content, $trainingType);

                $set = $knowledgeSet->update([
                    'name'            => $name,
                    'collection_id'   => Arr::get($result, 'data.collectionId', ''),
                    'input_data'      => [
                        'name'         => $name,
                        'text'         => $content,
                        'trainingType' => $trainingType,
                    ],
                    'training_amount' => 1,
                    'output_result'   => $result,
                ]);
            }

            DB::commit();
            return $request->kernel->success('更新成功');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 文档移除知识库
     *
     * @Author: 玄尘
     * @Date: 2025/3/26 08:50
     * @param  \App\Http\Requests\FastGpt\Article\ArticleRemoveKnowledgeRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function removeFromKnowledge(ArticleRemoveKnowledgeRequest $request)
    {
        try {
            DB::beginTransaction();
            $knowledge_set_id = $request->knowledge_set_id;
            $set              = FastgptKnowledgeSet::find($knowledge_set_id);
            $user             = $request->kernel->user();

            if ($set->type->value != FastgptKnowledgeSetEnum::ARTICLE->value) {
                throw new \Exception('该集合不是文档');
            }

            if (! $set->canRemoveKnowledge($user)) {
                throw new \Exception('您没有权限进行此操作');
            }

            $result = FastGpt::set()->deleteSet($set->collection_id);
            $set->delete();
            DB::commit();
            return $request->kernel->success('移除成功');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 文档加入知识库
     *
     * @Author: 玄尘
     * @Date: 2025/4/3 09:27
     * @param  \App\Http\Requests\FastGpt\Article\ArticleMoveKnowledgeRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function moveInKnowledge(ArticleMoveKnowledgeRequest $request)
    {
        try {
            DB::beginTransaction();
            $knowledge_id       = $request->knowledge_id;
            $fastgpt_article_id = $request->fastgpt_article_id;
            $trainingType       = $request->trainingType ?? 'qa';
            $user               = $request->kernel->user();

            //生成文档
            $article = FastgptKnowledgeArticle::find($fastgpt_article_id);
            if ($article->knowledgeSet) {
                throw new \Exception('该文档已存在知识库');
            }

            $content   = $article->getContent();
            $knowledge = $this->checkKnowledgeById($knowledge_id);

            $this->checkPermissionData($request, $knowledge);
            $result = '';
            if (! empty($content)) {
                $result = FastGpt::set()->setText($knowledge->dataset_id, $article->title, $content, $trainingType);
            }

            $set = FastgptKnowledgeSet::create([
                'knowledge_id'                 => $knowledge->id,
                'fastgpt_knowledge_article_id' => $article->id,
                'collection_id'                => Arr::get($result, 'data.collectionId', ''),
                'name'                         => $article->title,
                'type'                         => FastgptKnowledgeSetEnum::ARTICLE,
                'input_data'                   => [
                    'name'         => $article->title,
                    'text'         => $content,
                    'trainingType' => $trainingType,
                ],
                'training_amount'              => 1,
                'output_result'                => $result,
            ]);

            DB::commit();
            return $request->kernel->success(new KnowledgeSetResource($set));
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 删除文档
     *
     * @Author: 玄尘
     * @Date: 2025/3/26 09:31
     * @param  \App\Http\Requests\FastGpt\Article\DeleteKnowledgeArticleRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function delete(DeleteKnowledgeArticleRequest $request)
    {
        try {
            DB::beginTransaction();
            $article_id = $request->fastgpt_article_id;
            $article    = FastgptKnowledgeArticle::find($article_id);
            $user       = $request->kernel->user();
            if (! $article->canDelete($user)) {
                throw new \Exception('没有权限');
            }

            //删除知识库合集
            $knowledgeSet = $article->knowledgeSet;
            if ($knowledgeSet) {
                if ($knowledgeSet->collection_id) {
                    $result = FastGpt::set()->deleteSet($knowledgeSet->collection_id);
                }
                $knowledgeSet->delete();
            }

            $article->delete();
            DB::commit();
            return $request->kernel->success('删除成功');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 文档详情
     *
     * @Author: 玄尘
     * @Date: 2025/3/26 13:19
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */

    public function show(Request $request)
    {
        $request->kernel->validate([
            'fastgpt_article_id' => 'required|exists:fastgpt_knowledge_articles,id',
        ], [
            'fastgpt_article_id.required' => '请传入文档id',
            'fastgpt_article_id.exists'   => '文档不存在',
        ]);

        $info = FastgptKnowledgeArticle::find($request->fastgpt_article_id);
        //增加浏览量
        $info->increment('browse_count');
        Browse::create([
            'user_id'        => $request->kernel->id(),
            'browsable_type' => $info->getMorphClass(),
            'browsable_id'   => $info->id,
        ]);
        return $request->kernel->success(new ArticleResource($info));
    }

    /**
     * Notes: 公开文档
     *
     * @Author: 玄尘
     * @Date: 2025/3/26 10:00
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function allOpenArticleByKnowledge(Request $request)
    {
        $type      = $request->type ?? '';//edit  browse  favorite
        $name      = $request->name;
        $author_id = $request->author_id ?? '';

        $user  = $request->kernel->user();
        $lists = FastgptKnowledgeSet::query()
            ->whereHas('knowledge', function ($query) {
                $query->where('level', FastgptKnowledgeLevelEnum::ALL->value);
            })
            ->with(['knowledge', 'note', 'note.user', 'note.user.info'])
            ->whereHas('knowledgeArticle', function ($query) use ($type, $name, $user, $author_id) {
                $query->when($author_id, function ($query) use ($author_id) {
                    $query->where('user_id', $author_id);
                })->when($name, function ($query) use ($name) {
                    $query->where('title', 'like', "%{$name}%");
                })->when(in_array($type, ['edit', 'edite']), function ($query) use ($user) {
                    $query->where('user_id', $user->id)->orderBy('updated_at', 'desc');
                })->when($type == 'browse', function ($query) {
                    $query->whereHas('browsable', function ($query) {
                        $query->orderBy('created_at', 'desc');
                    });
                })->when($type == 'favorite', function ($query) {
                    $query->whereHas('favoriteable', function ($query) {
                        $query->orderBy('created_at', 'desc');
                    });
                });
            })
            ->paginate();

        return $request->kernel->success(new KnowledgeSetArticleCollection($lists));
    }

    /**
     * Notes: 所有公开知识库文档
     *
     * @Author: 玄尘
     * @Date: 2025/3/31 17:08
     */
    public function getPublicArticles(Request $request)
    {
        $name      = $request->name;
        $author_id = $request->author_id ?? '';

        $lists = FastgptKnowledgeArticle::query()
            ->whereHas('knowledgeSet.knowledge', function ($query) {
                $query->where('level', FastgptKnowledgeLevelEnum::ALL->value);
            })
            ->when($author_id, function ($query) use ($author_id) {
                $query->where('user_id', $author_id);
            })
            ->when($name, function ($query) use ($name) {
                $query->where('title', 'like', "%{$name}%");
            })
            ->with(['knowledgeSet', 'knowledgeSet.knowledge', 'user', 'user.info'])
            ->orderBy('created_at', 'desc')
            ->paginate();

        return $request->kernel->success(new ArticleAndKnowledgeCollection($lists));
    }

    /**
     * Notes: 所有公开的文档和自己私有的文档
     *
     * @Author: 玄尘
     * @Date: 2025/3/31 16:45
     */
    public function allOpenArticleAndMy(Request $request)
    {
        $type      = $request->type ?? '';//edit  browse  favorite
        $name      = $request->name;
        $author_id = $request->author_id ?? '';
        $user      = $request->kernel->user();
        if (! $author_id) {
            $author_id = $user->id;
        }

        //我自己的文档和公开知识库的文档
        $lists = FastgptKnowledgeArticle::query()
            ->where(function ($query) use ($author_id) {
                $query->where('user_id', $author_id)
                    ->orWhereHas('knowledgeSet.knowledge', function ($query) {
                        $query->where('level', FastgptKnowledgeLevelEnum::ALL->value);
                    });
            })
            ->when($name, function ($query) use ($name) {
                $query->where('title', 'like', "%{$name}%");
            })
            ->when($type == 'browse', function ($query) use ($user) {
                $query->whereHas('browsable', function ($query) use ($user) {
                    $query->where('user_id', $user->id)
                        ->orderBy('created_at', 'desc');
                });
            })
            ->when($type == 'favorite', function ($query) use ($user) {
                $query->whereHas('favoriteable', function ($query) use ($user) {
                    $query->where('user_id', $user->id)
                        ->orderBy('created_at', 'desc');
                });
            })
            ->when(in_array($type, ['edit', 'edite']), function ($query) use ($user) {
                $query->where('user_id', $user->id)
                    ->orderBy('updated_at', 'desc');
            })
            ->with(['knowledgeSet', 'knowledgeSet.knowledge', 'user', 'user.info'])
            ->orderBy('created_at', 'desc')
            ->paginate();

        return $request->kernel->success(new ArticleAndKnowledgeCollection($lists));
    }

    /**
     * Notes: 我浏览和搜藏过的文档的作者
     *
     * @Author: 玄尘
     * @Date: 2025/3/26 10:29
     */
    public function authors(Request $request)
    {
        $user = $request->kernel->user();

        $authorIds = FastgptKnowledgeArticle::query()
            ->where(function ($query) use ($user) {
                $query->whereHas('browsable', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                })->orWhereHas('favoriteable', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                });
            })
            ->whereHas('knowledgeSet')
            ->select('user_id')
            ->distinct()
            ->pluck('user_id');

        $users = User::whereIn('id', $authorIds)->get();
        return $request->kernel->success(UserBaseInfoResource::collection($users));
    }

    //修改标题
    public function updateTitle(Request $request)
    {
        $request->kernel->validate([
            'fastgpt_article_id' => 'required|exists:fastgpt_knowledge_articles,id',
            'title'              => 'required|string',
        ], [
            'fastgpt_article_id.required' => '缺少文档id',
            'fastgpt_article_id.exists'   => '文档不存在',
            'title.required'              => '请输入标题',
        ]);
        try {
            DB::beginTransaction();
            $article = FastgptKnowledgeArticle::find($request->fastgpt_article_id);
            $article->update([
                'title' => $request->title,
            ]);
            $set = $article->knowledgeSet;
            if ($set) {
                $set->update([
                    'name' => $request->title,
                ]);
                if ($set->collection_id) {
                    $data   = [
                        'id'   => $set->collection_id,
                        'name' => $set->name,
                    ];
                    $result = FastGpt::set()->setUpdate($data);
                }
            }

            DB::commit();
            return $request->kernel->success('修改成功');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }
}
