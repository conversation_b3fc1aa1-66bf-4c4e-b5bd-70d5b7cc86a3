<?php

namespace App\Http\Controllers\Api\FastGpt;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Resources\FastGpt\DataPackageCollection;
use App\Models\FastgptData;
use App\Models\FastgptKnowledgeSet;
use App\Packages\FastGpt\BaseClient;
use App\Packages\FastGpt\FastGpt;
use App\Traits\FastGptTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class DataController extends ApiController
{
    use FastGptTrait;

    public function list(Request $request)
    {
        $request->kernel->validate([
            'collection_id' => 'required',
        ], [
            'collection_id.required' => '缺少集合id',
        ]);

        $collectionId = $request->input('collection_id');

        $page     = $request->input('page', 1);
        $pageSize = $request->input('pagesize', 10);
        $content  = $request->input('content', '');

        $list = FastGpt::data()->list($collectionId, $content, $page, $pageSize);

        if (BaseClient::isNew()) {
            $items       = Arr::get($list, 'data.list');
            $total       = Arr::get($list, 'data.total');
            $perPage     = $pageSize;
            $currentPage = $page;
        } else {
            $items       = Arr::get($list, 'data.data');
            $total       = Arr::get($list, 'data.total');
            $perPage     = Arr::get($list, 'data.pageSize');
            $currentPage = Arr::get($list, 'data.pageNum');
        }
        $data = $this->getPaginate($items, $currentPage, $perPage, $total);
        return $request->kernel->success(new DataPackageCollection($data));
    }

    public function create(Request $request)
    {
        $request->kernel->validate([
            'collection_id' => 'required',
            'training_mode' => 'required',
            'q'             => 'required',
        ], [
            'collection_id.required' => '缺少集合id',
            'training_mode.required' => '缺少训练模式',
            'q.required'             => '缺少主要数据',
        ]);

        $user          = $request->kernel->user();
        $collectionId  = $request->input('collection_id');
        $training_mode = $request->input('training_mode');
        $q             = $request->input('q');
        $a             = $request->input('a');
        $data_id       = null;

        $set = FastgptKnowledgeSet::where('collection_id', $collectionId)->first();
        if (! $set) {
            throw new ValidatorException('集合不存在');
        }

        $this->checkPermissionData($request, $set->knowledge);

        $result = FastGpt::data()->create($collectionId, $training_mode, $q, $a);
        if ($result['data']['insertLen'] != 1) {
            throw new ValidatorException('添加失败');
        }

        $list = FastGpt::data()->list($collectionId, $q, 1, 10);
        if ($list['code'] == 200) {
            $data = Arr::get($list, 'data.data');

            if (BaseClient::isNew()) {
                $data = Arr::get($list, 'data.list');
            }

            foreach ($data as $item) {
                $data_id = $item['_id'];
                FastgptData::updateOrCreate([
                    'user_id'       => $user->id,
                    'data_id'       => $data_id,
                    'collection_id' => $set->id,

                ], [
                    'training_mode' => $training_mode,
                    'q'             => $q,
                    'a'             => $a,
                ]);
            }
        } else {
            throw new ValidatorException('添加失败');
        }

        return $request->kernel->success('添加成功');
    }

    public function detail(Request $request)
    {
        $request->kernel->validate([
            'data_id' => 'required',
        ], [
            'data_id.required' => '缺少集合id',
        ]);
        $dataId = $request->input('data_id');
        $result = FastGpt::data()->detail($dataId);

        return $request->kernel->success($result['data']);
    }

    /**
     * Notes: 修改数据
     *
     * @Author: 玄尘
     * @Date: 2025/4/1 08:55
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function update(Request $request)
    {
        $request->kernel->validate([
            'data_id' => 'required',
            'q'       => 'required',
            'a'       => 'required',
        ], [
            'data_id.required' => '缺少数据id',
            'q.required'       => '缺少主要数据',
        ]);
        $dataId = $request->input('data_id');
        $q      = $request->input('q');
        $a      = $request->input('a');

        if (! $q && ! $a) {
            throw new ValidatorException('缺少数据');
        }

        $res = FastGpt::data()->update($dataId, $q, $a);

        $fastgptData = FastgptData::where('data_id', $dataId)->first();
        if ($fastgptData) {
            $fastgptData->q = $q;
            if ($a) {
                $fastgptData->a = $a;
            }
            $fastgptData->save();
        }

        return $request->kernel->success('修改成功');
    }

    public function delete(Request $request)
    {
        $request->kernel->validate([
            'data_id' => 'required',
        ], [
            'data_id.required' => '缺少数据id',
            'q.required'       => '缺少主要数据',
        ]);
        $dataId = $request->input('data_id');

        $res = FastGpt::data()->dataDelete($dataId);

        return $request->kernel->success('删除成功');
    }

}
