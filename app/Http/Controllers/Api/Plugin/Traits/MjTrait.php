<?php

namespace App\Http\Controllers\Api\Plugin\Traits;

use App\Exceptions\ValidatorException;
use App\Models\PluginMjDraw;
use Illuminate\Http\Request;

trait MjTrait
{

    protected function passImage(
        string $url,
        array $mimes = [
            'image/png',
            'image/jpg',
            'image/jpeg',
        ]
    ) {
        $size = getimagesize($url);
        return (! $size)
            || ! ($size['mime'] ?? false)
            || ! in_array($size['mime'], $mimes);
    }

    protected function getSeed()
    {
        $t                    = pow(10, 4 - 1);
        $r                    = pow(10, 4) - 1;
        $randomNumber         = rand($t, $r);
        $timestampLast9Digits = substr((string) time(), -9);
        return (string) ($randomNumber.$timestampLast9Digits);
    }

    protected function createTask(array $data, Request $request)
    {
        $data['is_asset'] = $request->is_asset ? true : false;
        $log              = PluginMjDraw::create($data);
        if ($log) {
            return $request->kernel->success($log->getAssetResource(), 'success');
        } else {
            return $request->kernel->error('创建任务失败');
        }
    }

    protected function getConfigFormJsonFile(string $type): array
    {
        $filePath = base_path("public/ai_style/".$type.".json");
        if (! file_exists($filePath)) {
            throw new ValidatorException('配置文件不存在');
        }
        $file = fopen($filePath, 'r');
        return json_decode(fread($file, filesize($filePath)), true);
    }
}