<?php

namespace App\Http\Controllers\Api\Plugin\Traits;

use App\Models\PluginJmDraw;
use Illuminate\Http\Request;

trait JmTrait
{
    protected function createTask(array $data, Request $request)
    {
        $data['is_asset'] = $request->is_asset ? true : false;
        $log              = PluginJmDraw::create($data);
        if ($log) {
            return $request->kernel->success($log->getAssetResource(), 'success');
        } else {
            return $request->kernel->error('创建任务失败');
        }
    }
}