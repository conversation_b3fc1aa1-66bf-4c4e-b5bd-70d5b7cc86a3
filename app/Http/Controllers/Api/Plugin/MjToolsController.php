<?php

namespace App\Http\Controllers\Api\Plugin;

use App\Http\Controllers\Api\Plugin\Traits\MjTrait;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class MjToolsController extends Controller
{
    use MjTrait;

    public function comfyDrawStyle(Request $request)
    {
        $request->kernel->validate([
            'key' => 'required',
        ], [
            'key.required' => '参数异常',
        ]);
        $key  = $request->key;
        $data = Cache::get('mj_style_drawStyle_'.$key, []);
        return $request->kernel->success($data);
    }

    public function qrcodeStyle(Request $request)
    {
        $styles = $this->getConfigFormJsonFile('qrcode');
        return $request->kernel->success($styles);
    }

    public function styleTransfer(Request $request)
    {
        $styles = $this->getConfigFormJsonFile('transfer');
        return $request->kernel->success($styles);
    }

    public function createQrCode(Request $request)
    {
        $text = $request->text ?: '';
        if (blank($text)) {
            return $request->kernel->error('请输入要转换的文本内容');
        }
        $path   = sprintf('mj_qrcode/%s/%s/%s.png', date('Y'), date('m'), Str::uuid()->toString());
        $qrcode = QrCode::format('png')
            ->encoding('UTF-8')
            ->size(1024)
            ->margin(3)
            ->generate($text);
        Storage::put($path, $qrcode);
        return $request->kernel->success([
            'qrcode' => Storage::url($path),
        ]);
    }

    public function mirrorStyle(Request $request)
    {
        $style = $this->getConfigFormJsonFile('mirror');
        return $request->kernel->success($style);
    }

    public function captureStyle(Request $request)
    {
        $style = $this->getConfigFormJsonFile('capture');
        return $request->kernel->success($style);
    }
}