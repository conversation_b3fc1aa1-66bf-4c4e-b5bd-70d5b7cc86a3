<?php

namespace App\Http\Controllers\Api\Plugin;

use App\Http\Controllers\Api\Plugin\Traits\AiPPTConfigTrait;
use App\Http\Controllers\Api\Plugin\Traits\AiPPTTrait;
use App\Http\Controllers\Controller;
use App\Http\Resources\Plugin\AiPPT\AiPPTTaskCollection;
use App\Http\Resources\Plugin\AiPPT\AiPPtTaskResource;
use App\Models\PluginAiPptOutline;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\Rule;

class AiPPTController extends Controller
{
    use AiPPTTrait, AiPPTConfigTrait;

    public function lists(Request $request)
    {
        $user  = $request->kernel->user();
        $lists = PluginAiPptOutline::ofUser($user)
            ->orderByDesc('created_at')
            ->paginate($request->pagesize ?: 10);
        return $request->kernel->success(new AiPPTTaskCollection($lists));
    }

    public function detail(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required|exists:plugin_ai_ppt_outlines,id',
        ], [
            'id.required' => 'id必须填写',
            'id.exists'   => 'id不存在',
        ]);
        $task = PluginAiPptOutline::find($request->id);
        return $request->kernel->success((new AiPPtTaskResource($task))->additional([
            'show_outline' => true,
            'show_content' => true,
        ]));
    }

    public function Outline(Request $request)
    {
        $audience = $this->getConfig('audience');
        $scene    = $this->getConfig('scene');
        $tone     = $this->getConfig('tone');
        $language = $this->getConfig('language');
        $request->kernel->validate([
            'title'    => 'required',
            'audience' => ['required', Rule::in($audience)],
            'scene'    => ['required', Rule::in($scene)],
            'tone'     => ['required', Rule::in($tone)],
            'language' => ['required', Rule::in($language)],
        ], [
            'title.required'    => '描述内容必须填写',
            'audience.required' => '请选择受众',
            'audience.in'       => '受众内容错误',
            'scene.required'    => '请选择场景',
            'scene.in'          => '场景内容错误',
            'tone.required'     => '请选择语气',
            'tone.in'           => '语气内容错误',
            'language.required' => '请选择语言',
            'language.in'       => '语言内容错误',
        ]);

        $message = [
            "title"    => $request->title,
            "fileUrl"  => $request->file_url,
            "audience" => $request->audience,
            "scene"    => $request->scene,
            "tone"     => $request->tone,
            "language" => $request->language,
        ];
        $data    = [
            'model'    => $this->getConfig('outline_model'),
            'messages' => [
                ['role' => 'user', 'content' => json_encode($message, JSON_UNESCAPED_UNICODE)],
            ],
        ];
        try {
            $data = $this->chatOutline($data);
            $user = $request->kernel->user();
            $task = PluginAiPptOutline::create([
                'user_id'  => $user->id,
                'title'    => $request->title,
                'audience' => $request->audience,
                'scene'    => $request->scene,
                'tone'     => $request->tone,
                'language' => $request->language,
                'outline'  => $data,
                'file_url' => $request->file_url,
                'content'  => '',
                'status'   => PluginAiPptOutline::STATUS_OUTLINE,
            ]);
            return $request->kernel->success((new AiPPtTaskResource($task))->additional([
                'show_outline' => true,
                'show_content' => true,
            ]));
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function genContent(Request $request)
    {
        config([
            'app.debug' => false,
        ]);
        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);
        try {
            $request->kernel->validate([
                'id'          => 'required|exists:plugin_ai_ppt_outlines,id',
                'template_id' => 'required',
            ], [
                'id.required'          => 'id必须填写',
                'id.exists'            => 'id不存在',
                'template_id.required' => '请选择PPT模板',
            ]);
            $task                  = PluginAiPptOutline::find($request->id);
            $outline               = $task->outline;
            $outline['templateId'] = $request->template_id;
            $outline['pptId']      = $task->id;
            $outline['audience']   = $task->audience;
            $outline['scene']      = $task->scene;
            $outline['tone']       = $task->tone;
            $outline['language']   = $task->language;
            $result                = $this->chatOutline([
                'model'    => $this->getConfig('content_model'),
                'messages' => [
                    [
                        'role'    => 'user',
                        'content' => json_encode($outline, JSON_UNESCAPED_UNICODE)
                    ],
                ],
            ]);
            $pages                 = $result['data'];
            $task->update([
                'status'   => PluginAiPptOutline::STATUS_SUCCESS,
                'content'  => $pages,
                'file_url' => $result['fileUrl']
            ]);
            $task->refresh();
            return $request->kernel->success((new AiPPtTaskResource($task))->additional([
                'show_outline' => true,
                'show_content' => true,
            ]));
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
        }
    }

    public function ShareDetail(Request $request)
    {
        $request->kernel->validate([
            'code' => 'required',
        ], [
            'code.required' => '分享码必须填写',
        ]);
        $code = Cache::get('ppt_'.$request->code, '');
        if (blank($code)) {
            return $request->kernel->error('分享内容以过期或分享码错误');
        }
        $data = decrypt($code);
        $task = PluginAiPptOutline::find($data['id'] ?? 0);
        if (! $task) {
            return $request->kernel->error('分享内容不存在');
        }
        return $request->kernel->success((new AiPPtTaskResource($task))->additional([
            'show_outline' => true,
            'show_content' => true,
        ]));
    }

    public function share(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required',
        ], [
            'id.required' => 'id必须填写',
        ]);
        $task = PluginAiPptOutline::find($request->id);
        $user = $request->kernel->user();
        if ($task && $task->user_id != $user->id) {
            return $request->kernel->error('您没有权限发起分享');
        }
        $expire_at = now()->addMinutes(60);
        $data      = [
            'id'        => $request->id,
            'expire_at' => $expire_at,
            'user_id'   => $task->user_id,
        ];
        $code      = encrypt($data);
        $md5       = md5($code);
        Cache::put('ppt_'.$md5, $code, 3600);
        return $request->kernel->success([
            'code' => $md5,
            'msg'  => '分享链接有效时长60分钟，截止至'.$expire_at->toDateTimeString(),
        ]);
    }

    public function deletes(Request $request)
    {
        $request->kernel->validate([
            'ids' => 'required',
        ], [
            'ids.required' => 'id必须填写',
        ]);
        $ids = array_filter(explode(',', $request->ids));
        if (count($ids) > 0) {
            $user = $request->kernel->user();
            PluginAiPptOutline::ofUser($user)->whereIn('id', $ids)->delete();
            return $request->kernel->success('删除成功');
        } else {
            return $request->kernel->error('请选择要删除的PPT');
        }
    }

    public function editOutLine(Request $request)
    {
        $request->kernel->validate([
            'id'      => 'required|exists:plugin_ai_ppt_outlines,id',
            'outline' => 'required',
        ], [
            'id.required'      => 'id必须填写',
            'id.exists'        => 'id不存在',
            'outline.required' => '请输入大纲内容',
        ]);
        $task = PluginAiPptOutline::find($request->id);
        if ($task->user_id != $request->kernel->id()) {
            return $request->kernel->error('您没有权限修改此PPT');
        }
        $task->outline = json_decode(htmlspecialchars_decode($request->outline), true);
        if ($task->save()) {
            return $request->kernel->success([], '修改成功');
        } else {
            return $request->kernel->error('修改失败');
        }
    }

    public function editContent(Request $request)
    {
        $request->kernel->validate([
            'id'      => 'required|exists:plugin_ai_ppt_outlines,id',
            'content' => 'required',
        ], [
            'id.required'      => 'id必须填写',
            'id.exists'        => 'id不存在',
            'content.required' => '内容不存在',
        ]);
        $task = PluginAiPptOutline::find($request->id);
        if ($task->user_id != $request->kernel->id()) {
            return $request->kernel->error('您没有权限修改此PPT');
        }
        $task->content = json_decode(htmlspecialchars_decode($request->post('content')), true);
        if ($task->save()) {
            return $request->kernel->success([], '修改成功');
        } else {
            return $request->kernel->error('修改失败');
        }
    }
}