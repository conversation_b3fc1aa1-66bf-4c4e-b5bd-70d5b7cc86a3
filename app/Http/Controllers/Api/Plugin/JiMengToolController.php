<?php

namespace App\Http\Controllers\Api\Plugin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Plugin\Jm\CheckTaskResource;
use App\Models\PluginJmCheckResource;
use App\Models\PluginJmDraw;
use App\Packages\AlibabaCloud\Alibaba;
use App\Packages\JiMeng\JiMeng;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class JiMengToolController extends Controller
{

    public function getModel(Request $request)
    {
        $request->kernel->validate([
            'type' => [
                'required', Rule::in([
                    'image',
                    'video'
                ])
            ],
        ]);
        $type = $request->type;
        return $request->kernel->success(PluginJmDraw::ModelType[$type]);
    }

    public function getPanelInfo(Request $request)
    {
        $result = JiMeng::tools()->getPanelInfo();
        if ($result->isSuccess()) {
            return $request->kernel->success($result->toArray());
        } else {
            return $request->kernel->error($result->getMessage());
        }
    }

    public function getFeed(Request $request)
    {
        $request->kernel->validate([
            'key' => 'required',
        ]);
        $result = JiMeng::tools()->getFeed($request->key);
        if ($result->isSuccess()) {
            return $request->kernel->success($result->toArray());
        } else {
            return $request->kernel->error($result->getMessage());
        }
    }

    public function genTTS(Request $request)
    {
        $request->kernel->validate([
            'vcn'   => 'required',
            'speed' => 'required|numeric|min:0.8|max:2',
        ], [
            'vcn.required'   => '请选择音色',
            'speed.required' => '请输入语速',
            'speed.min'      => '语速不能小于:min',
            'speed.max'      => '语速不能大于:max'
        ]);
        $no       = $request->no ?: Str::uuid();
        $fileName = $no.'.mp3';
        $ossPath  = 'temp/tts/'.$fileName;
        $text     = $request->text ?: '你好我是瓦特AI，欢迎使用对口型功能';
        try {
            Alibaba::bailian()->tts()->tts($request->vcn, $text, $request->speed, $fileName);
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }

        return $request->kernel->success([
            'url'  => Storage::url($ossPath),
            'no'   => $no,
            'vcn'  => $request->vcn,
            'text' => $text
        ]);
    }

    public function checkResource(Request $request)
    {
        $request->kernel->validate([
            'fileurl' => 'required|url',
        ]);
        $fileUrl   = $request->fileurl;
        $extension = Str::before(pathinfo($fileUrl, PATHINFO_EXTENSION), '?');
        if (! in_array($extension, ['jpg', 'jpeg', 'png', 'bmp', 'webp', 'mp4', 'avi', 'mov'])) {
            return $request->kernel->error('不支持的文件类型'.$fileUrl);
        }
        $type = match ($extension) {
            'jpg', 'jpeg', 'png', 'bmp', 'webp' => 'image',
            'mp4', 'avi', 'mov' => 'video',
            default => ''
        };
        if ($type == 'image') {
            $fileUrl   = $fileUrl.'?x-oss-process=image/resize,l_2048,s_512,limit_0';
            $imageInfo = getimagesize($fileUrl);
            if ($imageInfo === false) {
                return $request->kernel->error('获取图片大小失败');
            }
            list($width, $height) = $imageInfo;
            $ratio = (float) bcdiv(max($width, $height), min($width, $height), 2);
            if ($ratio > 2) {
                return $request->kernel->error('图片宽高比需小于等于2');
            }
        }
        $task = PluginJmCheckResource::updateOrCreate([
            'fileurl' => $fileUrl,
        ], [
            'type'   => $type,
            'status' => PluginJmCheckResource::STATUS_INIT,
        ]);

        if ($type == 'video') {
            $ext      = $task->getFFExt($fileUrl, $task->no);
            $duration = (float) $ext->get('duration');
            $width    = (int) $ext->get('width');
            $height   = (int) $ext->get('height');
            $fps      = (int) $ext->get('r_frame_rate');
            $error    = false;
            $message  = '';
            if ($duration < 2 || $duration > 120) {
                $error   = true;
                $message .= '视频时长需在2-120秒之间,当前时长为:'.$duration."\n";
            }
            if ($fps < 15 || $fps > 60) {
                $error   = true;
                $message .= '视频帧率需在15-60帧之间,当前帧率为:'.$fps."\n";
            }
            if ($width < 640 || $width > 2048 || $height < 640 || $height > 2048) {
                $error   = true;
                $message .= '视频宽高需在640-2048之间,当前宽高为:'.$width.'x'.$height."\n";
            }

            if ($error) {
                $task->update([
                    'status'  => PluginJmCheckResource::STATUS_ERROR,
                    'message' => $message,
                ]);
            } else {
                $task->update([
                    'status'   => PluginJmCheckResource::STATUS_SUCCESS,
                    'resource' => [
                        'duration' => $duration,
                        'width'    => $width,
                        'height'   => $height,
                        'fps'      => $fps,
                    ],
                ]);
            }
            return $request->kernel->success(new CheckTaskResource($task));
        }
        try {
            $result = Alibaba::bailian()->livePortrait()->detect($fileUrl);
            if ($result['output']['check_pass']) {
                $task->update([
                    'status'   => PluginJmCheckResource::STATUS_SUCCESS,
                    'resource' => [
                        'face_bbox' => $result['output']['face_bbox'],
                        'ext_bbox'  => $result['output']['ext_bbox'],
                    ],
                    'message'  => '检验通过',
                ]);
            } else {
                $task->update([
                    'status'  => PluginJmCheckResource::STATUS_ERROR,
                    'message' => Alibaba::aichat()->aiChat()->mt(
                        text: $result['output']['message'],
                        domains: '这是一个后端API开发场景，主要讲英文错误信息翻译成中文',
                    ),
                ]);
            }
            return $request->kernel->success(new CheckTaskResource($task));
        } catch (Exception $exception) {
            $task->update([
                'status'  => PluginJmCheckResource::STATUS_ERROR,
                'message' => Alibaba::aichat()->aiChat()->mt(
                    text: $exception->getMessage(),
                    domains: '这是一个后端API开发场景，主要讲英文错误信息翻译成中文',
                ),
            ]);
            return $request->kernel->success(new CheckTaskResource($task));
        }
    }

    public function checkQuery(Request $request)
    {
        $request->kernel->validate([
            'no' => 'required|exists:plugin_jm_check_resources,no',
        ]);
        $task = PluginJmCheckResource::where('no', $request->no)->first();
        return $request->kernel->success(new CheckTaskResource($task));
    }

    public function imageSize(Request $request)
    {
        $style = $request->kernel->getConfigFormJsonFile('jm_image_size');
        return $request->kernel->success($style);
    }
}