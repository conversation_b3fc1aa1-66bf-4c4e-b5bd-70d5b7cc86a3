<?php

namespace App\Http\Controllers\Api\Plugin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Plugin\Keling\ListCollection;
use App\Http\Resources\Plugin\Keling\ModelListCollection;
use App\Models\PluginKeLing;
use App\Packages\Plugin\Plugin;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class KelingImageController extends Controller
{
    public function options(Request $request)
    {
        $result = Plugin::Keling()->imageOptions();
        if ($result->isSuccess()) {
            return $request->kernel->success($result->toArray());
        } else {
            return $request->kernel->error($result->getMessage());
        }
    }

    public function modelCreate(Request $request)
    {
        $request->kernel->validate([
            'prompt'       => 'required',
            'aspect_ratio' => 'required',
            'gender'       => 'required',
            'age'          => 'required',
            'skin'         => 'required',
            'type'         => 'required',
        ], [
            'prompt.required'       => '请输入描述文字',
            'aspect_ratio.required' => '请选择比例',
            'gender.required'       => '请选择性别',
            'age.required'          => '请选择年龄',
            'skin.required'         => '请选择肤色',
            'type.required'         => '请传入模型',
        ]);
        $params = [
            'type'      => $request->type,
            'inputs'    => [],
            'arguments' => [
                ['name' => 'prompt', 'value' => $request->prompt],
                ['name' => 'aspect_ratio', 'value' => $request->aspect_ratio],
                ['name' => 'imageCount', 'value' => 1],
                [
                    'name'  => 'modelOptions',
                    'value' => json_encode([
                        'gender'     => $request->gender,
                        'age'        => $request->age,
                        'skin-color' => $request->skin,
                    ]),
                ],
                ['name' => 'biz', 'value' => 'klingai'],
            ]
        ];
        $user   = $request->kernel->user();
        $task   = PluginKeLing::create([
            'channel' => PluginKeLing::IMAGE,
            'user_id' => $user->id,
            'prompt'  => $request->prompt,
            'type'    => $request->type,
            'params'  => $params,
        ]);
        if ($task) {
            return $request->kernel->success($task->getAssetResource(), 'success');
        } else {
            return $request->kernel->error('创建失败');
        }
    }

    public function modelList(Request $request)
    {
        $user  = $request->kernel->user();
        $lists = PluginKeLing::ofUser($user)
            ->where('channel', PluginKeLing::IMAGE)
            ->where('type', 'mmu_txt2img_model')
            ->orderByDesc('created_at')
            ->paginate($request->pageSize ?? 10);
        return $request->kernel->success(new ModelListCollection($lists), 'success');
    }

    public function recoClothes(Request $request)
    {
        $result = Plugin::Keling()->recoClothes();
        if ($result->isSuccess()) {
            return $request->kernel->success($result->toArray());
        } else {
            return $request->kernel->error($result->getMessage());
        }
    }

    public function recoModel(Request $request)
    {
        $result = Plugin::Keling()->recoModel();
        if ($result->isSuccess()) {
            return $request->kernel->success($result->toArray());
        } else {
            return $request->kernel->error($result->getMessage());
        }
    }

    public function uploadModel(Request $request)
    {
        $request->kernel->validate([
            'image' => 'required|url',
        ], [
            'image.required' => '请上传图片',
            'image.url'      => '图片格式错误',
        ]);
        $reslut = Plugin::Keling()->uploadModel($request->image);
        if ($reslut->isSuccess()) {
            if (in_array($reslut->tryOnUploadStatus, [50, 99])) {
                return $request->kernel->success($reslut->toArray()['results'][0]);
            } else {
                return $request->kernel->error($reslut->quality);
            }
        } else {
            return $request->kernel->error($reslut->getMessage());
        }
    }

    public function uploadClothes(Request $request)
    {
        $request->kernel->validate([
            'image' => 'required|url',
        ], [
            'image.required' => '请上传图片',
            'image.url'      => '图片格式错误',
        ]);
        $reslut = Plugin::Keling()->uploadClothes($request->image);
        if ($reslut->isSuccess()) {
            if ($reslut->tryOnUploadStatus == 99) {
                $data  = collect($reslut->results);
                $upper = $data->firstWhere('clothesType', 'UPPER');
                $lower = $data->firstWhere('clothesType', 'LOWER');
                return $request->kernel->success([
                    'upper' => $upper,
                    'lower' => $lower,
                ]);
            } else {
                return $request->kernel->error($reslut->quality);
            }
        } else {
            return $request->kernel->error($reslut->getMessage());
        }
    }

    public function aitryon(Request $request)
    {
        $request->kernel->validate([
            'person_type' => 'required',
            'image_type'  => 'required',
            'human'       => 'required|url',
            'upper'       => 'required_without:lower|nullable|url',
            'lower'       => 'required_without:upper|nullable|url',
        ], [
            'person_type.required'   => '请上传person_type',
            'human.required'         => '请上传人物图片',
            'human.url'              => '人物图片格式不正确',
            'upper.required_without' => '必须上传至少一件衣服',
            'upper.url'              => '服装图片格式不正确',
            'lower.required_without' => '必须上传至少一件衣服',
            'lower.url'              => '下装图片格式不正确',
        ]);
        $inputs = [];
        if ($request->upper) {
            $inputs[] = [
                'name'      => 'upperInput',
                'inputType' => 'URL',
                'url'       => $request->upper,
            ];
        }
        if ($request->lower) {
            $inputs[] = [
                'name'      => 'lowerInput',
                'inputType' => 'URL',
                'url'       => $request->lower,
            ];
        }
        $inputs[]  = [
            'name'      => 'humanImage',
            'inputType' => 'URL',
            'url'       => $request->human,
        ];
        $imageType = Str::afterLast($request->image_type, '/');

        $params = [
            'type'      => $request->type,
            'inputs'    => $inputs,
            'arguments' => [
                ['name' => 'personType', 'value' => $request->person_type],
                ['name' => '__modelImageType', 'value' => $imageType],
                ['name' => 'imageCount', 'value' => '1'],
                ['name' => 'biz', 'value' => 'klingai'],
            ]
        ];

        $user = $request->kernel->user();
        $task = PluginKeLing::create([
            'channel' => PluginKeLing::IMAGE,
            'user_id' => $user->id,
            'prompt'  => '',
            'type'    => $request->type,
            'params'  => $params,
        ]);
        if ($task) {
            return $request->kernel->success($task->getAssetResource(), 'success');
        } else {
            return $request->kernel->error('创建失败');
        }
    }

    public function lists(Request $request)
    {
        $user  = $request->kernel->user();
        $lists = PluginKeLing::ofUser($user)
            ->where('channel', PluginKeLing::IMAGE)
            ->orderByDesc('created_at')
            ->paginate($request->pageSize ?? 10);

        return $request->kernel->success(new ListCollection($lists), 'success');
    }

}