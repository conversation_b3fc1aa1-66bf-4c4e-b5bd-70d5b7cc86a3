<?php

namespace App\Http\Controllers\Api\Knowledge;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Models\Assistant;
use App\Models\GenImage;
use App\Models\TokenLog;
use App\Packages\Knowledge\Knowledge as KnowledgeClient;
use App\Traits\CheckKnowledge;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ChatController extends ApiController
{
    use CheckKnowledge;

    /**
     * Notes: 聊天
     *
     * @Author: 玄尘
     * @Date: 2024/12/3 09:40
     * @param  \Illuminate\Http\Request  $request
     * @throws \App\Exceptions\ValidatorException
     */
    public function chat(Request $request)
    {
        $request->kernel->validate([
            'app_id' => 'required',
        ], [
            'app_id.required' => '智能助理ID不能为空',
        ]);

        $app_id         = $request->app_id;
        $conversationId = $request->conversation_id;
        $content        = $request->content;
        $img_url        = $request->image_url;

        $user = $request->kernel->user();
//        if ($user['balance']<=0){
//            throw new ValidatorException("余额不足，请先充值");
//        }

        $assistant = Assistant::query()
            ->where('app_id', $app_id)
            ->where('is_deleted', 0)
            ->first();

        if (! $assistant) {
            throw new ValidatorException("智能助理不存在");
        }

        $this->chatDo($user, $assistant, $conversationId, $content);
        echo "data: {}";
        die;
        $chatMes = [
            'inputs'             => [],
            'query'              => $content,
            'response_mode'      => 'streaming',
            'conversation_id'    => $conversationId,
            'user'               => $user->username,
            'auto_generate_name' => true,
        ];

        if ($img_url) {
            $chatMes['files'] = [
                [
                    'type'            => 'image',
                    'transfer_method' => 'remote_url',
                    'url'             => $img_url,
                ]
            ];
        }

        $result = KnowledgeClient::client()->chat($assistant->api_key, $chatMes);

        return $request->kernel->success($result);
    }

    public function getMessage(Request $request)
    {
        $request->kernel->validate([
            'conversation_id' => 'required',
            'type'            => 'required',
            'app_id'          => 'required',
        ], [
            'conversation_id.required' => '会话ID不能为空',
            'type.required'            => '会话类型不能为空',
            'app_id.required'          => '智能助理ID不能为空',
        ]);

        $user = $request->kernel->user();

        $app_id          = $request->app_id;
        $type            = $request->type;
        $conversation_id = $request->conversation_id;

        $assistant = Assistant::where('app_id', $app_id)->where('is_deleted', 0)->first();

        if (! $assistant) {
            throw new ValidatorException("智能助理不存在");
        }

        $balance_draw = $balance = $user->account->balance;

        $map['type']   = $type;
        $map['uid']    = $user->id;
        $map['froms']  = "数字名片";
        $map['source'] = "知识库";

        $results = [];
        switch ($type) {
            case 1:
                $data   = [
                    'conversation_id' => $conversation_id,
                ];
                $result = KnowledgeClient::client()->getMessage($assistant->app_id, $data);

                $result = $result['data'];
                $count  = count($result) - 1;

                $map['tokens']  = $result[$count]['answer_tokens'] + $result[$count]['message_tokens'];
                $map['mid']     = $result[$count]['id'];
                $map['app_id']  = $assistant['app_id'];
                $map['kb_id']   = ! empty($assistant['kb_id']) ? $assistant['kb_id'] : 0;
                $map['content'] = json_encode([
                    'question' => $result[$count]['query'],
                    'answer'   => $result[$count]['answer']
                ]);
                //if ($balance<$map['tokens']) throw new ValidatorException("余额不足，无法完成对话");
                $mid     = $map['mid'];
                $results = $result;
                $result  = $result[$count];
                //$result['tokens'] = $map['tokens'];
                $result['tokens']       = 0;
                $result['time_elapsed'] = $result['provider_response_latency'];
                $update['balance']      = $balance - $map['tokens'];
                break;
            case 2:
                $genImg = GenImage::where('id', $conversation_id)->first();

                $mid = $conversation_id;
                if (! $genImg) {
                    throw new ValidatorException("未查询到该对话信息");
                }

                $map['tokens']  = 1;
                $map['mid']     = $genImg->id;
                $map['app_id']  = $genImg->id;
                $map['content'] = $genImg->message;
                $map['image']   = $genImg->url;
                //if ($balance_draw<$map['tokens']) throw new ValidatorException("余额不足，无法完成对话");
                $update['balance_draw'] = $balance_draw - $map['tokens'];
                $result                 = $genImg->toArray();
                $result['tokens']       = $map['tokens'];
                $response               = json_decode($result['response'], true);
                $result['time_elapsed'] = $response['response']['time_elapsed'];
                break;
            default:
                throw new ValidatorException("类型错误");
                break;
        }

        $res = DB::transaction(function () use ($map, $mid, $results, $result) {
            try {
                $tokenLogs = TokenLog::where('mid', $mid)->first();
                if (! $tokenLogs) {
                    //User::where(['id'=>$this->uid])->update($update);
                    TokenLog::create($map);
                }
                return isset($data['status']) ? $results : $result;
            } catch (ValidatorException $e) {
                throw new ValidatorException("操作失败");
            }
        });
        return $request->kernel->success($res);
    }

}
