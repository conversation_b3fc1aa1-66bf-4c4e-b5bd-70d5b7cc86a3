<?php

namespace App\Http\Controllers\Api\Knowledge;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Requests\Knowledge\AddFileRequest;
use App\Models\CompanyStaff;
use App\Models\Knowledge;
use App\Models\KnowledgeFile;
use App\Packages\Knowledge\Knowledge as KnowledgeClient;
use App\Traits\CheckKnowledge;
use Illuminate\Http\Request;

class FileController extends ApiController
{
    use CheckKnowledge;

    /**
     * Notes: 文件列表
     *
     * @Author: 玄尘
     * @Date: 2024/12/2 13:14
     * @param  \Illuminate\Http\Request  $request
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function fileList(Request $request)
    {
        $request->kernel->validate([
            'datasetId' => 'required',
            //            'keyword'   => 'required',
        ], [
            'datasetId.required' => '知识库ID不能为空',
            'keyword.required'   => '知识库名称必填',
        ]);

        $keyword  = $request->keyword ?? '';
        $page     = $request->page ?? 1;      //默认页码为1
        $pagesize = $request->pagesize ?? 10; //默认每页显示10条

        $userId    = $request->kernel->id();
        $datasetId = $request->datasetId;

        $knowledge = Knowledge::where('kb_id', $datasetId)
            ->where('is_deleted', 0)
            ->first();
        $this->checkKnowledge($userId, $knowledge, 1, 1, false);

        //获取符合条件的文件列表
        $knowledgeFiles = $knowledge->files()
            ->where('is_deleted', 0)
            ->when($keyword, fn($query) => $query->where('file_name', 'like', '%'.$keyword.'%'))
            ->latest();

        $list      = $knowledgeFiles->forPage($page, $pagesize)->get();
        $fieldList = $list->map(function ($file) use ($userId, $knowledge) {
            $is_manage = '';
            //检测是否是管理员
            if ($knowledge->company_id != 0) {
                $is_manage = CompanyStaff::query()
                    ->where('uid', $userId)
                    ->where('company_id', $knowledge->company_id)
                    ->where('is_work', 0)
                    ->value('is_manage');
            }

            return [
                'id'           => $file->id,
                'file_id'      => $file->file_id,
                'name'         => $file->file_name,
                'word_count'   => $file->word_count,
                'link'         => $file->link,
                'created_time' => (string) $file->created_at,
                'company_id'   => $file->company_id,
                'is_manage'    => $is_manage && ($is_manage != 0) ? 1 : 0,
            ];
        });

        $count                = $knowledgeFiles->count();
        $data['total']        = $count;
        $data['per_page']     = $pagesize;
        $data['current_page'] = $page;
        $data['last_page']    = ceil($data['total'] / $pagesize);
        $data['data']         = $fieldList;

        return $request->kernel->success($data);
    }

    /**
     * Notes: 上传文件
     *
     * @Author: 玄尘
     * @Date: 2024/12/2 13:55
     * @param  \Illuminate\Http\Request  $request
     * @return array
     * @throws \Exception
     */
    public function addFile(Request $request)
    {
        $addFileRequest = new AddFileRequest();

        $request->kernel->validate($addFileRequest->rules(), $addFileRequest->messages());

        $userId    = $request->kernel->id();
        $datasetId = $request->datasetId;
        $fileUrl   = $request->file;

        $knowledge = $this->checkKnowledgeByDatasetId($userId, $datasetId);

        $query  = [
            [
                'name'     => 'data',
                'contents' => json_encode([
                    "indexing_technique" => "high_quality",
                    "process_rule"       => [
                        "mode"  => "custom",
                        'rules' => [
                            'pre_processing_rules' => [
                                [
                                    'id'      => "remove_extra_spaces",
                                    'enabled' => true,
                                ],
                                [
                                    'id'      => "remove_urls_emails",
                                    'enabled' => true,
                                ],
                            ],
                            'segmentation'         => [
                                "separator"  => "###",
                                "max_tokens" => 500
                            ]
                        ],
                    ]
                ]),
                'headers'  => [
                    'Content-Type' => 'text/plain',
                ]
            ],
            [
                'name'     => 'file',
                'contents' => fopen($fileUrl, 'r')
            ]
        ];
        $result = KnowledgeClient::client()->addFile($datasetId, $query);

        $res = KnowledgeFile::create([
            'uid'        => $userId,
            'kid'        => $knowledge->id,
            'file_id'    => $result['document']['id'],
            'link'       => $fileUrl,
            'file_name'  => $result['document']['name'],
            'company_id' => $knowledge->company_id,
            'json_text'  => json_encode($result, JSON_UNESCAPED_UNICODE),
        ]);

        if (! $res) {
            throw new ValidatorException('操作失败');
        }

        return $request->kernel->success([
            'file_id' => $result['document']['id']
        ]);
    }

    public function fileInfo(Request $request)
    {
        $request->kernel->validate([
            'fileId' => 'required',
        ], [
            'fileId.required' => '文件ID不能为空',
        ]);

        $userId  = $request->kernel->id();
        $fileId  = $request->fileId;
        $keyword = $request->keyword;

        $file = KnowledgeFile::where('file_id', $fileId)
            ->where('is_deleted', 0)
            ->first();

        if (! $file) {
            throw new ValidatorException("未查询到该文档");
        }

        $knowledge = $file->knowledge()->where('is_deleted', 0)->first();

        $this->checkKnowledge($userId, $knowledge);

        $result = KnowledgeClient::client()->fileDetail($knowledge->kb_id, $fileId, $keyword);

        return $request->kernel->success($result);
    }

    /**
     * Notes: 文件重命名
     *
     * @Author: 玄尘
     * @Date: 2024/12/2 15:20
     * @param  \Illuminate\Http\Request  $request
     */
    public function rename(Request $request)
    {
        $request->kernel->validate([
            'fileId' => 'required',
            'name'   => 'required',
        ], [
            'fileId.required' => '文件ID不能为空',
            'name.required'   => '要修改的名称不能为空',
        ]);
        $userId = $request->kernel->id();
        $fileId = $request->fileId;
        $name   = $request->name;

        $file = KnowledgeFile::where('file_id', $fileId)
            ->where('is_deleted', 0)
            ->first();

        if (! $file) {
            throw new ValidatorException("未查询到该文档");
        }

        $knowledge = Knowledge::where('id', $file->kid)
            ->where('is_deleted', 0)
            ->first();

        $this->checkKnowledge($userId, $knowledge, 0, 1, false);

        $result = KnowledgeClient::client()->reFileName($knowledge['kb_id'], $fileId, ['name' => $name]);

        $res = KnowledgeFile::query()
            ->where(['file_id' => $fileId, 'is_deleted' => 0])
            ->where('file_id', $fileId)
            ->where('is_deleted', 0)
            ->update(['file_name' => $result['name']]);

        if (! $res) {
            throw new ValidatorException("文件重命名失败");
        }

        return $request->kernel->success(true);
    }

    /**
     * Notes: 删除文件
     *
     * @Author: 玄尘
     * @Date: 2024/12/2 15:28
     * @param  \Illuminate\Http\Request  $request
     */
    public function delFile(Request $request)
    {
        $request->kernel->validate([
            'fileId' => 'required',
        ], [
            'fileId.required' => '文件ID不能为空',
        ]);

        $userId = $request->kernel->id();
        $fileId = $request->fileId;

        $file = KnowledgeFile::query()
            ->where('file_id', $fileId)
            ->where('is_deleted', 0)
            ->first();

        if (! $file) {
            throw new ValidatorException("未查询到该文档");
        }

        $knowledge = Knowledge::query()
            ->where('id', $file->kid)
            ->where('is_deleted', 0)
            ->first();

        $this->checkKnowledge($userId, $knowledge);

        $result = KnowledgeClient::client()->delFile($knowledge->kb_id, $fileId);

        $res = KnowledgeFile::where('file_id', $fileId)->update(['is_deleted' => 1]);

        if (! $res) {
            throw new ValidatorException("操作失败");
        }

        return $request->kernel->success(true);
    }

}
