<?php

namespace App\Http\Controllers\Api\Auth;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\Controller;
use App\Models\AppToken;
use App\Models\Pclogin;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\Socialite\Models\Wechat;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Tinywan\Jwt\JwtToken;

class WechatController extends Controller
{
    public function getNewQrCode(Request $request)
    {
        $state       = $this->getNonceStrNow(4).uniqid().$this->getNonceStrNow(4);
        $oauth       = app('wechat.official_account')->getOAuth();
        $callback    = route('wechat.callback');
        $redirectUrl = $oauth->scopes(['snsapi_userinfo'])
            ->withState($state)
            ->redirect($callback);
        $qrCodeBase  = 'data:image/png;base64,'.base64_encode(QrCode::format('png')
                ->size(400)
                ->margin(1)
                ->generate($redirectUrl));
        return $request->kernel->success([
            'qrcode' => $qrCodeBase,
            'state'  => $state,
        ]);
    }

    protected function getNonceStrNow($length = 4)
    {
        $chars = "abcdefghijkmnpqrstuvwxyz23456789";
        $str   = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    public function callback(Request $request)
    {
        $code   = $request->code;
        $state  = $request->state;
        $client = app('wechat.official_account');
        $utils  = $client->getUtils();
        $config = $utils->buildJsSdkConfig(
            url: url()->current(),
            jsApiList: ['closeWindow'],
            openTagList: [],
            debug: false,
        );
        try {
            $oauth = $client->getOAuth();

            $user = $oauth->userFromCode($code);
        } catch (Exception $e) {
            return view('wechat.auth.error', compact('config'));
        }

        $openId  = $user['raw']['openid'];
        $unionId = $user['raw']['unionid'] ?? '';
        if ($unionId) {
            $wechat = Wechat::updateOrCreate([
                'union_id' => $unionId,
            ], [
                'wx_openid' => $openId,
                'nickname'  => $user->getNickname(),
                'avatar'    => $user->getAvatar(),
            ]);
        } else {
            $wechat = Wechat::firstOrCreate([
                'wx_openid' => $openId,
            ], [
                'nickname' => $user->getNickname(),
                'avatar'   => $user->getAvatar(),
            ]);
        }
        $pcData = [
            'site_id' => 1,
            'user_id' => 0,
            'openid'  => $openId,
            'code'    => $state,
            'remark'  => '登录成功',
        ];
        if ($wechat->user_id > 0) {
            $pcData['user_id'] = $wechat->user_id;
        } else {
            $user            = User::create([
                'username' => $openId,
            ]);
            $wechat->user_id = $user->id;
            $wechat->save();
            $pcData['user_id'] = $user->id;
        }
        Pclogin::create($pcData);

        return view('wechat.auth', compact('wechat', 'config'));
    }

    public function codeAuth(Request $request)
    {
        try {
            $miniApp = app('wechat.mini');
            $code    = $request->code;

            $utils    = $miniApp->getUtils();
            $response = $utils->codeToSession($code);
            $openId   = $response['openid'] ?? '';
            $unionId  = $response['unionid'] ?? '';
            $session  = $utils->decryptSession($response['session_key'], $request->iv, $request->encryptedData);
            $mobile   = $session['purePhoneNumber'];

            Log::info('解密出来的数据', $session);

            $wechat = Wechat::where('union_id', $unionId)->first();
            if ($unionId) {
                $wechat = Wechat::updateOrCreate([
                    'union_id' => $unionId,
                ], [
                    'mini_openid' => $openId,
                ]);
            } elseif ($openId) {
                $wechat = Wechat::updateOrCreate([
                    'mini_openid' => $openId,
                ]);
            } else {
                return $request->kernel->error('解析数据失败');
            }
            $wechat->fresh();
            $isFirst = false;
            if ($wechat->user_id) {
                $user = $wechat->user;
                if (Str::length($user->username) > 11) {
                    $user->username = $mobile;
                    $user->save();
                }
            } else {
                $user = User::where('username', $mobile)->first();
                if (! $user) {
                    $user    = User::create([
                        'username' => $mobile,
                    ]);
                    $isFirst = true;
                }
                $wechat->user_id = $user->id;
                $wechat->save();
            }
            $key                = md5(Str::random(32));
            $token              = JwtToken::generateToken([
                'id'  => $user->id,
                'key' => $key,
            ]);
            $token['key']       = $key;
            $token['user_info'] = [
                'uid'      => $user->id,
                'nickname' => $user->info->nickname,
                'phone'    => $user->username,
                'is_first' => $isFirst ? 1 : 0,
            ];
            return $request->kernel->success($token);
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

    public function getQrCode(Request $request)
    {
        $state       = $this->getNonceStrNow(4).uniqid().$this->getNonceStrNow(4);
        $oauth       = app('wechat.official_account')->getOAuth();
        $callback    = route('wechat.callback');
        $redirectUrl = $oauth->scopes(['snsapi_userinfo'])
            ->withState($state)
            ->redirect($callback);
        $qrCodeBase  = 'data:image/png;base64,'.base64_encode(QrCode::format('png')
                ->size(400)
                ->margin(1)
                ->generate($redirectUrl));
        return $request->kernel->success([
            'qr_code_url' => $qrCodeBase,
            'code'        => $state,
        ]);
    }

    public function checkCode(Request $request)
    {
        if (blank($request->code)) {
            throw new ValidatorException("请上传CODE");
        }

        $info = Pclogin::where('code', $request->code)->select('user_id', 'remark')->first();
        if (! $info) {
            return ['code' => 200, 'message' => '找不到CODE信息或用户还未扫描', 'data' => ['login_code' => 3]];
        }
        if ($info->remark == "") {
            return ['code' => 200, 'message' => '等待扫描', 'data' => ['login_code' => 1]];
        }
        if ($info->remark == "扫码成功") {
            return ['code' => 200, 'message' => '扫码成功', 'data' => ['login_code' => 2]];
        }

        if ($info['remark'] == "登录成功") {
            $key    = md5($this->getNonceStrNow(32));
            $userId = $info->user_id;
            $token  = JwtToken::generateToken([
                'id'  => $userId,
                'key' => $key
            ]);

            $body = $request->kernel->getBody();
            //保存token
            $data_token = [
                'user_id'     => $info['user_id'],
                'platform'    => $body['Platform'] ?? '',
                'token'       => $token['access_token'],
                'device'      => $body['Device'] ?? '',
                'key'         => $key,
                'token_sha1'  => sha1($token['access_token']),
                'app_version' => $body['AppVersion'] ?? '',
                'created_at'  => now(),
            ];
            AppToken::create($data_token);

            $user                = User::find($info['user_id']);
            $token['key']        = $key;
            $token['user_info']  = [
                'id'       => $user->id,
                'nickname' => $user->info->nickname,
                'avatar'   => $user->info->avatar_url,
                'phone'    => $user->username,
            ];
            $token['login_code'] = 0;
            $info->delete();
            return $request->kernel->success($token, '登录成功');
        }
    }
}