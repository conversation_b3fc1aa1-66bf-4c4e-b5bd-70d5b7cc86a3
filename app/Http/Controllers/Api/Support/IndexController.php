<?php

namespace App\Http\Controllers\Api\Support;

use App\Http\Controllers\ApiController;
use App\Http\Resources\Bailian\Article\ArticleBaseInfoResource;
use App\Http\Resources\Bailian\Knowledge\KnowledgeBaseInfoResource;
use App\Http\Resources\Support\CategoryResource;
use App\Models\BailianArticle;
use App\Models\BailianCategory;
use App\Models\BailianKnowledge;
use Illuminate\Http\Request;

class IndexController extends ApiController
{

    public function index(Request $request)
    {
        $categories = BailianCategory::query()
            ->OfEnabled()
            ->where('type', BailianCategory::TYPE_HELP)
            ->get();

        $articles      = BailianArticle::query()
            ->ofEnabled()
            ->where('category_id', $categories->first()->id)
            ->inRandomOrder()
            ->take(3)
            ->get();
        $helpKnowledge = BailianKnowledge::query()
            ->where('type', BailianKnowledge::TYPE_HELP)
            ->first();

        $data = [
            'categories'     => CategoryResource::collection($categories),
            'articles'       => ArticleBaseInfoResource::collection($articles),
            'help_knowledge' => $helpKnowledge ? new KnowledgeBaseInfoResource($helpKnowledge) : null,
        ];
        return $request->kernel->success($data);
    }

}
