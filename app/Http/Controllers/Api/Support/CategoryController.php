<?php

namespace App\Http\Controllers\Api\Support;

use App\Http\Controllers\ApiController;
use App\Http\Resources\Support\CategoryResource;
use App\Models\BailianCategory;
use Illuminate\Http\Request;

class CategoryController extends ApiController
{

    public function index(Request $request)
    {
        $categories = BailianCategory::query()
            ->OfEnabled()
            ->where('type', BailianCategory::TYPE_HELP)
            ->get();
        return $request->kernel->success(CategoryResource::collection($categories));
    }

}
