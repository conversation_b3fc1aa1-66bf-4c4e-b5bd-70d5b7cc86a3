<?php

namespace App\Http\Controllers\Api\Support;

use App\Http\Controllers\ApiController;
use App\Http\Resources\Bailian\Article\ArticleBaseInfoResource;
use App\Models\BailianArticle;
use App\Models\BailianCategory;
use Illuminate\Http\Request;

class ArticleController extends ApiController
{

    public function index(Request $request)
    {
        $category_id = $request->category_id ?? '';
        if (! $category_id) {
            $category = BailianCategory::query()
                ->ofEnabled()
                ->where('type', BailianCategory::TYPE_HELP)
                ->first();
            if ($category) {
                $category_id = $category->id;
            }
        }
        $articles = BailianArticle::query()
            ->ofEnabled()
            ->where('category_id', $category_id)
            ->inRandomOrder()
            ->take(3)
            ->get();
        return $request->kernel->success(ArticleBaseInfoResource::collection($articles));
    }

}
