<?php

namespace App\Http\Controllers\Api\Note;

use App\Http\Controllers\Controller;
use App\Http\Requests\Note\AddNoteFileRequest;
use App\Http\Requests\Note\FileMoveKnowledgeRequest;
use App\Http\Requests\Note\NoteFileRemoveKnowledgeRequest;
use App\Http\Requests\Note\UpdateNoteFileRequest;
use App\Http\Resources\Note\NoteFileCollection;
use App\Http\Resources\Note\NoteFileResource;
use App\Models\Enums\FastgptKnowledgeSetEnum;
use App\Models\FastgptKnowledgeSet;
use App\Models\NoteFile;
use App\Packages\FastGpt\FastGpt;
use App\Traits\FastGptTrait;
use DB;
use Illuminate\Http\Request;
use Modules\Storage\Models\Upload;
use Storage;

class FileController extends Controller
{
    use FastGptTrait;

    public function index(request $request)
    {
        $user         = $request->kernel->user();
        $knowledge_id = $request->konwledge_id ?? '';

        $files = NoteFile::query()
            ->with(['storage', 'knowledgeSet.knowledge'])
            ->where('user_id', $user->id)
            ->when($knowledge_id, function ($query) use ($knowledge_id) {
                $query->whereHas('knowledgeSet', function ($query) use ($knowledge_id) {
                    $query->where('knowledge_id', $knowledge_id);
                });
            })
            ->latest('id')
            ->paginate();
        return $request->kernel->success(new NoteFileCollection($files));
    }

    /**
     * Notes: 添加
     *
     * @Author: 玄尘
     * @Date: 2025/3/12 09:23
     * @param  \App\Http\Requests\Note\AddNoteFileRequest  $request
     * @return mixed
     */
    public function store(AddNoteFileRequest $request)
    {
        try {
            $user    = $request->kernel->user();
            $hash    = $request->hash;
            $storage = Upload::where('hash', $hash)->first();
            if (! $storage) {
                return $request->kernel->error('文件不存在');
            }
            NoteFile::create([
                'user_id'    => $user->id,
                'storage_id' => $storage->id
            ]);
            return $request->kernel->success('添加成功');
        } catch (\Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 编辑
     *
     * @Author: 玄尘
     * @Date: 2025/3/12 09:26
     * @param  \App\Http\Requests\Note\UpdateNoteFileRequest  $request
     * @return mixed
     */
    public function update(UpdateNoteFileRequest $request)
    {
        try {
            $hash       = $request->hash;
            $noteFileId = $request->note_file_id;
            $storage    = Upload::where('hash', $hash)->first();
            if (! $storage) {
                return $request->kernel->error('文件不存在');
            }
            $file             = NoteFile::findOrFail($noteFileId);
            $file->storage_id = $storage->id;
            $file->save();

            return $request->kernel->success('编辑成功');
        } catch (\Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function show(Request $request)
    {
        $request->kernel->validate([
            'note_file_id' => 'required|integer|exists:note_files,id',
        ], [
            'note_id.required' => '缺少附件id',
            'note_id.exists'   => '附件不存在',
        ]);

        $file = NoteFile::find($request->note_file_id);

        return $request->kernel->success(new NoteFileResource($file));
    }

    /**
     * Notes: 删除
     *
     * @Author: 玄尘
     * @Date: 2025/3/12 09:29
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function delete(Request $request)
    {
        try {
            $request->kernel->validate([
                'note_file_id' => 'required|integer|exists:note_files,id',
            ], [
                'note_id.required' => '缺少附件id',
                'note_id.exists'   => '附件不存在',
            ]);

            $noteFileId = $request->note_file_id;

            $file = NoteFile::find($noteFileId);

            if ($file->knowledgeSet) {
                return $request->kernel->error('附件已加入知识库，不可删除');
            }
            DB::beginTransaction();
            if ($file->storage) {
                Storage::delete($file->storage->path);
                $file->storage->delete();
            }
            $file->delete();
            DB::commit();
            return $request->kernel->success('删除成功');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 转入知识库
     *
     * @Author: 玄尘
     * @Date: 2025/3/12 10:18
     * @param  \App\Http\Requests\Note\FileMoveKnowledgeRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function moveToKnowledge(FileMoveKnowledgeRequest $request)
    {
        try {
            DB::beginTransaction();
            $knowledge_id = $request->knowledge_id;
            $note_file_id = $request->note_file_id;
            $name         = $request->name;
            $trainingType = $request->trainingType ?? 'qa';

            $noteFile = NoteFile::findOrFail($note_file_id);

            if ($noteFile->knowledgeSet) {
                throw new \Exception('该附件已关联知识库');
            }

            $knowledge = $this->checkKnowledgeById($knowledge_id);

            $this->checkPermissionData($request, $knowledge);

            $result = FastGpt::set()->setFileData($knowledge->dataset_id, $noteFile->storage->path_url, $trainingType);

            $set = FastgptKnowledgeSet::create([
                'knowledge_id'    => $knowledge->id,
                'note_file_id'    => $note_file_id,
                'collection_id'   => $result['data']['collectionId'],
                'name'            => $name,
                'type'            => FastgptKnowledgeSetEnum::FILE,
                'input_data'      => [
                    'file'         => $noteFile->storage->path_url,
                    'trainingType' => $trainingType,
                ],
                'training_amount' => 1,
                'output_result'   => $result,
            ]);
            DB::commit();
            return $request->kernel->success('转入知识库成功');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 移除知识库附件
     *
     * @Author: 玄尘
     * @Date: 2025/3/12 10:42
     * @param  \App\Http\Requests\Note\NoteFileRemoveKnowledgeRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function removeToKnowledge(NoteFileRemoveKnowledgeRequest $request)
    {
        try {
            DB::beginTransaction();
            $knowledge_set_id = $request->knowledge_set_id;
            $set              = FastgptKnowledgeSet::find($knowledge_set_id);
            if ($set->type->value != FastgptKnowledgeSetEnum::FILE->value) {
                throw new \Exception('该集合不是小记');
            }
            if (! $set->note_file_id) {
                throw new \Exception('该集合没有关联附件');
            }

            $result = FastGpt::set()->deleteSet($set->collection_id);
            $set->delete();
            DB::commit();
            return $request->kernel->success('移除成功');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

}
