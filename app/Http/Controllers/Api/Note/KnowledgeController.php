<?php

namespace App\Http\Controllers\Api\Note;

use App\Http\Controllers\Controller;
use App\Http\Requests\Note\AddKnowledgeNoteRequest;
use App\Http\Requests\Note\AddKnowledgeRequest;
use App\Http\Requests\Note\NoteRemoveKnowledgeRequest;
use App\Http\Requests\Note\UpdateKnowledgeNoteRequest;
use App\Http\Resources\FastGpt\KnowledgeSetResource;
use App\Http\Resources\Note\NoteCollection;
use App\Models\Enums\FastgptKnowledgeSetEnum;
use App\Models\FastgptKnowledgeSet;
use App\Models\Note;
use App\Packages\FastGpt\FastGpt;
use App\Traits\FastGptTrait;
use DB;
use Illuminate\Http\Request;

class KnowledgeController extends Controller
{
    use FastGptTrait;

    public function index(Request $request)
    {
        $title        = $request->title;
        $knowledge_id = $request->knowledge_id;
        if (! $knowledge_id) {
            return $request->kernel->error('知识库ID不能为空');
        }
        $user = $request->kernel->user();

        $notes = Note::query()
            ->whereHas('knowledgeSet', function ($query) use ($knowledge_id) {
                $query->where('knowledge_id', $knowledge_id);
            })
            ->when($title, function ($query, $title) {
                $query->where('title', 'like', "%$title%");
            })
            ->paginate(10);
        return $request->kernel->success(new NoteCollection($notes));
    }

    /**
     * Notes: 添加文档
     *
     * @Author: 玄尘
     * @Date: 2025/2/27 09:02
     * @param  \App\Http\Requests\Note\AddKnowledgeNoteRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function store(AddKnowledgeNoteRequest $request)
    {
        try {
            DB::beginTransaction();
            $knowledge_id = $request->knowledge_id;
            $name         = $request->name;
            $details      = $request->details;
            $parent_id    = $request->parent_id ?? null;
            $trainingType = $request->trainingType ?? 'qa';

            $knowledge = '';
            if ($knowledge_id) {
                $knowledge = $this->checkKnowledgeById($knowledge_id);
            }
            $user = $request->kernel->user();

            $note = Note::create([
                'user_id' => $user->id,
                'content' => $details,
                'title'   => $name,
                'type'    => Note::TYPE_DOCUMENT,
            ]);

            if ($knowledge) {
                $content = $note->getContent();
                $result  = FastGpt::set()->setText($knowledge->dataset_id, $name, $content, $trainingType);

                $set = $note->knowledgeSet()
                    ->create([
                        'knowledge_id'    => $knowledge->id,
                        'collection_id'   => $result['data']['collectionId'],
                        'name'            => $name,
                        'parent_id'       => $parent_id,
                        'type'            => FastgptKnowledgeSetEnum::NOTE,
                        'input_data'      => [
                            'name'         => $name,
                            'text'         => $content,
                            'trainingType' => $trainingType,
                        ],
                        'training_amount' => 1,
                        'output_result'   => $result,
                    ]);
            }

            DB::commit();
            return $request->kernel->success('添加成功');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 转入知识库
     *
     * @Author: 玄尘
     * @Date: 2025/2/27 09:00
     * @param  \App\Http\Requests\Note\AddKnowledgeRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function moveToKnowledge(AddKnowledgeRequest $request)
    {
        try {
            DB::beginTransaction();
            $knowledge_id = $request->knowledge_id;
            $note_id      = $request->note_id;
            $name         = $request->name;
            $details      = $request->details;
            $trainingType = $request->trainingType ?? 'qa';

            $note = Note::findOrFail($note_id);

            if ($note->knowledgeSet) {
                throw new \Exception('该小记已关联知识库');
            }

            $note->title   = $name;
            $note->type    = Note::TYPE_DOCUMENT;
            $note->content = $details;
            $note->save();
            $content = $note->getContent();

            $knowledge = $this->checkKnowledgeById($knowledge_id);

            $this->checkPermissionData($request, $knowledge);
            $result = '';
            if (! empty($content)) {
                $result = FastGpt::set()->setText($knowledge->dataset_id, $name, $content, $trainingType);
            }

            $set = FastgptKnowledgeSet::create([
                'knowledge_id'    => $knowledge->id,
                'note_id'         => $note_id,
                'collection_id'   => $result['data']['collectionId'] ?? '',
                'name'            => $name,
                'type'            => FastgptKnowledgeSetEnum::NOTE,
                'input_data'      => [
                    'name'         => $name,
                    'text'         => $content,
                    'trainingType' => $trainingType,
                ],
                'training_amount' => 1,
                'output_result'   => $result,
            ]);
            DB::commit();
            return $request->kernel->success(new KnowledgeSetResource($set));
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 编辑知识库信息
     *
     * @Author: 玄尘
     * @Date: 2025/2/26 13:15
     * @param  \App\Http\Requests\Note\UpdateKnowledgeNoteRequest  $request
     * @return mixed
     * @throws \Throwable
     */

    public function update(UpdateKnowledgeNoteRequest $request)
    {
        try {
            DB::beginTransaction();
            $note_id      = $request->note_id;
            $name         = $request->name;
            $details      = $request->details;
            $trainingType = $request->trainingType ?? 'qa';

            $note         = Note::findOrFail($note_id);
            $knowledgeSet = $note->knowledgeSet;
            if (! $knowledgeSet) {
                throw new \Exception('该小记未关联知识库');
            }
            $note->title   = $name;
            $note->content = $details;
            $note->save();

            $knowledge = $this->checkKnowledgeById($knowledgeSet->knowledge_id);

            $this->checkPermissionData($request, $knowledge);

            $content = $note->getContent();

            $result = FastGpt::set()->deleteSet($knowledgeSet->collection_id);
            $result = FastGpt::set()->setText($knowledge->dataset_id, $name, $content, $trainingType);

            $set = $knowledgeSet->update([
                'name'            => $name,
                'collection_id'   => $result['data']['collectionId'] ?? '',
                'input_data'      => [
                    'name'         => $name,
                    'text'         => $content,
                    'trainingType' => $trainingType,
                ],
                'training_amount' => 1,
                'output_result'   => $result,
            ]);

            DB::commit();
            return $request->kernel->success(new KnowledgeSetResource($knowledgeSet));
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 小记移除知识库
     *
     * @Author: 玄尘
     * @Date: 2025/3/11 18:34
     * @param  \App\Http\Requests\Note\NoteRemoveKnowledgeRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function removeToKnowledge(NoteRemoveKnowledgeRequest $request)
    {
        try {
            DB::beginTransaction();
            $knowledge_set_id = $request->knowledge_set_id;
            $set              = FastgptKnowledgeSet::find($knowledge_set_id);
            if ($set->type->value != FastgptKnowledgeSetEnum::NOTE->value) {
                throw new \Exception('该集合不是小记');
            }

            $result = FastGpt::set()->deleteSet($set->collection_id);
            $set->delete();
            DB::commit();
            return $request->kernel->success('移除成功');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

}
