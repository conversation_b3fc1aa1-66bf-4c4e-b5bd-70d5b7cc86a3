<?php

namespace App\Http\Controllers\Api\Tool;

use App\Http\Controllers\Controller;
use App\Packages\WateCheckPhone;
use Illuminate\Http\Request;
use Modules\User\Rules\MobileRule;

class IndexController extends Controller
{
    public function checkCode(Request $request)
    {
        $request->kernel->validate([
            'phone' => [
                'required',
                new MobileRule()
            ],
            //            'type'  => 'required',
            'code'  => 'required',
        ], [
            'phone.required' => '请输入手机号',
            'type.required'  => '验证码类型不能为空',
            'code.required'  => '请输入验证码'
        ]);
        $code  = $request->code;
        $phone = $request->phone;
        $type  = $request->type;
        WateCheckPhone::checkPhone($phone, $type, $code);

        return $request->kernel->success([]);
    }

}