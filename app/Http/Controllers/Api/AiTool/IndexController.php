<?php

namespace App\Http\Controllers\Api\AiTool;

use App\Http\Controllers\Controller;
use App\Http\Resources\AiTool\CategoryResource;
use App\Http\Resources\AiTool\ToolCollection;
use App\Models\AiTool;
use App\Models\AiToolCategory;
use Illuminate\Http\Request;

class IndexController extends Controller
{
    /**
     * Notes: 分类列表
     *
     * @Author: 玄尘
     * @Date: 2025/4/17 09:02
     */
    public function category(Request $request)
    {
        $categories = AiToolCategory::where('status', 1)->get();

        return $request->kernel->success(CategoryResource::collection($categories));
    }

    public function index(Request $request)
    {
        $category_id = $request->category_id ?? '';
        $name        = $request->name ?? '';
        $pageSize    = $request->pagesize ?? 25;

        $tools       = AiTool::query()
            ->when($category_id, function ($query) use ($category_id) {
                $query->where('category_id', $category_id);
            })
            ->when($name, function ($query) use ($name) {
                $query->where('name', 'like', '%'.$name.'%');
            })
            ->where('status', 1)
            ->paginate($pageSize);

        return $request->kernel->success(new ToolCollection($tools));
    }
}