<?php

namespace App\Http\Controllers\Api\Vidu;

use App\Http\Controllers\Controller;
use App\Http\Requests\Vidu\DrawRequest;
use App\Http\Resources\Vidu\ViduDrawCollection;
use App\Http\Resources\Vidu\ViduDrawResource;
use App\Models\PluginViduDraw;
use App\Models\PluginViduTemplate;
use DB;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class IndexController extends Controller
{

    public function index(Request $request)
    {
        $user = $request->kernel->user();

        $list = PluginViduDraw::query()
            ->where('user_id', $user->id)
            ->with(['template'])
            ->latest('id')
            ->paginate($request->per_page ?? 10);

        return $request->kernel->success(new ViduDrawCollection($list));
    }

    /**
     * Notes: 查询任务状态
     *
     * @Author: 玄尘
     * @Date: 2025/4/18 14:23
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function query(Request $request)
    {
        $no   = $request->no;
        $user = $request->kernel->user();
        try {
            $viduDraw = PluginViduDraw::where('no', $no)->first();
            if (! $viduDraw) {
                return $request->kernel->error('任务不存在');
            }

            // 检查任务是否属于当前用户
            if ($viduDraw->user_id != $user->id) {
                return $request->kernel->error('无权访问此任务');
            }

            if ($viduDraw->canPolling()) {
                $viduDraw->videoQuery();
            }

            return $request->kernel->success(new ViduDrawResource($viduDraw));
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * Notes: 制作视频
     *
     * @Author: 玄尘
     * @Date: 2025/4/18 14:23
     * @param  \App\Http\Requests\Vidu\DrawRequest  $request
     * @return mixed
     */
    public function draw(DrawRequest $request)
    {
        try {
            DB::beginTransaction();
            $scene        = $request->template;
            $images       = $request->images;
            $prompt       = $request->prompt;
            $seed         = $request->seed;
            $aspect_ratio = $request->aspect_ratio;
            $extra        = $request->extra;

            //            $images       = [
            //                'https://cdn.watestar.com/2024/12/10/359101d690a8901dc929dcdddddce75d.jpg'
            //            ];
            // 检查图片数量是否符合模板要求
            $template = PluginViduTemplate::where('scene', $scene)->first();
            $user     = $request->kernel->user();

            $data = [
                'user_id'  => $user->id,
                'scene'    => $template->scene,
                'status'   => PluginViduDraw::STATUS_INIT,
                'inputs'   => [
                    [
                        'url'          => $images,
                        'prompt'       => $prompt,
                        'seed'         => $seed,
                        'aspect_ratio' => $aspect_ratio,
                    ]
                ],
                'is_asset' => $request->is_asset ? true : false,
            ];

            if ($extra) {
                $extraName                  = $template->getExtraName();
                $data['inputs'][$extraName] = $extra;
            }

            // 创建任务
            $info = PluginViduDraw::create($data);
            DB::commit();
            return $request->kernel->success(new ViduDrawResource($info), '已排入制作队列');
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Vidu视频生成任务创建失败', [
                'error'  => $e->getMessage(),
                'trace'  => $e->getTraceAsString(),
                'params' => $request->all(),
            ]);
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * Notes: 删除任务
     *
     * @Author: 玄尘
     * @Date: 2025/4/18 14:23
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $taskId
     * @return mixed
     */
    public function delete(Request $request, $taskId)
    {
        $no = $request->no;

        try {
            $viduDraw = PluginViduDraw::where('no', $no)->first();
            if (! $viduDraw) {
                return $request->kernel->error('任务不存在');
            }

            // 检查任务是否属于当前用户
            if ($viduDraw->user_id != $request->user()->id) {
                return $request->kernel->error('无权访问此任务');
            }

            if (! $viduDraw->canDelete()) {
                return $request->kernel->error('当前状态不可删除');
            }

            $viduDraw->delete();

            return $request->kernel->success('删除成功');
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }
}