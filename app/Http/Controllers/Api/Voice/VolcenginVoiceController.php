<?php

namespace App\Http\Controllers\Api\Voice;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class VolcenginVoiceController extends Controller
{
    use VolcenginVoiceBase;

    protected function train(Request $request)
    {
        $request->kernel->validate([
            'speaker_id' => 'required',
            'text'       => 'required',
            'audio_url'  => 'required|url',
        ], [
            'speaker_id.required' => '请上选择音频id',
            'text.required'       => '请上传文本',
            'audio_url.required'  => '请上传音频文件',
            'audio_url.url'       => '音频文件地址不正确',
        ]);
        $data['appid'] = $this->appId;
        //唯一音色代号
        $data['speaker_id'] = $request->speaker_id;
        //音频格式支持：wav、mp3、ogg、m4a、aac、pcm，其中pcm仅支持24k 单通道目前限制单文件上传最大10MB，每次最多上传1个音频文件
        //音频文件base64编码后的数据，每次最多上传1个音频文件，音频文件大小限制10MB
        $data['audios'] = [
            [
                'audio_bytes'  => base64_encode(file_get_contents($request->audio_url)),
                'audio_format' => 'mp3',
            ]
        ];
        //可以让用户按照该文本念诵，服务会对比音频与该文本的差异。若差异过大会返回1109 WERError
        $data['text'] = $request->text;
        /**
         * 默认为0
         * 1为2.0效果（ICL），0为1.0效果
         * 2为DiT标准版效果（音色、不还原用户的风格）
         * 3为DiT还原版效果（音色、还原用户口音、语速等风格）
         */
        $data['model_type'] = 0;

        //固定值：2
        $data['source'] = 2;
        /*
        model_type为0或者1时候，支持以下语种
        cn = 0 中文（默认）
        en = 1 英文
        ja = 2 日语
        es = 3 西班牙语
        id = 4 印尼语
        pt = 5 葡萄牙语
        model_type为2或者3时候，仅支持以下语种
        cn = 0 中文（默认）
        en = 1 英文
        */
        $data['language'] = 0;

        $result = $this->getResponse('api/v1/mega_tts/audio/upload', $data);
        dd($result);
        //        if ($result['code'] === 0) {
        //            $task->logs()->updateOrCreate([
        //                'text_id' => $request->text_id,
        //                'seg_id'  => $request->seg_id,
        //            ], [
        //                'audio_url' => $request->audio_url,
        //                'flag'      => $result['flag'],
        //            ]);
        //            return $request->kernel->success([
        //                'test'   => $result,
        //                'result' => $result['flag'],
        //                'task'   => new XunfeiTaskResource($task),
        //            ]);
        //        } else {
        //            return $request->kernel->error($result['desc']);
        //        }
    }
}
