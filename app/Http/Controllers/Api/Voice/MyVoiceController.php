<?php

namespace App\Http\Controllers\Api\Voice;

use App\Http\Resources\AiAbout\BailianTts\BailianTtsResource;
use App\Models\AudioTimbre;
use Illuminate\Http\Request;

class MyVoiceController extends BaseController
{
    public function index(Request $request)
    {
        $user = $request->kernel->user();
        $list = AudioTimbre::ofUser($user)
            ->ofEnabled()
            ->get();
        return $request->kernel->success(BailianTtsResource::collection($list), '查询成功');
    }
}
