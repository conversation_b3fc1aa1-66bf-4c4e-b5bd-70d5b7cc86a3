<?php

namespace App\Http\Controllers\Api\Voice;

use App\Exceptions\ValidatorException;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;

trait VolcenginVoiceBase
{
    protected string $appId       = '6684464157';
    protected string $assetsToken = 'KWHuQZBapsQnhKcG9KRVEvG2rs1j63s7';
    protected string $baseUrl     = 'https://openspeech.bytedance.com/';

    protected function getResponse(string $path, array $params = [])
    {
        $client = new Client([
            'base_uri' => $this->baseUrl,
            'verify'   => false,
        ]);
        try {
            $response = $client->post($path, [
                'headers' => [
                    'Authorization' => 'Bearer; '.$this->assetsToken,
                    'Resource-Id'   => 'volc.megatts.voiceclone',
                ],
                'json'    => $params,
            ]);
            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                return $data;
            } else {
                throw new ValidatorException($response->getBody()->getContents());
            }
        } catch (RequestException $exception) {
            $content = $exception->getResponse()->getBody()->getContents();
            if (Str::isJson($content)) {
                $data = json_decode($content, true);
                throw new ValidatorException($data['message']);
            } else {
                throw new ValidatorException($content);
            }
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }
}
