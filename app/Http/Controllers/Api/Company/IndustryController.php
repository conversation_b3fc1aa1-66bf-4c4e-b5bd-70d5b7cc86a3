<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\ApiController;
use App\Http\Resources\Industry\IndustryResource;
use App\Models\Industry;
use Illuminate\Http\Request;

class IndustryController extends ApiController
{
    public function index(Request $request)
    {
        $industries = Industry::get()->pluck('name')->toArray();

        return $request->kernel->success($industries);
    }

}
