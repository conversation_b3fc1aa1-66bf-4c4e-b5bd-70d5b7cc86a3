<?php

namespace App\Http\Controllers\Api\Company;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Requests\Company\AddUserRequest;
use App\Http\Requests\Company\BatchAddUserRequest;
use App\Http\Requests\Company\CompanyRequest;
use App\Http\Resources\Company\CompanyResource;
use App\Http\Resources\Company\CompanyStaffCollection;
use App\Models\Business;
use App\Models\Company;
use App\Models\CompanyDepartmentRole;
use App\Models\CompanyRole;
use App\Models\CompanyStaff;
use App\Models\CompanyUser;
use App\Models\Contact;
use App\Models\Realname;
use App\Models\User;
use App\Traits\CompanyTraits;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Modules\User\Rules\MobileRule;

class CompanyController extends ApiController
{
    use CompanyTraits;

    /**
     * Notes: 待认证企业
     *
     * @Author: 玄尘
     * @Date: 2024/12/23 09:26
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function unverifiedCompany(Request $request)
    {
        $user = $request->kernel->user();

        //检测是否实名认证
        $isRealname = $user->realName()->where('is_check', 1)->exists();

        $businesses = Business::where('uid', $user->id)
            ->oldest()
            ->get();

        $companyName = [];
        $list        = [];
        foreach ($businesses as &$business) {
            $business->is_realname = 0;
            if ($isRealname) {
                $business->is_realname = 1;
            }

            $business->is_manage = (bool) $user->isManagementRole($business->company_id);

            if (in_array($business->company_name, $companyName)) {
                continue;
            } else {
                $companyName[] = $business->company_name;
                $list[]        = $business;
            }
        }
        return $request->kernel->success($list);
    }

    /**
     * Notes: 企业认证
     *
     * @Author: 玄尘
     * @Date: 2024/12/5 13:01
     * @return true
     * @throws \App\Exceptions\ValidatorException
     */
    public function authentication(Request $request)
    {
        $companyRequest = new CompanyRequest();

        $request->kernel->validate($companyRequest->rules(), $companyRequest->messages());

        $user = $request->kernel->user();

        $enterpriseName = $request->enterpriseName;
        $enterpriseNo   = $request->enterpriseNo;
        $legalName      = $request->legalName;
        $legalIdCard    = $request->legalIdCard;
        $phone          = $request->phone;
        $bid            = $request->bid ?? '';
        $img            = $request->img ?? '';

        //检测名片是否存在
        if ($bid) {
            $business = Business::find($bid);
        } else {
            $business = Business::where('uid', $user->id)->first();
        }

        if (! $business) {
            throw new ValidatorException("未查询到名片信息");
        }

        if ($business->is_work == 1) {
            throw new ValidatorException("该名片已经职位认证过");
        }

        //检测用户是否实名
        $realname = Realname::where('uid', $user->id)->first();

        if (! $realname) {
            throw new ValidatorException("请先进行实名认证");
        }

        if ($realname->is_check == 0) {
            throw new ValidatorException("实名认证正在审核中");
        }

        if ($realname->is_check == 2) {
            throw new ValidatorException("实名认证审核没通过，请先重新认证实名认证");
        }

        $company = Company::where('enterprise_no', $enterpriseNo)->first();

        if ($company) {
            if ($company->is_check == 0) {
                throw new ValidatorException("企业认证正在审核中");
            }

            if ($company->is_check == 1) {
                throw new ValidatorException("该企业已经认证过");
            }

            $company->update([
                'uid'           => $user->id,
                'company_name'  => $enterpriseName,
                'enterprise_no' => $enterpriseNo,
                'legal_name'    => $legalName,
                'id_card'       => $legalIdCard,
                'img'           => $img ?? '',
                'code'          => '',
                'is_check'      => 0
            ]);
        } else {
            DB::beginTransaction();
            //先检测三要素
            $checkUser = alibaba(
                url: 'https://mobilecert.market.alicloudapi.com/mobile3MetaSimple',
                paramsType: "query",
                params: [
                    'identifyNum' => $legalIdCard,
                    'mobile'      => $phone,
                    'userName'    => $legalName,
                ]
            );
            switch ($checkUser['bizCode']) {
                case 2:
                    throw new ValidatorException("校验不一致");
                    break;
                case 3:
                    throw new ValidatorException("查无记录");
                    break;
            }

            //进行企业认证
            try {
                //企业三要素
                $companyData = alibaba(
                    url: 'https://juccvvt.market.alicloudapi.com/enterprise/business/base',
                    paramsType: 'form_params',
                    params: [
                        'keyword' => $enterpriseName,
                    ],
                    method: 'POST',
                );

                if ($companyData['LegalPerson'] != $legalName) {
                    throw new ValidatorException("法人姓名错误");
                }

                if ($companyData['CreditNo'] != $enterpriseNo) {
                    throw new ValidatorException("企业社会统一信用代码错误");
                }

                $company = Company::create([
                    'uid'           => $user->id,
                    'company_name'  => $enterpriseName,
                    'enterprise_no' => $enterpriseNo,
                    'legal_name'    => $legalName,
                    'id_card'       => $legalIdCard,
                    'is_check'      => 1,
                    'img'           => $img ?? '',
                    'code'          => '',
                    'data_json'     => $companyData
                ]);

                CompanyUser::create([
                    'uid'        => $user->id,
                    'company_id' => $company->id,
                    'nickname'   => $business->nickname,
                    'position'   => $business->position,
                    'type'       => 0,
                    'is_check'   => 1,
                    //                    'is_private' => 1,
                    'bid'        => $business->id,
                ]);

                CompanyStaff::create([
                    'uid'        => $user->id,
                    'company_id' => $company->id,
                    'is_work'    => 0,
                    'is_open'    => 0,
                    'type'       => 0,
                ]);

                Business::where('company_name', $business->company_name)
                    ->update([
                        'company_id'   => $company->id,
                        'company_name' => $enterpriseName,
                        'is_work'      => 1,
                        'is_company'   => 1,
                    ]);

                //增加权限
//                $user->companyDepartmentRoles()
//                    ->create([
//                        'department_id'   => 0,
//                        'company_role_id' => 1,
//                    ]);

                DB::commit();
                return $request->kernel->success(true);
            } catch (\Exception $e) {
                DB::rollBack();
                throw new ValidatorException($e->getMessage());
            }
        }
        throw new ValidatorException("认证失败");
    }

    /**
     * Notes: 设置邀请码
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 09:08
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function setCode(Request $request)
    {
        $request->kernel->validate([
            'code'       => ['required', 'min:4', 'max:8'],
            'type'       => 'required|integer',
            'company_id' => 'required',
        ], [
            'code.required'       => '邀请码不能为空',
            'code.min'            => '邀请码长度大于 :min',
            'code.max'            => '邀请码长度小于 :max',
            'type.required'       => '类型不能为空',
            'type.integer'        => '类型只能是数字',
            'company_id.required' => '企业ID不能为空'
        ]);

        $user = $request->kernel->user();

        $company_id = $request->company_id;
        $type       = $request->type;
        $code       = $request->code;

        if (! $user->isManagementRole($company_id)) {
            throw new ValidatorException("权限不足");
        }

        switch ($type) {
            case 1:
                $update['is_open'] = 0;
                break;
            case 2:
                $update['code']     = $code;
                $update['exp_time'] = Carbon::now()->addDays(7)->toDateTimeString();
                $update['is_open']  = 1;
                break;
            case 3:
                $update['code']     = $code;
                $update['exp_time'] = 0;
                $update['is_open']  = 1;
                break;
            default:
                throw new ValidatorException("类型错误");
                break;
        }

        $company = Company::find($company_id);

        $this->checkCompany($company);

        if ($company->is_open == 0 && $type == 1) {
            throw new ValidatorException("已关闭邀请码，请勿重复设置");
        }

        $res = $company->update($update);

        if (! $res) {
            throw new ValidatorException("邀请码设置失败");
        }
        return $request->kernel->success(true);
    }

    /**
     * Notes: 在职认证
     *
     * @Author: 玄尘
     * @Date: 2024/12/9 17:08
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function workAuthentication(Request $request)
    {
        $request->kernel->validate([
            'type'         => 'required|integer',
            'company_name' => 'required',
            'nickname'     => 'required',
            'position'     => 'required',
            'link'         => 'required',
            'bid'          => 'required|integer',
            'company_id'   => 'required|integer',
        ], [
            'company_name.required' => '企业名称不能为空',
            'nickname.required'     => '姓名',
            'type.required'         => '类型不能为空',
            'type.integer'          => '类型只能是数字',
            'position.required'     => '职位不能为空',
            'link.required'         => 'link不能为空',
            'bid.required'          => '名片id不能为空',
            'bid.integer'           => '名片id只能是数字',
            'company_id.required'   => '企业id不能为空',
            'company_id.integer'    => '企业id只能是数字',
        ]);

        $user = $request->kernel->user();

        $type         = $request->type;
        $company_name = $request->company_name;
        $nickname     = $request->nickname;
        $position     = $request->position;
        $link         = $request->link;
        $bid          = $request->bid;
        $company_id   = $request->company_id;

        //检测名片是否存在
        $business = Business::find($bid);

        if (! $business) {
            throw new ValidatorException("未查询到名片信息");
        }
        if ($business->is_work == 1) {
            throw new ValidatorException("该名片已经职位认证过");
        }

        $company = Company::find($company_id);

        $this->checkCompany($company);

        $companyUserData = [
            'bid'        => $business->id,
            'company_id' => $company_id,
            'type'       => $type,
            'nickname'   => $nickname,
            'position'   => $position,
            'uid'        => $user->id
        ];

        $companyUser = CompanyUser::where('uid', $user->id)
            ->where('bid', $business->id)
            ->whereIn('is_check', [0, 1])
            ->first();

        if ($companyUser) {
            throw new ValidatorException("已经提交过，请等待审核");
        }

        DB::beginTransaction();
        try {
            switch ($type) {
                case 1://邀请码
                    if ($company->is_open != 1) {
                        DB::rollBack();
                        throw new ValidatorException("邀请码认证已经关闭");
                    }
                    if ($company->exp_time && Carbon::parse($company->exp_time)->lt(Carbon::now())) {
                        DB::rollback();
                        throw new ValidatorException("邀请码已经过期");
                    }

                    $companyUserData = array_merge($companyUserData, [
                        'is_check' => 1,
                        //                        'is_private' => 1,
                    ]);

                    if ($company->code != $link) {
                        DB::rollBack();
                        throw new ValidatorException("邀请码错误");
                    }

                    $staff = $user->companyStaffs()->where('company_id', $company->id)->first();
                    if (! $staff) {
                        CompanyStaff::create([
                            'uid'        => $user->id,
                            'company_id' => $company->id,
                            'is_work'    => 0,
                            'is_open'    => 0,
                            'type'       => 0
                        ]);
                    }

                    $business->update([
                        'company_id'   => $company->id,
                        'is_work'      => 1,
                        'is_company'   => 1,
                        'position'     => $position,
                        'company_name' => $company_name,
                        'company_icon' => $company->logo
                    ]);

                    break;
                case 2:
                case 3:
                    $companyUserData = array_merge($companyUserData, [
                        'img'      => $link,
                        'is_check' => 0,
                    ]);
                    break;
                default:
                    break;
            }

            CompanyUser::create($companyUserData);

            DB::commit();
            return $request->kernel->success(true);
        } catch (ValidatorException $e) {
            DB::rollBack();
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 企业列表
     *
     * @Author: 玄尘
     * @Date: 2025/4/7 13:37
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function companies(Request $request)
    {
        $companies = Company::where('is_check', 1)->get();
        return $request->kernel->success(CompanyResource::collection($companies));
    }

    /**
     * Notes: 企业详情
     *
     * @Author: 玄尘
     * @Date: 2024/12/9 17:29
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function info(Request $request)
    {
        $request->kernel->validate([
            'company_id' => 'required|integer'
        ], [
            'company_id.required' => '企业id不能为空',
            'company_id.integer'  => '企业id只能是数字'
        ]);

        $user = $request->kernel->user();

        $companyId = $request->company_id;

        $company = Company::find($companyId);
        $this->checkCompany($company);

        //在职人员数

        $data['in_job'] = $company->users()->where('is_check', 1)->where('is_work', 0)->count();
        //离职人员数
        $data['out_job'] = $company->users()->where('is_work', 1)->where('is_work', 1)->count();

        //待审核成员数
        $data['is_check'] = $company->users()->where('is_check', 0)->count();

        //待激活成员数
        $data['is_active'] = $company->users()->where('is_check', 0)->count();

        //企业人脉总数
        //获取所有在职认证过的名片id
        $businesses = $company->businesses;

        $businessesIds = $company->businesses->pluck('id')->toArray();
        $contactCount  = Contact::whereIn('to_bid', $businessesIds)->where('status', 1)->count();

        $data['count']         = $contactCount;
        $data['company_name']  = $company->company_name;
        $data['manage']        = $user->isManagementRole($company->id);
        $data['business_type'] = $company->business_type;
        $data['business']      = $company->getBusiness();
        $data['company_type']  = $company->company_type;
        $data['company']       = $company->getCompany();
        $data['logo']          = $company->logo_url ?: "";
        $data['created_time']  = (string) $company->created_at;
        $data['code']          = $company->code;

        return $request->kernel->success($data);
    }

    public function update(Request $request)
    {
        $request->kernel->validate([
            'company_id'    => 'required|integer',
            'business_type' => 'nullable|integer|in:0,1,2',
            'company_type'  => 'nullable|integer|in:0,1,2',
            //            'business'      => 'required',
        ], [
            'company_id.required'   => '企业id不能为空',
            'company_id.integer'    => '企业id只能是数字',
            'business_type.integer' => '业务介绍类型只能是数字',
            'business_type.in'      => '业务介绍类型参数不对',
            'company_type.integer'  => '企业介绍类型只能是数字',
            'company_type.in'       => '企业介绍类型参数不对'
        ]);

        $user          = $request->kernel->user();
        $companyId     = $request->company_id;
        $business_type = $request->business_type;
        $company_type  = $request->company_type;
        $business      = $request->business;
        $company       = $request->company;
        $logo          = $request->logo;

        $map = [];

        if (! $user->isManagementRole($companyId)) {
            throw new ValidatorException("您没有操作权限");
        }

        $companyModel = Company::find($companyId);

        $this->checkCompany($companyModel);

        if (in_array($business_type, [0, 1, 2]) && $business) {
            $map['business_type'] = $business_type;
            $map['business']      = is_array($business) ? $business : [$business];
        }

        if (in_array($company_type, [0, 1, 2]) && $company) {
            $map['company_type'] = $company_type;
            $map['company']      = is_array($company) ? $company : [$company];
        }

        if ($logo !== null) {
            $map['logo'] = $logo;
        }

        if (empty($map)) {
            throw new ValidatorException("参数缺失");
        }

        $res = $companyModel->update($map);
        if (! $res) {
            throw new ValidatorException("操作失败");
        }

        if (isset($map['logo'])) {
            $companyModel->businesses()->update([
                'company_icon' => $map['logo']
            ]);
        }
        return $request->kernel->success($res);
    }

    /**
     * Notes: 获取员工
     *
     * @Author: 玄尘
     * @Date: 2024/12/6 08:39
     */
    public function users(Request $request)
    {
        $request->kernel->validate([
            'company_id' => 'required|integer',
            'type'       => 'required|integer|in:1,2,3',
        ], [
            'company_id.required' => '企业id不能为空',
            'company_id.integer'  => '企业id只能是数字',
            'type.required'       => '在职状态不能为空',
            'type.integer'        => '在职状态只能是数字',
            'type.in'             => '在职状态参数不对'
        ]);

        $user      = $request->kernel->user();
        $companyId = $request->company_id;
        $type      = $request->type;

        $page     = $request->page ?? 1;
        $pageSize = $request->pagesize ?? 10;

        $companyUserQuery = CompanyUser::with(['business'])
            ->ofTypeSearch($type)
            ->where('company_id', $companyId);

        $types = $type == 3 ? 3 : 2;

        if (! $user->isManagementRole($companyId)) {
            throw new ValidatorException("权限不足");
        }

//        $role = CompanyStaff::getRole($userId, $companyId, $types);
//        if (! $role) {
//            throw new ValidatorException("权限不足");
//        }

        $companyUsers = $companyUserQuery->paginate($pageSize, ['*'], 'page', $page);

        $list = [];
        foreach ($companyUsers as $companyUser) {
            $list[] = [
                'id'           => $companyUser->id,
                'uid'          => $companyUser->uid,
                'nickname'     => $companyUser->nickname,
                'position'     => $companyUser->position,
                'is_check'     => $companyUser->is_check,
                'is_work'      => $companyUser->is_work,
                'is_manage'    => $companyUser->user->isManagementRole($companyUser->company_id),
                'phone'        => $companyUser->business->phone ?? '',
                'wechat'       => $companyUser->business->wechat ?? '',
                'created_time' => (string) $companyUser->created_at,
                'type_name'    => $this->getTypeName($companyUser->type),
            ];
        }

        $data = [
            'total'        => $companyUsers->count(),
            'per_page'     => $companyUsers->perPage(),
            'current_page' => $companyUsers->currentPage(),
            'last_page'    => $companyUsers->lastPage(),
            'data'         => $list,
        ];

        return $request->kernel->success($data);
    }

    public function staffs(Request $request)
    {
        $request->kernel->validate([
            'company_id' => 'required|integer',
            'type'       => 'required|integer|in:1,2,3',
        ], [
            'company_id.required' => '企业id不能为空',
            'company_id.integer'  => '企业id只能是数字',
            'type.required'       => '查看类型不能为空',
            'type.integer'        => '查看类型只能是数字',
            'type.in'             => '查看类型参数不对'
        ]);

        $user       = $request->kernel->user();
        $company_id = $request->company_id;
        $type       = $request->type;
        $manage     = $request->manage;

        $page     = $request->page ?? 1;
        $pageSize = $request->pagesize ?? 10;

        if (! $user->isManagementRole($company_id)) {
            throw new ValidatorException("权限不足");
        }
//
//        $role = CompanyStaff::getRole($user->id, $company_id, 4);
//        if (! $role) {
//            throw new ValidatorException("权限不足");
//        }

        $staffQuery = CompanyStaff::with(['user'])
            ->where('company_id', $company_id)
            ->when($type == 1, function ($query) {
                $query->where('is_work', 1);
            })
            ->when($manage && $manage != 0, function ($query) use ($manage) {
                $query->whereHas('companyDepartmentRoles');
            });

        $staffs = $staffQuery->paginate($pageSize, ['*'], 'page', $page);

        return $request->kernel->success(new CompanyStaffCollection($staffs));
    }

//    public function removeManage(Request $request)
//    {
//        $request->kernel->validate([
//            'uid'        => 'required|integer',
//            'company_id' => 'required|integer|in:1,2,3',
//        ], [
//            'company_id.required' => '企业id不能为空',
//            'company_id.integer'  => '企业id只能是数字',
//            'uid.required'        => '请选择要移除的管理员',
//            'uid.integer'         => '管理员id只能是数字',
//        ]);
//
//        $user = $request->kernel->user();
//
//        $company_id = $request->company_id;
//        $uid        = $request->uid;
//
//        $staff = CompanyStaff::where('uid', $user->id)->where('company_id', $company_id)->first();
//
//        if (! $staff) {
//            throw new ValidatorException("未查询到该企业");
//        }
//
//        if ($staff->is_manage != 2) {
//            throw new ValidatorException("无权限进行该操作");
//        }
//
//        $audienceStaff = CompanyStaff::where('uid', $uid)->where('company_id', $company_id)->first();
//
//        if (! $audienceStaff) {
//            throw new ValidatorException("未查到该用户");
//        }
//
//        $audienceStaff->is_manage  = 0;
//        $audienceStaff->permission = '';
//        $audienceStaff->save();
//
//        return $request->kernel->success([]);
//    }
//
//    public function updateManage(Request $request)
//    {
//        $request->kernel->validate([
//            'uid'          => 'required|integer',
//            'company_id'   => 'required|integer',
//            'updateManage' => 'required',
//        ], [
//            'company_id.required'   => '企业id不能为空',
//            'company_id.integer'    => '企业id只能是数字',
//            'uid.required'          => '请选择要设置的用户',
//            'uid.integer'           => '管理员id只能是数字',
//            'updateManage.required' => '缺少权限',
//        ]);
//
//        $user         = $request->kernel->user();
//        $uid          = $request->uid;
//        $company_id   = $request->company_id;
//        $updateManage = $request->updateManage;
//
//        $staff = CompanyStaff::where('uid', $user->id)->where('company_id', $company_id)->first();
//
//        if (! $staff) {
//            throw new ValidatorException("未查询到该企业");
//        }
//
//        if ($staff->is_manage != 2) {
//            throw new ValidatorException("无权限进行该操作");
//        }
//
//        $audienceStaff = CompanyStaff::where('uid', $uid)->where('company_id', $company_id)->first();
//
//        if (! $audienceStaff) {
//            throw new ValidatorException("未查到该用户");
//        }
//
//        if ($audienceStaff->is_manage != 1) {
//            throw new ValidatorException("该用户不是管理员");
//        }
//
//        $audienceStaff->permission = $updateManage;
//        $audienceStaff->save();
//
//        return $request->kernel->success([]);
//    }
//
//    public function addManage(Request $request)
//    {
//        $request->kernel->validate([
//            'uid'          => 'required|integer',
//            'company_id'   => 'required|integer',
//            'updateManage' => 'required',
//        ], [
//            'company_id.required'   => '企业id不能为空',
//            'company_id.integer'    => '企业id只能是数字',
//            'uid.required'          => '请选择要设置的用户',
//            'uid.integer'           => '管理员id只能是数字',
//            'updateManage.required' => '缺少权限',
//        ]);
//
//        $user         = $request->kernel->user();
//        $uid          = $request->uid;
//        $company_id   = $request->company_id;
//        $updateManage = $request->updateManage;
//
//        $staff = CompanyStaff::where('uid', $user->id)->where('company_id', $company_id)->first();
//
//        if (! $staff) {
//            throw new ValidatorException("未查询到该企业");
//        }
//
//        if ($staff->is_manage != 2) {
//            throw new ValidatorException("无权限进行该操作");
//        }
//
//        $audienceStaff = CompanyStaff::where('uid', $uid)->where('company_id', $company_id)->first();
//
//        if (! $audienceStaff) {
//            throw new ValidatorException("未查到该用户");
//        }
//
//        if ($audienceStaff->is_manage != 0) {
//            throw new ValidatorException("该用户已经是管理员");
//        }
//
//        $audienceStaff->is_manage  = 1;
//        $audienceStaff->permission = $updateManage;
//        $audienceStaff->save();
//
//        return $request->kernel->success([]);
//    }

    public function searchUser(Request $request)
    {
        $request->kernel->validate([
            'company_id' => 'required|integer',
            'phone'      => ['nullable', new MobileRule()],
            'nickname'   => ['nullable', 'min:2', 'max:4'],
        ], [
            'company_id.required' => '企业id不能为空',
            'company_id.integer'  => '企业id只能是数字',
            'nickname.min'        => '姓名不能少于 :min 字',
            'nickname.max'        => '姓名不能大于 :max 字',
        ]);

        $user = $request->kernel->user();

        $company_id = $request->company_id;
        $nickname   = $request->nickname;
        $phone      = $request->phone;

        if (! $nickname && ! $phone) {
            throw new ValidatorException("姓名或者手机号码不能为空");
        }

        $companyUsers = CompanyUser::with(['business'])
            ->where('company_id', $company_id)
            ->when($nickname, function ($query) use ($nickname) {
                $query->where('nickname', 'like', "%{$nickname}%");
            })
            ->when($phone, function ($query) use ($phone) {
                $query->whereHas('business', function ($query) use ($phone) {
                    $query->where('phone', $phone);
                });
            })
            ->get()
            ->map(function ($item) {
                return [
                    'nickname' => $item->business->nickname,
                    'phone'    => $item->business->phone,
                    'uid'      => $item->uid,
                ];
            });
        return $request->kernel->success($companyUsers);
    }

    public function updateOpen(Request $request)
    {
        $request->kernel->validate([
            'company_id' => 'required|integer',
            'uid'        => 'required|integer',
        ], [
            'company_id.required' => '企业id不能为空',
            'company_id.integer'  => '企业id只能是数字',
            'uid.required'        => '请选择要修改的用户',
            'uid.integer'         => '用户id只能是数字',
        ]);

        $user = $request->kernel->user();

        $company_id = $request->company_id;
        $uid        = $request->uid;

//        $role = CompanyStaff::getRole($user->id, $company_id, 4);
//        if (! $role) {
//            throw new ValidatorException("权限不足");
//        }

        if (! $user->isManagementRole($company_id)) {
            throw new ValidatorException("权限不足");
        }

        $staff = CompanyStaff::where('uid', $uid)
            ->where('company_id', $company_id)
            ->first();
        if (! $staff) {
            throw new ValidatorException("未查询到该用户");
        }

        $staff->is_open = $staff->is_open == 0 ? 1 : 0;
        $staff->save();

        return $request->kernel->success([
            'is_open' => $staff->is_open,
        ]);
    }

    public function leaveWork(Request $request)
    {
        $request->kernel->validate([
            'company_id' => 'required|integer',
            'uid'        => 'required|integer',
            'type'       => 'required|in:1,2',
        ], [
            'company_id.required' => '企业ID不能为空',
            'company_id.integer'  => '企业id只能是数字',
            'uid.required'        => '请选择要操作的人员ID',
            'uid.integer'         => '用户id只能是数字',
            'type.required'       => '类型不能为空',
            'type.in'             => '类型错误',
        ]);

        $user = $request->kernel->user();

        $company_id = $request->company_id;
        $uid        = $request->uid;
        $type       = $request->type;

        if (! $user->isManagementRole($company_id)) {
            throw new ValidatorException("权限不足");
        }
        $company = Company::find($company_id);
        if (! $company) {
            throw new ValidatorException('企业不存在');
        }
        if ($company->uid == $uid) {
            throw new ValidatorException('企业主不能操作');
        }
//        $role = CompanyStaff::getRole($user->id, $company_id, 2);
//        if (! $role) {
//            throw new ValidatorException("权限不足");
//        }

        DB::beginTransaction();
        try {
            $companyUser  = CompanyUser::where('uid', $uid)->where('company_id', $company_id)->first();
            $companyStaff = CompanyStaff::where('uid', $uid)->where('company_id', $company_id)->first();
            if (! $companyUser) {
                DB::rollBack();
                throw new ValidatorException("员工信息不存在");
            }

            $business = Business::where('uid', $uid)->where('company_id', $company_id)->first();

            if ($type == 1) {//删除
                CompanyUser::where('uid', $uid)
                    ->where('company_id', $company_id)
                    ->delete();
                if ($business) {
                    Business::where('uid', $uid)
                        ->where('company_id', $company_id)
                        ->update([
                            'is_work'    => 0,
                            'is_company' => 0,
                            'company_id' => 0,
                        ]);
                }
                if ($companyStaff) {
                    CompanyStaff::where('uid', $uid)->where('company_id', $company_id)->delete();
                }
            } elseif ($type == 2) {//离职
                CompanyUser::where('uid', $uid)->where('company_id', $company_id)->update(['is_work' => 1]);
                if ($business) {
                    Business::where('uid', $uid)->where('company_id', $company_id)
                        ->update([
                            'is_work'    => 0,
                            'is_company' => 0,
                            'company_id' => 0,
                        ]);
                }
            } else {
                CompanyUser::where('uid', $uid)->where('company_id', $company_id)->update(['is_work' => 0]);
                if ($business) {
                    Business::where('uid', $uid)->where('company_id', $company_id)
                        ->update([
                            'is_work'    => 1,
                            'is_company' => 1,
                            'company_id' => $company_id,
                        ]);
                }
            }

            DB::commit();
            return $request->kernel->success(null);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ValidatorException($e->getMessage());
        }
    }

    public function qrcode(Request $request)
    {
        $request->kernel->validate([
            //            'code'       => 'required',
            'cid' => 'required|integer',
        ], [
            'company_id.required' => '请选择要生成邀请码的企业id',
            'company_id.integer'  => '企业id只能是数字',
            'code.required'       => '企业邀请码不能为空',
        ]);

        $user = $request->kernel->user();

        $company_id = $request->cid;
        $code       = $request->code;

        if (! $user->isManagementRole($company_id)) {
            throw new ValidatorException("权限不足");
        }

//        $role = CompanyStaff::getRole($userId, $company_id, 2);
//
//        if (! $role) {
//            throw new ValidatorException("权限不足");
//        }

        $company = Company::find($company_id);
        if (! $company->code) {
            throw new ValidatorException("请先设置企业邀请码");
        }
        if (! $company) {
            throw new ValidatorException("企业不存在");
        }

        try {
            $size       = $request->size ?? '430';
            $envVersion = $request->version ?? 'release';

            $app  = app('wechat.mini');
            $name = md5('company'.$company_id).'.png';
            $path = "share/company/{$envVersion}/{$name}";
            if (! Storage::has($path)) {
                $response = $app->getClient()->postJson('/wxa/getwxacodeunlimit', [
                    'scene'       => http_build_query([
                        'cid'  => $company_id,
                        'code' => $code,
                    ]),
                    'page'        => 'pages/authentication/job',
                    'width'       => $size,
                    'is_hyaline'  => true,
                    'env_version' => $envVersion,
                    'check_path'  => false,
                ]);
                Storage::put($path, $response->getContent());
            }

            return $request->kernel->success([
                'qrcode_url' => Storage::url($path),
                'qrcode'     => "data:image/png;base64,".base64_encode(Storage::get($path))
            ]);
        } catch (Exception $e) {
            throw new ValidatorException($e->getMessage());
        }
    }

    public function manual(Request $request)
    {
        $addUserRequest = new AddUserRequest();
        $request->kernel->validate($addUserRequest->rules(), $addUserRequest->messages());

        $userId = $request->kernel->id();
        $user   = $request->kernel->user();

        $company_id      = $request->company_id;
        $nickname        = $request->nickname;
        $wechat          = $request->wechat;
        $phone           = $request->phone;
        $position        = $request->position;
        $email           = $request->email;
        $address         = $request->address;
        $wiki            = $request->wiki;
        $industry        = $request->industry;
        $company_user_id = $request->cid;//company_user id

        $company = Company::find($company_id);
        if (! $company) {
            throw new ValidatorException("未查询到对应的企业信息");
        }

        if (! $user->isManagementRole($company_id)) {
            throw new ValidatorException("权限不足");
        }

//        $role = CompanyStaff::getRole($userId, $company_id, 2);
//        if (! $role) {
//            throw new ValidatorException("权限不足");
//        }

        DB::beginTransaction();
        try {
            //检查用户是否存在
            $userInfo = User::where('username', $phone)->first();

            $companyUserInfo = [
                'uid'        => $user->id,
                'company_id' => $company->id,
                'nickname'   => $nickname,
                'position'   => $position,
                'is_check'   => 1,
                'is_work'    => 0,
                'type'       => 5,
            ];

            if (isset($company_user_id) && $company_user_id != 0) {
                $companyUser = CompanyUser::find($company_user_id);
                if (! $companyUser) {
                    throw new ValidatorException("该用户信息不存在");
                }
                if ($companyUser->company_id != $company_id) {
                    throw new ValidatorException("该用户不属于此公司");
                }

                $companyUser->update([
                    'uid' => $userId,
                ]);
            } else {
                if (! $userInfo) {
                    //创建用户
                    $userInfo = User::create([
                        'username' => $phone,
                        'password' => substr($phone, -4),
                    ]);

                    $userInfo->info->update([
                        'nickname' => $nickname,
                        'avatar'   => Storage::url('/avatar/'.rand(100, 174).'.jpg'),
                    ]);
                }

                $businessInfo = [
                    'phone'        => $phone,
                    'wechat'       => $wechat,
                    'position'     => $position,
                    'email'        => $email ?? '',
                    'address'      => $address ?? '',
                    'wiki'         => $wiki ?? '',
                    'uid'          => $userInfo->id,
                    'industry'     => $industry ?? '',
                    'nickname'     => $nickname,
                    'company_id'   => $company->id,
                    'avatar'       => $userInfo->info->avatar,
                    'company_name' => $company->company_name,
                    'is_work'      => 1,
                    'is_company'   => 1,
                ];

                $businessExists = Business::where('uid', $userInfo->id)
                    ->where('is_default', 1)
                    ->exists();

                if (! $businessExists) {
                    $businessInfo['is_default'] = 1;
                }

                Business::where('phone', $phone)
                    ->update([
                        'company_id' => $company->id,
                        'is_company' => $company->is_check,
                    ]);

                $business               = Business::create($businessInfo);
                $companyUserInfo['uid'] = $userInfo->id;
                $companyUserInfo['bid'] = $business->id;

                CompanyUser::create($companyUserInfo);

                DB::commit();
                return $request->kernel->success(null);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ValidatorException($e->getMessage());
        }
    }

    public function uploadExcel(Request $request)
    {
        $request->kernel->validate([
            'link'       => 'required',
            'company_id' => 'required|integer',
        ], [
            'link.required'       => '请输入要读取的文件地址',
            'company_id.required' => '缺少企业id',
            'company_id.integer'  => '企业id只能是数字'
        ]);

        $link       = $request->link;
        $company_id = $request->company_id;

        $user    = $request->kernel->user();
        $company = Company::find($company_id);

        if (! $company) {
            throw new ValidatorException('企业不存在');
        }

        if ($company->uid != $user->id) {
            throw new ValidatorException('您没有权限');
        }
        $fileContents = file_get_contents($link, false, stream_context_create([
            'http' => [
                'method' => 'GET',
            ],
        ]));

        if ($fileContents === false) {
            throw new ValidatorException("无法读取远程文件");
        }

        $tmpFile = tempnam(sys_get_temp_dir(), 'excel');
        file_put_contents($tmpFile, $fileContents);

        $rowData = excel($tmpFile);

        $num     = 0;
        $imUsers = [];
        foreach ($rowData as $k => &$v) {
            $phone = $v[1];
            $phone = $phone ? Str::replace('.0', '', $phone) : '';

            $info = [
                'nickname'     => $v[0] ?? '',
                'phone'        => $phone,
                'company_name' => $company->company_name,
                'position'     => $v[2] ?? '',
                'industry'     => $v[3] ?? '',
                'wechat'       => $v[4] ?? '',
                'email'        => $v[5] ?? '',
                'address'      => $v[6] ?? '',
                'status'       => 1,
                'message'      => '正常',
            ];

            $batchAddUserRequest = new BatchAddUserRequest();

            $validator = Validator::make($info,
                $batchAddUserRequest->rules(),
                $batchAddUserRequest->messages()
            );

            if ($validator->fails()) {
                $errors          = $validator->errors()->all();
                $info['message'] = implode(",", $errors);
                $info['status']  = 0;
            }

            $imUsers[] = $info;
        }
        DB::beginTransaction();
        try {
            $errors        = collect($imUsers)->where('status', 0)->all();
            $success_count = 0;
            foreach ($imUsers as $import) {
                if ($import['status'] == 1) {
                    $user = User::query()
                        ->firstOrCreate([
                            'username' => $import['phone'],
                        ]);
                    $user->info->update([
                        'nickname' => $import['nickname'],
                    ]);
                    $business = Business::create([
                        'uid'          => $user->id,
                        'nickname'     => $import['nickname'],
                        'company_name' => $import['company_name'],
                        'company_id'   => $company->id,
                        'position'     => $import['position'],
                        'phone'        => $import['phone'],
                        'industry'     => $import['industry'] ?? '',
                        'avatar'       => $user->info->avatar_url,
                        'is_work'      => 1,
                        'is_company'   => 1,
                    ]);

                    CompanyUser::updateOrCreate([
                        'uid'        => $user->id,
                        'company_id' => $company->id,
                    ], [
                        'nickname' => $import['nickname'],
                        'position' => $import['position'],
                        'type'     => 2,
                        'is_check' => 1,
                        'bid'      => $business->id,
                    ]);

                    CompanyStaff::updateOrCreate([
                        'uid'        => $user->id,
                        'company_id' => $company->id,
                    ], [
                        'permission' => '2',
                        'is_work'    => 0,
                        'is_open'    => 0,
                        'type'       => 0
                    ]);
                    $success_count++;
                }
            }
            DB::commit();

            $total       = count($imUsers);
            $error_count = count($errors);

            $data['total']         = $total;
            $data['error_count']   = $error_count;
            $data['success_count'] = $success_count;
            $data['list']          = $errors;

            return $request->kernel->success($data);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 审核用户
     *
     * @Author: 玄尘
     * @Date: 2024/12/23 10:53
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function checkUser(Request $request)
    {
        $request->kernel->validate([
            'ids'        => 'required',
            'company_id' => 'required|integer',
            'status'     => 'required|in:1,2',
        ], [
            'company_id.required' => '企业ID不能为空',
            'company_id.integer'  => '企业id只能是数字',
            'code.required'       => '企业邀请码不能为空',
            'ids.required'        => '请选择要审核的用户',
            'status.required'     => '请选择同意或者拒绝',
            'status.in'           => 'status参数错误',
        ]);

        $user       = $request->kernel->user();
        $ids        = $request->ids;
        $company_id = $request->company_id;
        $status     = $request->status;//1通过 2拒绝

        $companyUserIds = explode(',', $ids);

        if (! $user->isManagementRole($company_id)) {
            throw new ValidatorException('您没有权限');
        }

//        $role = CompanyStaff::getRole($userId, $company_id, 3);
//        if (! $role) {
//            throw new ValidatorException("权限不足");
//        }

        $company = Company::find($company_id);

        if (! $company) {
            throw new ValidatorException("没有查询到该公司");
        }

        if ($company->is_check != 1) {
            throw new ValidatorException("没有查询到该公司");
        }

        $companyUsers = CompanyUser::whereIn('id', $companyUserIds)
            ->where('company_id', $company_id)
            ->get();

        DB::beginTransaction();
        try {
            foreach ($companyUsers as $companyUser) {
                CompanyUser::where('uid', $companyUser->uid)
                    ->where('company_id', $company_id)
                    ->update([
                        'is_check' => $status,
                    ]);
            }
            if ($status == 1) {
                $businessInfo = [
                    'is_work'    => 0,
                    'is_company' => 0,
                ];
            } else {
                $businessInfo = [
                    'is_work'    => 1,
                    'is_company' => 1,
                ];
            }
            foreach ($companyUsers as $k => $companyUser) {
                $companyUser->business()->update($businessInfo);
            }
            DB::commit();

            return $request->kernel->success(true);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 申请人详情
     *
     * @Author: 玄尘
     * @Date: 2024/12/6 17:30
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function userInfo(Request $request)
    {
        $request->kernel->validate([
            'id'         => 'required|integer',
            'company_id' => 'required|integer',
        ], [
            'company_id.required' => '企业ID不能为空',
            'company_id.integer'  => '企业id只能是数字',
            'code.required'       => '企业邀请码不能为空',
            'id.required'         => '请选择要查看的申请用户',
            'id.integer'          => '用户id只能是数字',
        ]);

        $user          = $request->kernel->user();
        $companyUserId = $request->id;
        $company_id    = $request->company_id;

        if (! $user->isManagementRole($company_id)) {
            throw new ValidatorException("权限不足");
        }
//        $role = CompanyStaff::getRole($userId, $company_id, 3);
//        if (! $role) {
//            throw new ValidatorException("权限不足");
//        }

        $companyUser = CompanyUser::find($companyUserId);
        if (! $companyUser) {
            throw new ValidatorException("未查询到申请信息");
        }

        if ($companyUser->is_check != 0) {
            throw new ValidatorException("状态错误");
        }

        $user     = $companyUser->user;
        $business = $companyUser->business;

        return $request->kernel->success([
            'nickname'     => $companyUser->nickname,
            'phone'        => $business->phone,
            'wechat'       => $business->wechat ?? "",
            'avatar'       => $business->avatar,
            'position'     => $companyUser->position,
            'created_time' => (string) $companyUser->created_at,
            'openid'       => $user->wechat->mini_openid ?? '',
            'type'         => $companyUser->type,
            'link'         => $companyUser->link ?? ""
        ]);
    }

    /**
     * Notes: 转让管理员
     *
     * @Author: 玄尘
     * @Date: 2024/12/23 11:01
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function transference(Request $request)
    {
        $request->kernel->validate([
            'uid'        => 'required|integer',
            'company_id' => 'required|integer',
        ], [
            'company_id.required' => '企业ID不能为空',
            'company_id.integer'  => '企业id只能是数字',
            'code.required'       => '企业邀请码不能为空',
            'uid.required'        => '用户id不能为空',
            'uid.integer'         => '用户id只能是数字',
        ]);

        $user = $request->kernel->user();

        $company_id = $request->company_id;
        $uid        = $request->uid;

        if (! $user->isAdministrator($company_id)) {
            throw new ValidatorException("没有权限执行转让操作");
        }
//
//        $companyUser = CompanyUser::where('uid', $userId)
//            ->where('company_id', $company_id)
//            ->where('is_work', 0)
//            ->where('is_manage', 2)
//            ->first();
//
//        if (! $companyUser) {
//            throw new ValidatorException("没有权限执行转让操作");
//        }

        DB::beginTransaction();
        try {
            $toUser = CompanyUser::where('uid', $uid)
                ->where('company_id', $company_id)
                ->first();
            if (! $toUser) {
                throw new ValidatorException("未找到用户信息");
            }

            if ($toUser->is_check != 1) {
                throw new ValidatorException("用户不可成为管理员");
            }

            if ($toUser->is_work == 1) {
                throw new ValidatorException("用户已离职");
            }

            $user->companyDepartmentRoles()->where('company_id', $company_id)->delete();//删除权限
            $administratorRole = CompanyRole::where('label', 'administrator')->first();

            //增加权限
            CompanyDepartmentRole::create([
                'company_id'      => $company_id,
                'company_role_id' => $administratorRole->id,
                'department_id'   => 0,
            ]);

            DB::commit();
            return $request->kernel->success(true);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ValidatorException($e->getMessage());
        }
    }
}
