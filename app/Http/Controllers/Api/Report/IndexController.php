<?php

namespace App\Http\Controllers\Api\Report;

use App\Http\Controllers\Controller;
use App\Http\Resources\Report\ReportItemResource;
use App\Http\Resources\Report\ReportListCollection;
use App\Models\ReportCenter;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class IndexController extends Controller
{
    public function store(Request $request)
    {
        $request->kernel->validate([
            'model'   => ['required', Rule::in(['user', 'unify_asset'])],
            'id'      => 'required',
            'message' => 'required',
            //            'pictures' => 'required',
        ], [
            'model.required'    => '举报对象模型错误',
            'model.in'          => '举报对象模型错误',
            'id.required'       => '举报对象ID错误',
            'message.required'  => '请输入举报内容',
            'pictures.required' => '请上传举报图片',
        ]);

        $pictures = $request->pictures ?? [];
        if ($pictures) {
            $pictures = explode(',', $pictures);
            if (count($pictures) > 6) {
                return $request->kernel->error('最多上传6张图片');
            }
        }

        $user = $request->kernel->user();

        $report = ReportCenter::create([
            'user_id'        => $user->id,
            'modelable_type' => $request->model,
            'modelable_id'   => $request->id,
            'report_content' => $request->message,
            'pictures'       => $pictures,
        ]);
        return $request->kernel->success(new ReportItemResource($report));
    }

    public function lists(Request $request)
    {
        $user    = $request->kernel->user();
        $reports = ReportCenter::ofUser($user)
            ->paginate($request->pagesize ?: 10);
        return $request->kernel->success(new ReportListCollection($reports));
    }

    public function detail(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required|exists:report_centers,id',
        ], [
            'id.required' => '参数错误',
            'id.exists'   => '内容不存在',
        ]);
        $report = ReportCenter::find($request->id);
        return $request->kernel->success(new ReportItemResource($report));
    }

    public function delete(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required|exists:report_centers,id',
        ], [
            'id.required' => '参数错误',
            'id.exists'   => '内容不存在',
        ]);
        $report = ReportCenter::find($request->id);
        $user   = $request->kernel->user();
        if ($report->user_id != $user->id) {
            return $request->kernel->error('无权限删除');
        }
        $report->delete();
        return $request->kernel->success([], '操作成功');
    }
}