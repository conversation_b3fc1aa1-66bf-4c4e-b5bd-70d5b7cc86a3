<?php

namespace App\Http\Controllers\Api\Department;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Requests\Department\AddDepartmentRequest;
use App\Http\Requests\Department\UpdateDepartmentRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\User\Http\Resources\DepartmentCollection;
use Modules\User\Http\Resources\DepartmentResource;
use Modules\User\Models\Department;

class IndexController extends ApiController
{
    public function index(Request $request): JsonResponse
    {
        $user = $request->kernel->user();

        $company = $user->company;

        $list = Department::ofEnabled()
            ->where('company_id', $company->id)
            ->where('parent_id', 0)
            ->ordered()
            ->get();

        return $request->kernel->success(DepartmentCollection::collection($list));
    }

    public function show(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'department_id' => 'required|integer'
        ], [
            'department_id.required' => '缺少部门id'
        ]);

        $department = Department::find($request->department_id);
        if (! $department->status) {
            return $this->failed('部门不存在', 404);
        }

        return $this->success(new DepartmentResource($department));
    }

    /**
     * Notes: 添加部门
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 09:47
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request): JsonResponse
    {
        $requestRule = new AddDepartmentRequest();

        $request->kernel->validate($requestRule->rules(), $requestRule->messages());

        $user        = $request->kernel->user();
        $name        = $request->input('name');
        $description = $request->input('description', '');

        if (! $user->isManagementRole()) {
            throw new ValidatorException('您没有权限');
        }

        Department::create([
            'name'                => $name,
            'remark'              => $description,
            'company_id'          => $user->company->id,
            'parent_id'           => $request->parent_id ?? 0,
            'is_business'         => $request->input('is_business'),
            'is_assistant'        => $request->input('is_assistant'),
            'is_update_knowledge' => $request->input('is_update_knowledge'),
            'is_watermark'        => $request->input('is_watermark'),
            'is_download'         => $request->input('is_download'),
            'is_password'         => $request->input('is_password'),
            'allow_user'          => 1,
            'status'              => 1,
        ]);

        return $this->success('添加成功');
    }

    public function update(Request $request): JsonResponse
    {
        $updateDepartmentRule = new UpdateDepartmentRequest();
        $request->kernel->validate($updateDepartmentRule->rules(), $updateDepartmentRule->messages());

        $user         = $request->kernel->user();
        $departmentId = $request->input('department_id');
        $name         = $request->input('name');
        $description  = $request->input('description', '');

        $department = Department::find($departmentId);
        if (! $department) {
            throw new ValidatorException('部门不存在');
        }

        if (! $user->isManagementRole($department->company_id)) {
            throw new ValidatorException('您没有权限');
        }

        $data = [
            'name'                => $name,
            'parent_id'           => $request->input('parent_id', 0),
            'is_business'         => $request->input('is_business', 0),
            'is_assistant'        => $request->input('is_assistant', 0),
            'is_update_knowledge' => $request->input('is_update_knowledge', 0),
            'is_watermark'        => $request->input('is_watermark', 0),
            'is_download'         => $request->input('is_download', 0),
            'is_password'         => $request->input('is_password', 0),
        ];
        if ($description) {
            $data['remark'] = $description;
        }

        $department->update($data);

        return $request->kernel->success('修改成功');
    }

    public function delete(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'department_id' => 'required|integer',
        ], [
            'department_id.required' => '部门名称必须填写',
        ]);

        $user = $request->kernel->user();

        $departmentId = $request->input('department_id');

        $department = Department::withCount(['users'])->find($departmentId);
        if (! $department) {
            throw new ValidatorException('部门不存在');
        }

        if (! $user->isManagementRole($department->company_id)) {
            throw new ValidatorException('您没有权限');
        }

        if ($department->users_count > 0) {
            throw new ValidatorException('部门有人不可删除');
        }

        $department->delete();

        return $request->kernel->success('删除成功');
    }

}