<?php

namespace App\Http\Controllers\Api\AiSearch;

use App\Http\Controllers\Controller;
use App\Models\AiSearchHotNews;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class IndexV2Controller extends Controller
{

    public function getHot(Request $request)
    {
        $category   = $request->category ?: '';
        $categories = explode(',', $category);
        $list       = AiSearchHotNews::query()
            ->when(! empty($categories), function (Builder $builder) use ($categories) {
                $builder->whereIn('category', $categories);
            })
            ->orderByDesc('created_at')
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->limit(10)
            ->get();

        return $request->kernel->success($list, '获取成功');
    }
}
