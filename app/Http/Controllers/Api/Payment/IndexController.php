<?php

namespace App\Http\Controllers\Api\Payment;

use App\Http\Controllers\Controller;
use App\Models\RechargeOrder;
use App\Models\RechargePackage;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Modules\Payment\Models\Payment;

class IndexController extends Controller
{

    public function newPackage(Request $request)
    {
        $lists = RechargePackage::ofEnabled()
            ->withCount([
                'orders' => function (Builder $query) {
                    return $query->whereIn('status', [RechargeOrder::STATUS_PAID, RechargeOrder::STATUS_SETTLE]);
                }
            ])
            ->orderBy('price')
            ->get()
            ->map(function ($rechargePackage) {
                return [
                    'id'           => $rechargePackage->id,
                    'title'        => $rechargePackage->title,
                    'price'        => $rechargePackage->price,
                    'market_price' => $rechargePackage->price,
                    'num'          => $rechargePackage->score,
                    'gift'         => $rechargePackage->gift,
                    'base'         => $rechargePackage->base,
                    'hint'         => $rechargePackage->hint,
                    'desc'         => $rechargePackage->remark,
                    'sales'        => $rechargePackage->orders_count ?: 0,
                    'is_default'   => $rechargePackage->is_default,
                ];
            })
            ->toArray();
        return $request->kernel->success($lists);
    }

    public function package(Request $request)
    {
        $lists = RechargePackage::ofEnabled()
            ->withCount([
                'orders' => function (Builder $query) {
                    return $query->whereIn('status', [RechargeOrder::STATUS_PAID, RechargeOrder::STATUS_SETTLE]);
                }
            ])
            ->orderBy('price')
            ->get()
            ->map(function ($rechargePackage) {
                return [
                    'id'           => $rechargePackage->id,
                    'title'        => $rechargePackage->title,
                    'price'        => $rechargePackage->price,
                    'market_price' => $rechargePackage->price,
                    'num'          => $rechargePackage->score,
                    'hint'         => $rechargePackage->hint,
                    'desc'         => $rechargePackage->remark,
                    'sales'        => $rechargePackage->orders_count ?: 0,
                    'is_default'   => $rechargePackage->is_default,
                ];
            })
            ->toArray();
        return $request->kernel->success($lists);
    }

    public function checkStatus(Request $request)
    {
        $request->kernel->validate([
            'order_no' => ['required', 'exists:payments,no'],
        ], [
            'order_no.required' => "请传入支付单号",
            'order_no.exists'   => "支付单不存在",
        ]);
        $payment = Payment::where('no', $request->order_no)->first();
        return $request->kernel->success([
            'is_pay' => $payment->isPaid() ? 1 : 0,
        ]);
    }
}