<?php

namespace App\Http\Controllers\Api\Payment;

use App\Http\Controllers\Controller;
use App\Models\Card;
use Illuminate\Http\Request;

class CardController extends Controller
{
    public function index(Request $request)
    {
        $code = $request->code ?: '';
        if ($code) {
            $card = Card::where('no', $code)->first();
            if (! $card || $card->status == Card::STATUS_FAILURE) {
                return $request->kernel->error('卡号不存在或已被使用');
            }
            if ($card->status == Card::STATUS_FAILURE) {
                return $request->kernel->error('此卡已过期');
            }
            return $request->kernel->success([
                'id'         => $card->id,
                'code'       => $card->no,
                'score'      => $card->score,
                'cans'       => [
                    'exchange' => $card->status == Card::STATUS_INIT,
                ],
                'status'     => [
                    'code' => $card->status,
                    'text' => $card->status_text,
                ],
                'failure_at' => $card->failure_at ? $card->failure_at->toDateString() : '',
            ]);
        } else {
            return $request->kernel->error('请输入卡号');
        }
    }

    public function exchange(Request $request)
    {
        $user = $request->kernel->user();
        $request->kernel->validate([
            'code'   => 'required',
            'secret' => 'required',
        ], [
            'code.required'   => '请传入卡号',
            'secret.required' => '请输入卡密',
        ]);
        $card = Card::where('no', $request->code)
            ->where('secret', $request->secret)
            ->first();
        if (! $card) {
            return $request->kernel->error('卡密错误');
        }
        if ($card->status == Card::STATUS_USED) {
            return $request->kernel->error('兑换卡已被使用');
        }
        if ($card->status == Card::STATUS_FAILURE) {
            return $request->kernel->error('兑换卡已过期');
        }
        $card->useDo($user);
        return $request->kernel->success([
            'code'  => $card->no,
            'score' => $card->score,
        ], '兑换成功');
    }
}