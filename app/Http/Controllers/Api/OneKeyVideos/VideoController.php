<?php

namespace App\Http\Controllers\Api\OneKeyVideos;

use App\Http\Controllers\Controller;
use App\Packages\OneKeyVideo\OneKeyVideo;

use Illuminate\Http\Request;

class VideoController extends Controller
{
    public function create(Request $request)
    {
        $request->kernel->validate([
            'prompts' => 'required',
            'image' => 'required'
        ], [
            'prompts.required' => '请输入描述词',
            'image.required' => '请上传图片'
        ]);
        $result = OneKeyVideo::Video()->createVideo($request->prompts, $request->image);
        return $request->kernel->success(['tackid' => $result['task_id']], '创建成功');
    }

    public function lunxun(Request $request){
        $result = OneKeyVideo::Video()->lunxun($request->tackid);
        $status = $result['status'];
        $message = "";
        if($status == 'Preparing'){
            $message = "准备中";
        }elseif($status == 'Queueing'){
            $message = "队列中";
        }elseif($status == 'Queueing'){
            $message = "队列中";
        }elseif($status == 'Processing'){
            $message = "生成中";
        }elseif($status == 'Success'){
            $message = "生成成功";
        }else{
            $message = "生成失败";
        }
        return $request->kernel->success(['data' => $result], $message);
    }

    public function download(Request $request){
        $result = OneKeyVideo::Video()->download($request->tackid);
        return $request->kernel->success(['data' => $result], "success");
    }
}
