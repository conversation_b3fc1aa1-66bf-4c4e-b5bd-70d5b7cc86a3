<?php

namespace App\Http\Controllers\Api\OneKeyVideos;

use App\Http\Controllers\Controller;
use App\Models\SystemConfig;
use App\Packages\OneKeyVideo\OneKeyVideo;
use Illuminate\Http\Request;

class IndexController extends Controller
{
    public function create(Request $request)
    {
        $request->kernel->validate([
            'prompts' => 'required'
        ], [
            'prompts.required' => '请输入描述词'
        ]);
        $cid = OnekeyVideo::Images()->getCid($request);
        $width = 768;
        $height = 1024;
        $refine = OneKeyVideo::Images()->refine($request->prompts,$cid);
        $query = [
            "source"=>3,
            "adetailerEnable"=>0,
            "mode"=>1,
            "uuid"=>"fe57d488-bf0a-46e1-9bb7-21edc6bd1744",
            "projectData"=>[
                "flowId"=>"",
                "baseType"=>19,
                "presetBaseModelId"=>"base_checkpoint",
                "hiresOptions"=>[
                    "enabled"=>false,
                    "scale"=>1.5,
                    "upscaler"=>"16",
                    "strength"=>0.5,
                    "steps"=>20
                ],
                "imageReference"=>null,
                "prompt"=>$request->prompts,
                "negativePrompt"=>"",
                "presetNegativePrompts"=>[],
                "checkpoint"=>null,
                "loraModels"=>[],
                "addOns"=>[],
                "count"=>1,
                "width"=>$width,
                "height"=>$height,
                "icon"=>[9,12],
                "isFixedRatio"=>false,
                "controlnets"=>[
                    [
                        "id"=>"b2f8151e-2779-4aef-b0f0-10b48a9f52b9",
                        "type"=>"depth",
                        "sourceImage"=>null,
                        "previewImage"=>null,
                        "controlWeight"=>0.7,//景深
                        "controlMode"=>0,
                        "resizeMode"=>1,
                        "startingControlStep"=>0,
                        "endingControlStep"=>0.3,
                        "preprocessorResolution"=>512,
                        "pixelPerfect"=>1,
                        "downSamplingRate"=>1,
                        "sharpness"=>1,
                        "variation"=>8,
                        "sigma"=>9,
                        "lowThreshold"=>100,
                        "highThreshold"=>100,
                        "valueThreshold"=>0.1,
                        "distanceThreshold"=>0.1,
                        "safeSteps"=>2,
                        "removeNear"=>0,
                        "removeBackground"=>0,
                        "backgroundThreshold"=>0.4,
                        "XDOGThreshold"=>32,
                        "styleFidelity"=>0.5,
                        "gammaCorrection"=>1,
                        "referenceType"=>"preset",
                        "faceType"=>"front",
                        "bodyType"=>"halfbody",
                        "preprocessor"=>2,
                        "model"=>2495516]
                ],
                "samplerMethod"=>"1",
                "samplingSteps"=>30,
                "seedType"=>"0",
                "seedNumber"=>"",
                "vae"=>"-1",
                "cfgScale"=>3.5,
                "clipSkip"=>2,
                "colorControl"=>["colorPalette"=>[]],
                "presetBaseModel"=>[
                    [
                        "id"=>"base_checkpoint",
                        "displayName"=>"星流 Star-3 Alpha",
                        "imageBanner"=>"https=>//liblibai-online.liblib.cloud/img/********************************/082a12a33a725650959c049d1258cb6ec6756be4d385f32457f4fcc8b289806a.png",
                        "imageVertical"=>"https=>//liblibai-online.liblib.cloud/img/********************************/2208db2f837133a8c940448af5be0b7e5f78a57a7855058780b1966885d8503c.png",
                        "imageUrl"=>"https=>//liblibai-online.liblib.cloud/img/********************************/bf9f7859681046b1f279b7eb1c5da01a048a1b66062b0dc3953ebc9aede28076.png",
                        "imageIcon"=>"https=>//liblibai-online.liblib.cloud/img/********************************/f0097b0fd89f6a12688964ae175e52f36b2a04f96f2a014dc26b8ccb53a26a78.png",
                        "imageIcon2"=>"https=>//liblibai-online.liblib.cloud/public/xingliu.jpg",
                        "versionDetail"=>[
                             "baseType"=>"FLUX.1",
                             "name"=>"Star-3",
                             "modelId"=>16932777,
                             "modelType"=>"checkpoint",
                             "modelUuid"=>"8b86d942adc241228900c7c5f0a47ad9",
                             "versionId"=>18592945,
                             "versionIdInpaint"=>18532056,
                             "uuid"=>"eaac694155ec464e8120207f555b054a",
                             "vae"=>"",
                             "samplerMethod"=>"1",
                             "samplingSteps"=>30
            ]
                    ]
                ],
                "lockRatio"=>false,
                "unitId"=>"node-generator-m5f8m70h",
                "unitLabel"=>"生成器",
                "mode"=>1,
                "isSimpleMode"=>true,
                "generateType"=>"normal",
                "promptRecommend"=>true,
                "renderWidth"=>$width,
                "renderHeight"=>$height,
                "samplerMethodName"=>"Euler",
                "extendedPrompt"=> $refine['data']
            ],
            "vae"=>"",
            "taskQueuePriority"=>1,
            "checkpointId"=>18592945,
            "additionalNetwork"=>[],
            "generateType"=>21,
            "coreParamsField"=>"text2img",
            "text2img"=>[
                "checkPointName"=>18592945,
                "prompt"=>$refine['data'],
                "negPrompt"=>",bad quality, poor quality, doll, disfigured, jpg, toy, bad anatomy, missing limbs, missing fingers, 3d, cgi",
                "promptRecommend"=>false,
                "seed"=>-1,
                "samplingMethod"=>"1",
                "samplingStep"=>30,
                "imgCount"=>1,
                "cfgScale"=>3.5,
                "clipSkip"=>2,
                "width"=>$width,
                "height"=>$height,
                "tiling"=>0,
                "randnSource"=>1,
                "restoreFaces"=>0,
                "hiResFix"=>0,
                "seedExtra"=>0
            ],
            "subTasks"=>null,
            "customMade"=>1001,
            "cid"=> $cid// SystemConfig::getValue('OneKeyVideoCid')??"1735879303219prfuwlgv"
        ];
        $result = OneKeyVideo::Images()->createImage($query);

        $tackID = $result['data'];
        return $request->kernel->success(['tackid' => $tackID], '创建成功');
    }

    public function lunxun(Request $request){
        $result = OneKeyVideo::Images()->lunxun($request->tackid,OnekeyVideo::Images()->getCid($request));
        return $request->kernel->success(['result' => $result], '查询成功');
    }
}
