<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\CompanyUser;
use Illuminate\Http\Request;

class WorkCardController extends Controller
{
    public function index(Request $request)
    {
        if (empty($request->bid)) {
            return $request->kernel->error('名片ID不能为空');
        }
        $business = Business::where(['id' => $request->bid, 'is_deleted' => 0])->first();
        // 0 没有提交认证  1 待审核 2 通过 3 拒绝
        if ($business) {
            if ($business['is_work'] == 1) {
                $map['status'] = 2;
            } else {
                $companyUser = CompanyUser::where(['bid' => $business['id'], 'is_deleted' => 0])->first();
                if ($companyUser) {
                    if ($companyUser['is_check'] == 0) {
                        $map           = $companyUser;
                        $map['status'] = 1;
                    } elseif ($companyUser['is_check'] == 1) {
                        $map['status'] = 2;
                    } else {
                        $map           = $companyUser;
                        $map['status'] = 3;
                    }
                } else {
                    $map['status'] = 0;
                }
            }
            return $request->kernel->success($map);
        }
        return $request->kernel->success([], '名片不存在');
    }
}
