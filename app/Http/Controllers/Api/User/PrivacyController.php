<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserPrivacy;
use Carbon\Carbon;
use Illuminate\Http\Request;

class PrivacyController extends Controller
{
    public function browse(Request $request)
    {
        $uid     = $request->kernel->id();
        $privacy = UserPrivacy::where(['uid' => $uid])->first();
        if (! $privacy) {
            $privacy = UserPrivacy::create([
                'uid' => $uid
            ]);
        }
        return $request->kernel->success($privacy);
    }

    public function setRank(Request $request)
    {
        $type = $request->type;
        $data = $request->validate([
            'type' => 'required|string',
        ], [
            'type.required' => '请选择要设置的条件',
            'type.string'   => '参数错误',
        ]);

        if (! in_array($type, ['search', 'rank'])) {
            return $request->kernel->error("参数错误");
        }

        switch ($data['type']) {
            case "search":
                $type = "is_search";
                break;
            case "rank":
                $type = "is_rank";
                break;
        }
        $uid     = $request->kernel->id();
        $privacy = UserPrivacy::where(['uid' => $uid])->first();
        if (! $privacy) {
            $privacy = UserPrivacy::create([
                'uid' => $uid,
            ]);
        }
        $value = 1;
        if ($privacy[$type] == 1) {
            $value = 0;
        }
        UserPrivacy::where(['uid' => $uid])->update([$type => $value]);
        return $request->kernel->success([$type => $value], '设置成功');
    }

    public function setting(Request $request)
    {
        if (blank($request->type)) {
            return $request->kernel->error("请选择要设置的条件");
        }
        $typeArray = ['company_name', 'nickname', 'phone', 'wechat', 'address'];
        if (! in_array($request->type, $typeArray)) {
            return $request->kernel->error("参数错误");
        }
        $type = $request->type;

        $uid = $request->kernel->id();
        try {
            $info = UserPrivacy::where(['uid' => $uid])->select($type)->first();
        } catch (\Exception $e) {
            return $request->kernel->error("用户信息不存在");
        }
        if ($info->$type == 1) {
            $status = 0;
        } else {
            $status = 1;
        }
        $res = UserPrivacy::where(['uid' => $uid])->update([$type => $status]);
        if ($res) {
            return UserPrivacy::where(['uid' => $uid])->first();
        }
        return $request->kernel->error("设置失败");
    }
}
