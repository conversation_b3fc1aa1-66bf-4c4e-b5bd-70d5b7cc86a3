<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Packages\WateCheckPhone;
use Illuminate\Http\Request;
use Modules\User\Rules\IdCardRule;
use Modules\User\Rules\MobileRule;

class RealnameController extends Controller
{
    public function verify_realname(Request $request)
    {
        $request->kernel->validate([
            'phone'    => [
                'required',
                new MobileRule()
            ],
            'code'     => 'required',
            'nickname' => 'required',
            'card_num' => [
                'required',
                new IdCardRule()
            ]
        ], [
            'phone.required'    => '手机号码不能为空',
            'phone.mobile'      => '手机号码格式不正确',
            'code.required'     => '验证码不能为空',
            'nickname.required' => '姓名不能为空',
            'card_num.required' => '身份证号码不能为空',
        ]);

        $mobile = $request->phone;
        $type   = $request->type ?? 1;
        $code   = $request->code;
        WateCheckPhone::checkPhone($mobile, $type, $code);

        $user = $request->kernel->user();
        $data = alibaba(
            url: 'https://mobilecert.market.alicloudapi.com/mobile3Meta',
            paramsType: 'query',
            params: [
                'identifyNum'    => $request->card_num,
                'identifyNumMd5' => md5($request->card_num),
                'mobile'         => $request->phone,
                'mobileMd5'      => md5($request->phone),
                'userName'       => $request->nickname,
                'userNameMd5'    => md5($request->nickname),
            ],
        );
        if ($data['bizCode'] != '1') {
            $message = match ($data['subCode']) {
                '101' => '验证通过',
                '301' => '查⽆记录',
                '201' => '手机号和姓名不一致，手机号和证件号不一致',
                '202' => '⼿机号和姓名⼀致，⼿机号和证件号不⼀致',
                '203' => '⼿机号和证件号⼀致，⼿机号和姓名不⼀致',
                '204' => '其他不⼀致',
                default => '未知错误',
            };
            return $request->kernel->error($message);
        }
        $res = $user->realName()
            ->updateOrCreate([
                'uid' => $user->id,
            ], [
                'nickname' => $request->nickname,
                'phone'    => $request->phone,
                'card_num' => $request->card_num,
                'is_check' => 1,
                'cover'    => $request->cover ?? null,
            ]);
        if ($res) {
            return $request->kernel->success([], '实名认证成功');
        } else {
            return $request->kernel->error("实名认证失败");
        }
    }
}
