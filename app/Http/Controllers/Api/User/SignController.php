<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\User\Models\UserSignRule;

class SignController extends Controller
{
    public function index(Request $request)
    {
        $type        = $request->type ?: 'week';
        $user        = $request->kernel->user();
        $timeBetween = match ($type) {
            'week' => [now()->startOfWeek(), now()->endOfWeek()],
            'month' => [now()->startOfMonth()->startOfWeek(), now()->endOfMonth()->endOfWeek()],
            default => [],
        };
        $signed      = $user->signs()
            ->ofBetween($timeBetween)
            ->select('*', DB::raw('DATE_FORMAT(sign_at,"%Y-%m-%d") as sign_at_day'))
            ->get();
        $daies       = [];
        for ($time = $timeBetween[0]; $time < $timeBetween[1]; $time->addDay()) {
            $daies[] = [
                'date'          => $time->toDateString(),
                'day'           => $time->day,
                'current_month' => $time->isCurrentMonth(),
                'is_sign'       => (bool) $signed->where('sign_at_day', $time->toDateString())->first(),
                'today'         => $time->isCurrentDay(),
            ];
        }
        $daySign = $user->signs()->ofDay(now())->first();

        //        $tasks = UserSignRule::ofEnabled()
        //            ->orderBy('day')
        //            ->latest()
        //            ->get()
        //            ->map(function ($item) use ($user) {
        //                return [
        //                    'title'    => $item->day > 1 ? '连续签到' : '签到',
        //                    'day'      => $item->day,
        //                    'only_one' => (bool) $item->only_one,
        //                    'coupon'   => $item->coupon ? [
        //                        'name'   => $item->coupon->name,
        //                        'number' => $item->number,
        //                    ] : (object) [],
        //                    'score'    => $item->score,
        //                    'complete' => $user->signLogs()
        //                        ->when($item->only_one === 0, function (Builder $builder) {
        //                            $builder->ofDay();
        //                        })
        //                        ->ofRule($item)
        //                        ->exists(),
        //                ];
        //            })
        //            ->toArray();
        return $request->kernel->success([
            'type'              => $type,
            'daies'             => $daies,
            'total'             => $user->signs()->count(),
            'can_sign'          => ! $daySign,
            'continuous_day'    => $daySign->continuous_day ?? 0,
            'task_time_between' => null,
        ]);
    }

    public function sign(Request $request)
    {
        $user = $request->kernel->user();
        if ($user->signs()->ofDay(now())->exists()) {
            return $request->kernel->error('今日已经签到');
        }
        $last          = $user->signs()->ofDay(now()->subDay())->value('continuous_day') ?: 0;
        $continuousDay = $last + 1;

        $rules = UserSignRule::ofEnabled()
            ->whereRaw("MOD({$continuousDay}, day) = 0")
            ->get();
        $score = $rules->sum('score');
        if ($score > 0) {
            $user->account->exec('activity_score', $score, now()->endOfDay()->toDateTimeString(), [
                'type'   => 'sign',
                'remark' => '签到积分',
                'date'   => now()->toDateString(),
            ]);
        }
        $log = $this->user()->signs()->create([
            'sign_at'        => now(),
            'continuous_day' => 1,
            'message'        => '积分×'.((int) $score),
        ]);

        return $request->kernel->success([
            'sign_at'        => $log->sign_at->toDateTimeString(),
            'continuous_day' => $log->continuous_day,
            'message'        => $log->message,
        ]);
    }
}