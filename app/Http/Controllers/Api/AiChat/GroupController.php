<?php

namespace App\Http\Controllers\Api\AiChat;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\Controller;
use App\Models\ChatGroup;
use App\Traits\CheckGroup;
use Exception;
use Illuminate\Http\Request;

class GroupController extends Controller
{
    use CheckGroup;

    public function index(Request $request)
    {
        $userId   = $request->kernel->id();
        $pageSize = $request->pageSize ?? 10;
        $query    = ChatGroup::query()
            ->where('user_id', $userId)
            ->latest('id')
            ->select('id', 'name', 'description', 'bgcolor','created_at');
        $count    = $query->count();
        $lists    = $query->paginate($pageSize);
        return $request->kernel->success([
            'list'  => $lists->map(function ($item) {
                return [
                    'id'          => $item->id,
                    'name'        => $item->name,
                    'description' => $item->description,
                    'bgcolor'     => $item->bgcolor,
                    'created_at'=>$item->created_at->toDateTimeString(),
                ];
            }),
            'count' => $count
        ]);
    }

    /**
     * Notes: 创建/修改
     *
     * @Author: 玄尘
     * @Date: 2024/12/3 11:02
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function create(Request $request)
    {
        $request->kernel->validate([
            'title' => 'required',
        ], [
            'title.required' => '请输入标题',
        ]);

        $userId = $request->kernel->id();

        $title   = $request->title;
        $groupId = $request->id ?? '';

        try {
            if ($groupId) {
                $group = ChatGroup::find($groupId);
                $this->checkGroup($group, $userId);
                $group->name = $title;
                $group->save();

                $message = '更新成功';
            } else {
                $group   = ChatGroup::create([
                    'user_id' => $userId,
                    'name'    => $title,
                ]);
                $message = '创建成功';
                $groupId = $group->id;
            }

            return $request->kernel->success([
                'id'    => $groupId,
                'title' => $group->title
            ], $message);
        } catch (Exception $e) {
            throw new ValidatorException("失败:".$e->getMessage());
        }
    }

    public function clear(Request $request)
    {
        $userId = $request->kernel->id();

        try {
            $groups = ChatGroup::where('user_id', $userId)->get();
            foreach ($groups as $group) {
                $group->forceDelete();
            }
            return $request->kernel->success([], '清空成功');
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

    public function delete(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required',
        ], [
            'id.required' => 'ID为空',
        ]);

        $userId  = $request->kernel->id();
        $groupId = $request->id;

        try {
            $group = ChatGroup::find($groupId);
            $this->checkGroup($group, $userId, false);
            $group->forceDelete();
            return $request->kernel->success([], '删除成功');
        } catch (Exception $e) {
            throw new ValidatorException("删除失败:".$e->getMessage());
        }
    }

    public function info(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required',
        ], [
            'id.required' => '请上传群组ID',
        ]);

        $userId  = $request->kernel->id();
        $groupId = $request->id ?? '';

        try {
            $info = ChatGroup::find($groupId);

            $this->checkGroup($info, $userId);

            return $request->kernel->success([
                'id'    => $info->id,
                'title' => $info->name
            ], 'success');
        } catch (Exception $e) {
            throw new ValidatorException("失败:".$e->getMessage());
        }
    }

}