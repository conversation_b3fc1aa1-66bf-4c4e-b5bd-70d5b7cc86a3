<?php

namespace App\Http\Controllers\Api\AiChat;

use App\Http\Controllers\Controller;
use App\Models\AudioSinger;
use App\Models\SystemConfig;
use App\Packages\Suno\Suno;
use Illuminate\Http\Request;

class SunoToolController extends Controller
{
    public function createdSinger(Request $request)
    {
        $request->kernel->validate([
            'name'  => 'required|max:50',
            'audio' => 'required',
            'cover' => 'required',
        ], [
            'name.required'  => '请输入歌手姓名',
            'name.max'       => '歌手姓名最长50个字符',
            'audio.required' => '请传入您的朗读或唱歌的录音文件',
            'cover.required' => '请传入歌手封面图',
        ]);
        $user  = $request->kernel->user();
        $score = SystemConfig::getValue('ai_singer', 0);
        if ($score > 0 && $user->account->score < $score) {
            return $request->kernel->error('积分余额不足');
        }
        $singer = AudioSinger::create([
            'user_id' => $user->id,
            'score'   => $score,
            'name'    => $request->name,
            'cover'   => $request->cover,
            'audio'   => $request->audio,
            'status'  => AudioSinger::STATUS_INIT,
        ]);
        if ($singer) {
            return $request->kernel->success([
                'id'   => $singer->id,
                'no'   => $singer->no,
                'name' => $singer->name,
            ]);
        } else {
            return $request->kernel->error('创建歌手失败');
        }
    }

    public function aiSinger(Request $request)
    {
        $result = Suno::tools()->aiSinger();
        $data   = [];
        if ($result->isSuccess()) {
            $data = $result->toArray()['hot'] ?? [];
        }
        return $request->kernel->success($data);
    }

    public function audioClass(Request $request)
    {
        $result = Suno::tools()->musicStyle();
        $data   = [];
        if ($result->isSuccess()) {
            $data = $result->toArray();
        }
        return $request->kernel->success($data);
    }

    public function audioLists(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required',
        ], [
            'id.required' => '请传入分类ID',
        ]);
        $result = Suno::tools()->audioList($request->id, $request->page ?: 1,
            $request->pagesize ?: 10);
        $data   = [
            'current_page' => 0,
            'first_page'   => 0,
            'last_page'    => 0,
            'list'         => [],
            'page_size'    => 0,
            'total'        => 0,
        ];
        if ($result->isSuccess()) {
            $data = $result->toArray();
        }
        return $request->kernel->success($data);
    }
}