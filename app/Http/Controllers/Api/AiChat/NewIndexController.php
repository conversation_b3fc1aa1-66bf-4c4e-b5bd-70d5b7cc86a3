<?php

namespace App\Http\Controllers\Api\AiChat;

use App\Http\Controllers\Controller;
use App\Models\ChatGroup;
use App\Models\ChatLog;
use App\Models\User;
use App\Packages\WateAiChat\WateAiChat;
use App\Traits\CheckGroup;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Modules\User\Http\Resources\UserBaseHiddenResource;
use OpenAI;

class NewIndexController extends Controller
{
    use CheckGroup;

    public function delete(Request $request)
    {
        $ids    = array_filter(explode(',', $request->ids ?: ''));
        $number = count($ids);
        if ($number <= 0) {
            return $request->kernel->error('参数错误');
        }
        $user = $request->kernel->user();
        $logs = ChatLog::ofUser($user)
            ->whereIn('id', $ids)
            ->get();
        if ($logs->count() != $number) {
            return $request->kernel->error('记录存在权限问题');
        }
        foreach ($logs as $log) {
            $log->delete();
        }
        return $request->kernel->success([], '删除成功');
    }

    public function getShareNo(Request $request)
    {
        $groupId = $request->group_id;
        $ids     = $request->ids;
        $user    = $request->kernel->user();
        $group   = ChatGroup::ofUser($user)
            ->where('id', $groupId)
            ->first();
        if (! $group) {
            return $request->kernel->error('群组不存在');
        }
        $ids = array_filter(explode(',', $ids));
        if (count($ids) > 0 && $group->logs()->whereIn('id', $ids)->count() != count($ids)) {
            return $request->kernel->error('记录与群组关系异常');
        }
        $data = [
            'user'  => $user->id,
            'group' => $groupId,
            'ids'   => $ids,
        ];
        $key  = md5(json_encode($data));
        Cache::put('chat_share_'.$key, $data, 24 * 60 * 60);
        return $request->kernel->success([
            'share_no' => $key,
        ]);
    }

    public function getShareChat(Request $request)
    {
        $no = $request->no;
        if (Cache::has('chat_share_'.$no)) {
            $data    = Cache::get('chat_share_'.$no);
            $user    = User::find($data['user']);
            $firstId = $request->first_id;
            $group   = ChatGroup::ofUser($user)->where('id', $data['group'])->first();
            if (! $group) {
                return $request->kernel->error('分享内容校验失败');
            }
            $logs = $group->logs()
                ->when(count($data['ids']) > 0, function (Builder $builder) use ($data) {
                    $builder->whereIn('id', $data['ids']);
                })
                ->when($firstId, function (Builder $builder, $firstId) {
                    $builder->where('id', '<', $firstId);
                })
                ->orderByDesc('id')
                ->limit($request->pagesize ?: 10)
                ->get()
                ->sortBy('id');

            return $request->kernel->success([
                'user' => new UserBaseHiddenResource($user),
                'list' => $logs->map(function ($item) {
                    return [
                        'id'   => $item->id,
                        'user' => [
                            'message' => $item->message,
                            'images'  => $item->images,
                        ],
                        'ai'   => [
                            'user'      => 'AI',
                            'id'        => $item->id,
                            'message'   => $item->response,
                            'reasoning' => $item->reasoning,
                        ],
                        'task' => [
                            'has'  => (bool) $item->asset,
                            'data' => ($item->asset?->assetable->getAssetResource()) ?: [],
                        ],
                    ];
                })->values()->toArray()
            ]);
        } else {
            return $request->kernel->error('分享链接已失效');
        }
    }

    public function chatV2(Request $request)
    {
        config([
            'app.debug' => false,
        ]);
        $user    = $request->kernel->user();
        $groupId = $request->group_id;
        $message = trim($request->message ?: '');
        $channel = $request->ai ?: '';
        $images  = $request->images ?: '';
        $feature = trim($request->feature ?? '');
        $think   = $request->think ?? 0;
        $search  = $request->search ?? 0;
        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);

        if (blank($message) && ! in_array($feature, ['questions'])) {
            $request->kernel->sseError('请您输入对话信息');
            exit;
        }
        $message = match ($feature) {
            default => $message,
            'questions' => '请帮我解答图中的写题.',
        };
        $group   = ChatGroup::ofUser($user)->where('id', $groupId)->first();
        if (! $group) {
            $group = ChatGroup::create([
                'user_id' => $user->id,
                'name'    => Str::limit($message, 20),
            ]);
        }
        
        try {
            $wateAi = WateAiChat::make($channel, $group, $user);
            $wateAi->send($message, $images ? [$images] : [], (bool) $think, (bool) $search);
        } catch (Exception $exception) {
            $request->kernel->sseError($exception->getMessage());
            exit;
        }
    }

    public function getHistoryMsg(Request $request)
    {
        $group_id = $request->group_id ?? 0;

        $user = $request->kernel->user();

        $group = ChatGroup::where('user_id', $user->id)
            ->where('id', $group_id)
            ->first();
        $this->checkGroup($group, $user->id);
        $lists   = $group->logs()
            ->orderByDesc('id')
            ->paginate($request->pagesize ?: 10)
            ->sortBy('id');
        $msgList = [];
        foreach ($lists as $item) {
            $msgList[] = [
                'user'    => '我',
                'id'      => $item->id,
                'message' => $item->message,
                'images'  => $item->images[0] ?? '',
            ];
            $hasTask   = (bool) $item->asset;
            $msgList[] = [
                'user'      => 'AI',
                'id'        => $item->id,
                'message'   => $item->response,
                'reasoning' => $item->reasoning,
                'task'      => [
                    'has'  => $hasTask,
                    'data' => ($item->asset?->assetable->getAssetResource()) ?: [],
                ],
            ];
        }

        return $request->kernel->success([
            'list' => $msgList
        ]);
    }

    public function pcHistoryMsg(Request $request)
    {
        $group_id = $request->group_id ?? 0;
        $firstId  = $request->first_id ?: '';
        $user     = $request->kernel->user();

        $group = ChatGroup::where('user_id', $user->id)
            ->where('id', $group_id)
            ->first();
        $this->checkGroup($group, $user->id);
        $lists = $group->logs()
            ->when($firstId, function (Builder $builder, $firstId) {
                $builder->where('id', '<', $firstId);
            })
            ->orderByDesc('id')
            ->limit($request->pagesize ?: 10)
            ->get()
            ->sortBy('id');

        return $request->kernel->success([
            'list' => $lists->map(function ($item) {
                return [
                    'id'   => $item->id,
                    'user' => [
                        'message' => $item->message,
                        'images'  => $item->images,
                    ],
                    'ai'   => [
                        'user'      => 'AI',
                        'id'        => $item->id,
                        'message'   => $item->response,
                        'reasoning' => $item->reasoning,
                    ],
                    'task' => [
                        'has'  => (bool) $item->asset,
                        'data' => ($item->asset?->assetable->getAssetResource()) ?: [],
                    ],
                ];
            })->values()->toArray()
        ]);
    }

    public function chatGpt(Request $request)
    {
        $client = OpenAI::factory()
            ->withApiKey(env('OPENAI_API_KEY'))
            ->withBaseUri('http://47.129.9.62:7799/v1')
            ->withHttpHeader('OpenAI-Beta', 'assistants=v2')
            ->make();

        $stream = $client->chat()->createStreamed([
            'model'    => 'gpt-4',
            'messages' => [
                ['role' => 'user', 'content' => $request->message],
            ],
        ]);
        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);
        foreach ($stream->getIterator() as $chunk) {
            $choices = $chunk['choices'][0];
            //            echo $choices['delta']['content'] ?? '';
            echo "data: ".($choices['delta']['content'] ?? '')."\n\n";
            ob_flush();
            flush();
            usleep(20000);
        }
    }
}