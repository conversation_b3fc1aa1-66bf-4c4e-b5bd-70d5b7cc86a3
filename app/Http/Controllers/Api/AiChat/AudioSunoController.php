<?php

namespace App\Http\Controllers\Api\AiChat;

use App\Http\Controllers\Controller;
use App\Http\Resources\Draw\AudioCollection;
use App\Http\Resources\Draw\AudioResource;
use App\Models\AiUnifyAsset;
use App\Models\DrawAudio;
use Exception;
use Illuminate\Http\Request;

class AudioSunoController extends Controller
{
    public function detail(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required|integer|exists:draw_audio,id',
        ], [
            'id.required' => '音乐ID不能为空',
            'id.integer'  => '音乐ID必须是整数',
            'id.exists'   => '音乐不存在',
        ]);
        $audio = DrawAudio::find($request->id);
        return $request->kernel->success((new AudioResource($audio))->additional([
            'showCan' => true
        ]));
    }

    public function PlatformTo(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required|integer|exists:draw_audio,id',
        ]);
        $user  = $request->kernel->user();
        $audio = DrawAudio::ofUser($user)->find($request->id);
        if (! $audio) {
            return $request->kernel->error('音乐不存在');
        }
        if ($audio->platform == 1) {
            return $request->kernel->error('音乐已经公开');
        }
        $audio->platform = 1;
        $audio->save();
        return $request->kernel->success('操作成功');
    }

    public function PlatformDown(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required|integer|exists:draw_audio,id',
        ]);
        $user  = $request->kernel->user();
        $audio = DrawAudio::ofUser($user)->find($request->id);
        if (! $audio) {
            return $request->kernel->error('音乐不存在');
        }
        if ($audio->platform === 0) {
            return $request->kernel->error('音乐已经私密状态');
        }
        $audio->platform = 0;
        $audio->save();
        return $request->kernel->success('操作成功');
    }

    public function show(DrawAudio $audio, Request $request)
    {
        if ($audio->platform !== 1) {
            throw new Exception('音乐已经私密状态');
        }
        return view('suno.show', compact('audio'));
    }

    public function MyUpdateName(Request $request)
    {
        $request->kernel->validate([
            'id'   => 'required|integer|exists:draw_audio,id',
            'name' => 'required|string|min:2|max:20',
        ]);
        $user  = $request->kernel->user();
        $audio = DrawAudio::ofUser($user)->find($request->id);
        if (! $audio) {
            return $request->kernel->error('音乐不存在');
        }
        $audio->name = $request->name;
        $audio->save();
        return $request->kernel->success('操作成功');
    }

    public function Platform(Request $request)
    {
        $keyword = $request->keyword;
        $logs    = DrawAudio::where('platform', 1)
            ->where('status', AiUnifyAsset::STATUS_SUCCESS)
            ->when($keyword, function ($query) use ($keyword) {
                return $query->where('name', 'like', "%{$keyword}%")
                    ->orWhere('lyric', 'like', "%{$keyword}%")
                    ->orWhere('prompt', 'like', "%{$keyword}%")
                    ->orWhere('tags', 'like', "%{$keyword}%");
            })
            ->orderByDesc('updated_at')
            ->orderByDesc('id')
            ->paginate($request->pagesize ?: 10);
        return $request->kernel->success(new AudioCollection($logs, false));
    }

    public function MyDelete(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required|integer|exists:draw_audio,id',
        ]);
        $user  = $request->kernel->user();
        $audio = DrawAudio::ofUser($user)->find($request->id);
        if (! $audio) {
            return $request->kernel->error('音乐不存在');
        }
        if ($audio->delete()) {
            return $request->kernel->success([], '删除成功');
        } else {
            return $request->kernel->error('删除失败');
        }
    }
}