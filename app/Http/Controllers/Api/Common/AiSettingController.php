<?php

namespace App\Http\Controllers\Api\Common;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Models\AiChatConfig;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class AiSettingController extends ApiController
{
    private static $site_id = 1;

    public function aiLists(Request $request)
    {
        $showGpt = Config::get('app.show_gpt');
        $lang    = env('lang.default_lang', 'zh-cn');

        $setting    = DB::table('setting')->where('site_id', self::$site_id)->first();
        $site       = DB::table('site')->where(['id' => self::$site_id, 'is_delete' => 0])->first();
        $webSetting = $setting->web ? json_decode($setting->web, true) : [];

        if (! $site || empty($webSetting['is_open'])) {
            throw new ValidatorException("已暂停服务");
        }

        $loginSetting  = $setting->login ? json_decode($setting->login, true) : [];
        $aiSetting     = $setting->chatgpt ? json_decode($setting->chatgpt, true) : [];
        $ai4Setting    = $setting->gpt4 ? json_decode($setting->gpt4, true) : [];
        $drawSetting   = $setting->draw ? json_decode($setting->draw, true) : [];
        $videoSetting  = $setting->video ? json_decode($setting->video, true) : [];
        $musicSetting  = $setting->music ? json_decode($setting->music, true) : [];
        $pkSetting     = $setting->pk ? json_decode($setting->pk, true) : [];
        $batchSetting  = $setting->batch ? json_decode($setting->batch, true) : [];
        $novelSetting  = $setting->novel ? json_decode($setting->novel, true) : [];
        $teamSetting   = $setting->team ? json_decode($setting->team, true) : [];
        $mindSetting   = $setting->mind ? json_decode($setting->mind, true) : [];
        $docSetting    = $setting->doc ? json_decode($setting->doc, true) : [];
        $searchSetting = $setting->search ? json_decode($setting->search, true) : [];
        $priceSetting  = $setting->price
            ? json_decode($setting->price, true)
            : ['title' => '积分'];

        try {
            // 汇总成最终AI列表，显示到前端
            $configs = AiChatConfig::ofEnabled()
                ->with(['multiEngine', 'thinkEngine'])
                ->orderByDesc('is_default')
                ->orderBy('order')
                ->orderByDesc('id')
                ->get();
            $aiList  = $configs->map(function ($item) {
                return [
                    'name'        => $item->key,
                    'logo'        => $item->cover_url,
                    'remark'      => $item->remark,
                    'alias'       => $item->name,
                    'image'       => $item->multiEngine ? 1 : 0,
                    'think'       => $item->thinkEngine ? 1 : 0,
                    'search'      => [
                        'text'  => $item->engine?->search ? true : false,
                        'image' => $item->multiEngine?->search ? true : false,
                        'think' => $item->thinkEngine?->search ? true : false,
                    ],
                    'image_count' => 1,
                ];
            })->toArray();
        } catch (Exception $e) {
            throw new ValidatorException($e->getMessage());
        }

        // 功能开关
        $writeIsOpen   = 1;
        $cosplayIsOpen = 1;
        $drawIsOpen    = isset($drawSetting['is_open']) ? intval($drawSetting['is_open']) : 0;
        $pkIsOpen      = isset($pkSetting['is_open']) ? intval($pkSetting['is_open']) : 1;
        $batchIsOpen   = isset($batchSetting['is_open']) ? intval($batchSetting['is_open']) : 1;
        $novelIsOpen   = isset($novelSetting['is_open']) ? intval($novelSetting['is_open']) : 1;
        $teamIsOpen    = isset($teamSetting['is_open']) ? intval($teamSetting['is_open']) : 0;
        $mindIsOpen    = empty($mindSetting['is_open']) ? 0 : 1;
        $docIsOpen     = empty($docSetting['is_open']) ? 0 : 1;
        $searchIsOpen  = empty($searchSetting['is_open']) ? 0 : 1;
        $loginWechat   = $loginSetting['login_wechat'] ?? 1;
        $loginPhone    = $loginSetting['login_phone'] ?? 0;

        $videoIsOpen = 0;
        if ((! empty($videoSetting['pika']) && $videoSetting['pika']['is_open']) || (! empty($videoSetting['runway']) && $videoSetting['runway']['is_open']) || (! empty($videoSetting['stable']) && $videoSetting['stable']['is_open']) || (! empty($videoSetting['sora']) && $videoSetting['sora']['is_open'])) {
            $videoIsOpen = 1;
        }

        $musicIsOpen = 0;
        if (! empty($musicSetting['suno']) && $musicSetting['suno']['is_open']) {
            $musicIsOpen = 1;
        }

        $result = [
            'logo'           => $webSetting['logo'] ?? '',
            'logo_mini'      => $webSetting['logo_mini'] ?? '',
            'page_title'     => $webSetting['page_title'] ?? '',
            'copyright'      => $webSetting['copyright'] ?? '',
            'copyright_link' => $webSetting['copyright_link'] ?? '',
            'icp'            => $systemSetting['system_icp'] ?? '',
            'gongan'         => $systemSetting['system_gongan'] ?? '',
            'lang'           => $lang,
            'login_wechat'   => $loginWechat,
            'login_phone'    => $loginPhone,
            'priceSetting'   => $priceSetting,
            'theme'          => '', // light 或 dark
            'drawIsOpen'     => $drawIsOpen,
            'videoIsOpen'    => $videoIsOpen,
            'musicIsOpen'    => $musicIsOpen,
            'writeIsOpen'    => $writeIsOpen,
            'cosplayIsOpen'  => $cosplayIsOpen,
            'pkIsOpen'       => $pkIsOpen,
            'batchIsOpen'    => $batchIsOpen,
            'novelIsOpen'    => $novelIsOpen,
            'teamIsOpen'     => $teamIsOpen,
            'mindIsOpen'     => $mindIsOpen,
            'docIsOpen'      => $docIsOpen,
            'searchIsOpen'   => $searchIsOpen,
            'aiList'         => $aiList
        ];
        return $request->kernel->success($result);
    }
}
