<?php

namespace App\Http\Controllers\Api\Common;

use App\Http\Controllers\ApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class StrController extends ApiController
{
    /**
     * Notes: 生成字符串，用于fastgpt聊天的data_id
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 10:46
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function random(Request $request)
    {
        $number = $request->number ?? 18;
        return response()->json([
            'code'    => 0,
            'message' => '成功',
            'data'    => [
                'str' => Str::random($number)

            ]
        ]);
    }
}
