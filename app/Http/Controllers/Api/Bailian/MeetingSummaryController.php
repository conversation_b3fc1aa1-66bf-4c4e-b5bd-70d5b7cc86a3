<?php

namespace App\Http\Controllers\Api\Bailian;

use App\Enums\Bailian\ArticleProcessingStatusEnum;
use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Http\Controllers\Controller;
use App\Models\BailianArticle;
use App\Services\MeetingSummaryService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MeetingSummaryController extends Controller
{
    protected MeetingSummaryService $summaryService;

    public function __construct(MeetingSummaryService $summaryService)
    {
        $this->summaryService = $summaryService;
    }

    /**
     * 提交会议总结任务
     * 立即返回任务ID，不等待结果
     */
    public function submitTask(Request $request): JsonResponse
    {
        $request->validate([
            'article_id' => 'required|integer|exists:bailian_articles,id'
        ]);

        $article = BailianArticle::findOrFail($request->article_id);

        // 检查权限
        if (!$article->isMy($request->user())) {
            return response()->json([
                'success' => false,
                'message' => '无权限操作此笔记'
            ], 403);
        }

        $result = $this->summaryService->submitMeetingSummaryTask($article);

        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * 查询任务状态
     * 直接查询讯飞API，如果完成则自动保存结果
     */
    public function queryStatus(Request $request): JsonResponse
    {
        $request->validate([
            'article_id' => 'required|integer|exists:bailian_articles,id'
        ]);

        $article = BailianArticle::findOrFail($request->article_id);

        // 检查权限
        if (!$article->isMy($request->user())) {
            return response()->json([
                'success' => false,
                'message' => '无权限查看此笔记状态'
            ], 403);
        }

        $result = $this->summaryService->queryTaskStatus($article);

        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * 获取文章状态信息
     */
    public function getArticleStatus(Request $request): JsonResponse
    {
        $request->validate([
            'article_id' => 'required|integer|exists:bailian_articles,id'
        ]);

        $article = BailianArticle::findOrFail($request->article_id);

        // 检查权限
        if (!$article->isMy($request->user())) {
            return response()->json([
                'success' => false,
                'message' => '无权限查看此笔记'
            ], 403);
        }

        $status = $this->summaryService->getArticleStatus($article);

        return response()->json([
            'success' => true,
            'data' => $status
        ]);
    }

    /**
     * 重新处理会议总结
     */
    public function reprocess(Request $request): JsonResponse
    {
        $request->validate([
            'article_id' => 'required|integer|exists:bailian_articles,id'
        ]);

        $article = BailianArticle::findOrFail($request->article_id);

        // 检查权限
        if (!$article->isMy($request->user())) {
            return response()->json([
                'success' => false,
                'message' => '无权限操作此笔记'
            ], 403);
        }

        // 检查是否可以重新处理
        $currentStatus = ArticleProcessingStatusEnum::from($article->processing_status);
        if (!$currentStatus->canReprocess()) {
            return response()->json([
                'success' => false,
                'message' => '当前状态不允许重新处理'
            ], 400);
        }

        $result = $this->summaryService->startAsyncProcessing($article);

        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * 批量查询多个文章的处理状态
     */
    public function batchStatus(Request $request): JsonResponse
    {
        $request->validate([
            'article_ids' => 'required|array|max:50',
            'article_ids.*' => 'integer|exists:bailian_articles,id'
        ]);

        $user = $request->user();
        $articleIds = $request->article_ids;

        $articles = BailianArticle::whereIn('id', $articleIds)
            ->where('user_id', $user->id)
            ->get();

        $statuses = [];
        foreach ($articles as $article) {
            $statuses[] = $this->summaryService->getProcessingStatus($article);
        }

        return response()->json([
            'success' => true,
            'data' => $statuses
        ]);
    }

    /**
     * 获取处理统计信息
     */
    public function getStats(Request $request): JsonResponse
    {
        $user = $request->user();

        $stats = [
            'total' => BailianArticle::where('user_id', $user->id)
                ->where('type', BailianArticleTypeEnum::MEETING)
                ->count(),
            'pending' => BailianArticle::where('user_id', $user->id)
                ->where('type', BailianArticleTypeEnum::MEETING)
                ->where('processing_status', ArticleProcessingStatusEnum::PENDING)
                ->count(),
            'processing' => BailianArticle::where('user_id', $user->id)
                ->where('type', BailianArticleTypeEnum::MEETING)
                ->where('processing_status', ArticleProcessingStatusEnum::PROCESSING)
                ->count(),
            'completed' => BailianArticle::where('user_id', $user->id)
                ->where('type', BailianArticleTypeEnum::MEETING)
                ->where('processing_status', ArticleProcessingStatusEnum::COMPLETED)
                ->count(),
            'failed' => BailianArticle::where('user_id', $user->id)
                ->where('type', BailianArticleTypeEnum::MEETING)
                ->where('processing_status', ArticleProcessingStatusEnum::FAILED)
                ->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
