# 会议总结异步处理 API 文档

## 概述

会议总结处理采用简化的异步架构，集成在现有的文章接口中：
1. **创建会议笔记** - 自动提交总结任务，返回taskId
2. **查询状态** - 前端按需查询讯飞API状态，完成时自动保存结果

## API 接口

### 1. 创建会议笔记（自动提交总结任务）

**POST** `/api/bailian/article/meeting`

创建会议笔记，如果需要生成总结会自动提交任务。

#### 请求参数
```json
{
    "title": "会议标题",
    "details": "{\"audio\":\"音频URL\",\"text_record\":[...]}",
    "type": "meeting"
}
```

#### 响应示例
```json
{
    "success": true,
    "data": {
        "id": 123,
        "title": "会议标题",
        "type": "meeting",
        "summary_task": {
            "success": true,
            "message": "会议总结任务已提交",
            "article_id": 123,
            "task_id": "xfyun_task_12345"
        }
    }
}
```

### 2. 查询会议总结状态

**GET** `/api/bailian/article/query-meeting-summary?article_id=123`

查询讯飞CKM任务状态，如果完成会自动保存结果到文章。

### 3. 流式返回会议总结

**GET** `/api/bailian/article/stream-meeting-summary?article_id=123`

流式返回已完成的会议总结内容，支持打字机效果。

#### 响应示例

**处理中：**
```json
{
    "success": true,
    "article_id": 123,
    "task_id": "xfyun_task_12345",
    "is_completed": false,
    "is_success": false,
    "is_failed": false,
    "task_status": 1,
    "message": "任务处理中，请稍后查询",
    "has_content": false
}
```

**处理完成：**
```json
{
    "success": true,
    "article_id": 123,
    "task_id": "xfyun_task_12345",
    "is_completed": true,
    "is_success": true,
    "is_failed": false,
    "task_status": 0,
    "message": "会议总结生成完成",
    "has_content": true
}
```

#### 流式返回示例

**SSE 事件流：**
```
data: {"type":"start","msg":"开始获取会议总结","data":{"article_id":123,"title":"会议标题"}}

data: {"type":"msg","msg":"#","data":{}}

data: {"type":"msg","msg":"#","data":{}}

data: {"type":"msg","msg":" ","data":{}}

data: {"type":"msg","msg":"会","data":{}}

data: {"type":"msg","msg":"议","data":{}}

...

data: {"type":"complete","msg":"会议总结输出完成","data":{"article_id":123,"success":true}}
```

### 3. 获取文章状态

**GET** `/api/bailian/meeting-summary/article-status?article_id=123`

获取文章的处理状态信息（不查询讯飞API）。

#### 响应示例
```json
{
    "success": true,
    "data": {
        "article_id": 123,
        "has_task": true,
        "task_id": "xfyun_task_12345",
        "processing_status": "completed",
        "status_text": "已完成",
        "error": null,
        "started_at": "2025-06-25T17:30:00.000000Z",
        "completed_at": "2025-06-25T17:35:00.000000Z",
        "can_reprocess": true,
        "needs_summary": false
    }
}
```

### 3. 重新处理

**POST** `/api/bailian/meeting-summary/reprocess`

重新处理失败或已完成的会议总结。

#### 请求参数
```json
{
    "article_id": 123
}
```

### 4. 批量查询状态

**POST** `/api/bailian/meeting-summary/batch-status`

批量查询多个文章的处理状态。

#### 请求参数
```json
{
    "article_ids": [123, 124, 125]
}
```

### 5. 获取统计信息

**GET** `/api/bailian/meeting-summary/stats`

获取用户的会议总结处理统计信息。

#### 响应示例
```json
{
    "success": true,
    "data": {
        "total": 50,
        "pending": 2,
        "processing": 1,
        "completed": 45,
        "failed": 2
    }
}
```

## 前端实现示例

### JavaScript 实现

```javascript
class MeetingSummaryProcessor {
    constructor(articleId, options = {}) {
        this.articleId = articleId;
        this.pollInterval = options.pollInterval || 5000; // 5秒查询一次
        this.maxPollTime = options.maxPollTime || 600000; // 最大查询10分钟
        this.onStatusChange = options.onStatusChange || (() => {});
        this.onComplete = options.onComplete || (() => {});
        this.onError = options.onError || (() => {});

        this.pollTimer = null;
        this.startTime = Date.now();
        this.taskId = null;
    }

    // 提交任务
    async submitTask() {
        try {
            const response = await fetch('/api/bailian/meeting-summary/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getToken()}`
                },
                body: JSON.stringify({ article_id: this.articleId })
            });

            const result = await response.json();

            if (result.success) {
                this.taskId = result.task_id;
                this.onStatusChange('submitted', `任务已提交 (${this.taskId})`);

                // 开始查询状态
                this.startPolling();
            } else {
                this.onError(result.message);
            }
        } catch (error) {
            this.onError('提交任务失败: ' + error.message);
        }
    }

    // 开始轮询查询
    startPolling() {
        // 立即查询一次
        this.queryStatus();

        // 设置定时查询
        this.pollTimer = setInterval(() => {
            this.queryStatus();
        }, this.pollInterval);
    }

    // 查询状态
    async queryStatus() {
        // 检查是否超时
        if (Date.now() - this.startTime > this.maxPollTime) {
            this.stopPolling();
            this.onError('查询超时，请手动刷新页面查看结果');
            return;
        }

        try {
            const response = await fetch(
                `/api/bailian/meeting-summary/query?article_id=${this.articleId}`,
                {
                    headers: {
                        'Authorization': `Bearer ${this.getToken()}`
                    }
                }
            );

            const result = await response.json();

            if (result.success) {
                this.onStatusChange('processing', result.message);

                // 如果任务完成
                if (result.is_completed) {
                    this.stopPolling();

                    if (result.is_success && result.has_content) {
                        this.onComplete(result);
                    } else {
                        this.onError('任务处理失败');
                    }
                }
            } else {
                this.onError(result.message);
            }
        } catch (error) {
            console.error('查询状态失败:', error);
        }
    }

    // 停止轮询
    stopPolling() {
        if (this.pollTimer) {
            clearInterval(this.pollTimer);
            this.pollTimer = null;
        }
    }

    // 获取认证token
    getToken() {
        return localStorage.getItem('auth_token');
    }
}

// 使用示例
const processor = new MeetingSummaryProcessor(123, {
    onStatusChange: (status, message) => {
        console.log(`状态: ${status}, 消息: ${message}`);
        updateStatusUI(status, message);
    },
    onComplete: (result) => {
        console.log('处理完成!', result);
        // 刷新页面内容
        location.reload();
    },
    onError: (error) => {
        console.error('处理失败:', error);
        showErrorMessage(error);
    }
});

// 提交任务
processor.submitTask();
```

### Vue.js 组件示例

```vue
<template>
  <div class="meeting-summary-processor">
    <div v-if="!isProcessing && !isCompleted">
      <button @click="startProcessing" :disabled="loading">
        {{ loading ? '启动中...' : '生成会议总结' }}
      </button>
    </div>
    
    <div v-if="isProcessing" class="processing-status">
      <div class="progress-bar">
        <div class="progress" :style="{ width: progressPercent + '%' }"></div>
      </div>
      <p>{{ statusMessage }}</p>
      <small>已处理 {{ elapsedTime }}s</small>
    </div>
    
    <div v-if="error" class="error-message">
      <p>{{ error }}</p>
      <button @click="retryProcessing">重试</button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    articleId: {
      type: Number,
      required: true
    }
  },
  
  data() {
    return {
      loading: false,
      isProcessing: false,
      isCompleted: false,
      statusMessage: '',
      error: null,
      startTime: null,
      elapsedTime: 0,
      pollTimer: null
    };
  },
  
  computed: {
    progressPercent() {
      // 根据状态计算进度
      if (this.statusMessage.includes('等待')) return 10;
      if (this.statusMessage.includes('处理中')) return 50;
      if (this.statusMessage.includes('完成')) return 100;
      return 0;
    }
  },
  
  methods: {
    async startProcessing() {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await this.$http.post('/api/bailian/meeting-summary/start', {
          article_id: this.articleId
        });
        
        if (response.data.success) {
          this.isProcessing = true;
          this.startTime = Date.now();
          this.startPolling();
          this.startTimer();
        } else {
          this.error = response.data.message;
        }
      } catch (error) {
        this.error = '启动失败: ' + error.message;
      } finally {
        this.loading = false;
      }
    },
    
    startPolling() {
      this.pollTimer = setInterval(this.checkStatus, 3000);
    },
    
    async checkStatus() {
      try {
        const response = await this.$http.get('/api/bailian/meeting-summary/status', {
          params: { article_id: this.articleId }
        });
        
        if (response.data.success) {
          const status = response.data.data;
          this.statusMessage = status.status_text;
          
          if (status.is_final) {
            this.stopPolling();
            this.isProcessing = false;
            
            if (status.status === 'completed') {
              this.isCompleted = true;
              this.$emit('completed', status);
            } else {
              this.error = status.error || '处理失败';
            }
          }
        }
      } catch (error) {
        console.error('查询状态失败:', error);
      }
    },
    
    stopPolling() {
      if (this.pollTimer) {
        clearInterval(this.pollTimer);
        this.pollTimer = null;
      }
    },
    
    startTimer() {
      setInterval(() => {
        if (this.startTime) {
          this.elapsedTime = Math.floor((Date.now() - this.startTime) / 1000);
        }
      }, 1000);
    },
    
    retryProcessing() {
      this.error = null;
      this.isProcessing = false;
      this.isCompleted = false;
      this.startProcessing();
    }
  },
  
  beforeDestroy() {
    this.stopPolling();
  }
};
</script>
```

## 环境配置

确保在 `.env` 文件中配置了相关参数：

```env
# 讯飞CKM超时配置
XFYUN_CKM_TIMEOUT=60
XFYUN_CKM_CONNECT_TIMEOUT=10
XFYUN_CKM_MAX_POLL_TIME=600
XFYUN_CKM_STREAM_TIMEOUT=900

# 队列配置
QUEUE_CONNECTION=database
```

## 队列配置

确保队列工作进程正在运行：

```bash
# 启动队列工作进程
php artisan queue:work --queue=meeting-summary

# 或者使用 Supervisor 管理
php artisan queue:work --queue=meeting-summary --daemon
```
