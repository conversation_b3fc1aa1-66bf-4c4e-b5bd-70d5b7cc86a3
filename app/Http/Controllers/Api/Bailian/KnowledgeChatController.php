<?php

namespace App\Http\Controllers\Api\Bailian;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Bailian\Chat\DoChatRequest;
use App\Http\Requests\Bailian\Chat\LikeChatRequest;
use App\Http\Resources\Bailian\Chat\ChatCollection;
use App\Http\Resources\Bailian\Knowledge\KnowledgeByTypeCollection;
use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeChat;
use App\Packages\BailianAssistant\BLAssistant;
use App\Traits\KnowledgeHelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class KnowledgeChatController extends ApiController
{
    use KnowledgeHelperTrait;

    public function index(Request $request): JsonResponse
    {
        $session_id    = $request->input('session_id', null);
        $user          = $request->kernel->user();
        $helpKnowledge = BailianKnowledge::query()
            ->where('type', BailianKnowledge::TYPE_HELP)
            ->first();
        //排除帮助知识库
        $chats = BailianKnowledgeChat::query()
            ->whereHas('useKnowledge', function ($query) use ($helpKnowledge) {
                return $query->where('knowledge_id', '<>', $helpKnowledge->id);
            })
            ->when($session_id, function ($query) use ($session_id) {
                return $query->where('session_id', $session_id);
            })
            ->ofUser($user)
            ->latest('id')
            ->paginate();

        return $request->kernel->success(new ChatCollection($chats));
    }

    public function chat(DoChatRequest $request)
    {
        config([
            'app.debug' => false,
        ]);

        $message               = $request->input('message');
        $session_id            = $request->session_id ?? '';
        $bailian_knowledge_ids = $request->bailian_knowledge_ids ?? '';
        $session_file_ids      = $request->session_file_ids ?? '';
        $user                  = $request->kernel->user();
        $webSearch             = $request->web_search;
        $think                 = $request->think ?? false;

        if (! $this->isTrue($webSearch) && empty($bailian_knowledge_ids)) {
            return $request->kernel->error('知识库不能为空。');
        }

        $bailian_knowledge_ids = $this->filterKnowledgeIds($user, $bailian_knowledge_ids);
        if (empty($bailian_knowledge_ids) && ! $this->isTrue($webSearch)) {
            return $request->kernel->error('没有可用的知识库');
        }
        $this->cacheKnowledgeIds($user, $bailian_knowledge_ids);

        if (function_exists('apache_setenv')) {
            apache_setenv('no-gzip', 1);
        }
        ini_set('output_buffering', 'off');
        ini_set('zlib.output_compression', false);
        ini_set('implicit_flush', true);
        while (ob_get_level() > 0) {
            ob_end_flush();
        }
        ob_start();
        ignore_user_abort(true);
        set_time_limit(0);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush();
        try {
            $bailian = new BLAssistant();
            $bailian->knowledgeChat()
                ->setKnowledgeId($bailian_knowledge_ids)
                ->setFileIds($session_file_ids)
                ->setWebSearch($webSearch == 'true')
                ->setThink($think)
                ->chat($message, $user, $session_id);
        } catch (\Exception $exception) {
            $request->kernel->sseError($exception->getMessage());
        }
    }

    public function like(LikeChatRequest $request)
    {
        $is_like = $request->is_liked;
        $chat_id = $request->bailian_chat_id;
        if (! in_array($is_like, [0, 1])) {
            return $request->kernel->error('参数错误');
        }
        $chat = BailianKnowledgeChat::find($chat_id);
        if (! $chat) {
            return $request->kernel->error('聊天记录不存在');
        }
        $chat->is_liked = $is_like;
        $chat->save();
        return $request->kernel->success('操作成功');
    }

    public function clear(Request $request)
    {
        $user = $request->kernel->user();
        BailianKnowledgeChat::query()->ofUser($user)->delete();
        return $request->kernel->success('操作成功');
    }

    /**
     * Notes: 删除记录
     *
     * @Author: 玄尘
     * @Date: 2025/6/18 09:54
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function delete(Request $request)
    {
        $chat_id = $request->chat_id ?? '';
        if (! $chat_id) {
            return $request->kernel->error('缺少chat_id');
        }
        $user = $request->kernel->user();
        $chat = BailianKnowledgeChat::find($chat_id);
        if (! $chat) {
            return $request->kernel->error('聊天记录不存在');
        }
        if ($chat->user_id != $user->id) {
            return $request->kernel->error('没有权限');
        }
        $chat->delete();
        return $request->kernel->success('删除成功');
    }

    /**
     * Notes: 聊天可用的知识库
     *
     * @Author: 玄尘
     * @Date: 2025/6/4 15:02
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function knowledge(Request $request)
    {
        $knowledgeIds = $request->bailian_knowledge_ids;
        $user         = $request->kernel->user();
        $selectedIds  = $this->parseSelectedIds($knowledgeIds);

        // 分别获取各类型知识库
        $myKnowledge         = $this->getKnowledgeByType('my', $user, $selectedIds);
        $publicKnowledge     = $this->getKnowledgeByType('public', $user, $selectedIds);
        $subscribedKnowledge = $this->getKnowledgeByType('subscribe', $user, $selectedIds);

        // 获取统计信息
        $statistics = $this->getKnowledgeStatistics($myKnowledge, $publicKnowledge, $subscribedKnowledge, $selectedIds);

        return $request->kernel->success($statistics);
    }

    /**
     * Notes: 可用的知识库列表--即将废弃
     *
     * @Author: 玄尘
     * @Date: 2025/6/12 10:05
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function knowledgeList(Request $request)
    {
        $knowledgeIds = $request->bailian_knowledge_ids;
        $user         = $request->kernel->user();
        $selectedIds  = $this->parseSelectedIds($knowledgeIds);

        $knowledge = $this->getAccessibleKnowledge($user, $selectedIds);

        return $request->kernel->success($knowledge);
    }

    public function knowledgeListByType(Request $request)
    {
        $user      = $request->kernel->user();
        $type      = $request->type ?? 'my';
        $name      = $request->name ?? '';
        $knowledge = BailianKnowledge::query()
            ->when($name, function ($query) use ($name) {
                $query->where('name', 'like', "%{$name}%");
            })
            ->ofType($type, $user)
            ->select('id', 'knowledge_id', 'name', 'level', 'type')
            ->paginate();
        $user      = $request->kernel->user();
        $data      = [
            'knowledge_ids' => $this->getCacheKnowledgeIds($user),
            'list'          => new KnowledgeByTypeCollection($knowledge),
        ];
        return $request->kernel->success($data);
    }
}
