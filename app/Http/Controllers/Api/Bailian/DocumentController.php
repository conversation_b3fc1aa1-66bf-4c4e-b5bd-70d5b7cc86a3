<?php

namespace App\Http\Controllers\Api\Bailian;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Requests\Bailian\File\AddToKnowledgeRequest;
use App\Http\Requests\Bailian\File\UpdateFileRequest;
use App\Http\Resources\Bailian\File\FileCollection;
use App\Http\Resources\Bailian\File\FileResource;
use App\Jobs\BaiLian\FileUpdateJob;
use App\Models\BailianFile;
use App\Models\BailianKnowledge;
use App\Services\KnowledgeItemStrategies\KnowledgeItemStrategyFactory;
use DB;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Modules\Storage\Models\Upload;

class DocumentController extends ApiController
{

    /**
     * 获取文档列表
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $title       = $request->title;
        $inKnowledge = $request->in_knowledge ?? null;
        $user        = $request->kernel->user();
        $type        = $request->type;//my  public  featured
        $files       = BailianFile::query()
            ->whereHas('storage')
            ->with('storage', 'knowledgeItem')
            ->ofType($type, $user)
            ->when($title, function ($q) use ($title) {
                $q->where('title', 'like', "%{$title}%");
            })
            ->when($inKnowledge, function ($q) {
                $q->whereHas('knowledgeItem');
            })
            ->latest()
            ->paginate();

        return $request->kernel->success(new FileCollection($files));
    }

    /**
     * 上传知识库文档
     *
     * @param  Request  $request
     * @return JsonResponse
     * @throws Exception
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'hash' => 'required|string|exists:storage_uploads,hash',
        ], [
            'hash.required' => '缺少文件上传的hash',
            'hash.string'   => 'hash必须是字符串',
        ]);

        if ($validator->fails()) {
            throw new ValidatorException($validator->errors()->first());
        }

        try {
            DB::beginTransaction();

            $user              = $request->kernel->user();
            $hash              = $request->hash;
            $knowledge_id      = $request->knowledge_id;
            $knowledge_item_id = $request->knowledge_item_id ?? null;

            $knowledge = BailianKnowledge::find($knowledge_id);
            if ($knowledge && ! $knowledge->canSetData($user)) {
                throw new ValidatorException('您没有权限将文档添加到该知识库');
            }

            $storage = Upload::where('hash', $hash)->first();
            $file    = BailianFile::where('storage_id', $storage->id)->first();
            if (! $file) {
                $file = BailianFile::create([
                    'user_id'     => $user->id,
                    'storage_id'  => $storage->id,
                    'is_public'   => $request->is_public ?? 0,
                    'is_featured' => $request->is_featured ?? 0,
                    'title'       => $storage->original
                ]);
            }

            if (! $file->canMoveKnowledge($user)) {
                throw new ValidatorException('您没有权限将该文档加入知识库或该文档已经加入知识库');
            }

            if ($knowledge) {
                // 使用策略模式同步加入知识库
                $strategy = KnowledgeItemStrategyFactory::createForModel($file);
                $strategy->addToKnowledge($file, $knowledge, '', [
                    'parent_id' => $knowledge_item_id,
                    'user_id'   => $user->id,
                ]);
            }

            DB::commit();
            return $request->kernel->success(new FileResource($file));
        } catch (\Exception $exception) {
            DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 更新文档信息
     *
     * @Author: 玄尘
     * @Date: 2025/5/8 16:30
     * @param  \App\Http\Requests\Bailian\File\UpdateFileRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateFileRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $hash = $request->hash;
            $id   = $request->file_id;

            $file = BailianFile::find($id);
            $user = $request->kernel->user();

            if (! $file->canEdit($user)) {
                throw new ValidatorException('您没有权限更新该文档');
            }

            $storage = Upload::where('hash', $hash)->first();
            if (! $storage) {
                throw new ValidatorException('文件不存在');
            }

            $originalStorageId = $file->storage_id;
            $file->update([
                'storage_id'  => $storage->id,
                'is_public'   => $request->is_public ?? 0,
                'is_featured' => $request->is_featured ?? 0,
                'title'       => $storage->original
            ]);

            // 如果文件内容发生变化且在知识库中，需要更新知识库
            if ($originalStorageId != $file->storage_id && $file->isInKnowledge()) {
                // 延迟到事务提交后再触发，确保文件更新已保存
                DB::afterCommit(function () use ($file) {
                    FileUpdateJob::dispatch($file);
                });
            }

            DB::commit();
            return $request->kernel->success('文档更新成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * Notes: 删除文档
     *
     * @Author: 玄尘
     * @Date: 2025/5/8 16:38
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Throwable
     */
    public function delete(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $validator = Validator::make($request->all(), [
                'file_id' => 'required|integer|exists:bailian_files,id',
            ], [
                'file_id.required' => '缺少文档id',
                'file_id.integer'  => '文档id必须为整数',
                'file_id.exists'   => '文档不存在',
            ]);

            if ($validator->fails()) {
                throw new ValidatorException($validator->errors()->first());
            }

            $file_id = $request->input('file_id');
            $file    = BailianFile::find($file_id);
            $user    = $request->kernel->user();

            if (! $file->canDelete($user)) {
                $reason = $file->getDeleteBlockReason($user);
                throw new ValidatorException($reason ?: '您没有权限删除该文档');
            }

            $file->delete();

            DB::commit();
            return $request->kernel->success('文档删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * Notes: 将文档加入知识库
     *
     * @Author: 玄尘
     * @Date: 2025/5/8 16:37
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ValidatorException
     * @throws \Throwable
     */
    public function joinKnowledge(AddToKnowledgeRequest $request): JsonResponse
    {
        $fileId            = $request->file_id;
        $knowledgeId       = $request->knowledge_id;
        $knowledge_item_id = $request->knowledge_item_id ?? null;

        $file = BailianFile::find($fileId);
        $user = $request->kernel->user();
        if (! $file->canMoveKnowledge($user)) {
            return $request->kernel->error('您没有权限将该文档加入知识库');
        }

        $knowledge = BailianKnowledge::find($knowledgeId);

        if (! $knowledge->canSetData($user)) {
            return $this->error('您没有权限将文档添加到该知识库');
        }

        try {
            // 将文档同步加入知识库
            $file->addToKnowledgeBase($knowledgeId, null, [
                'parent_id' => $knowledge_item_id
            ]);

            return $request->kernel->success('文档已成功加入知识库');
        } catch (\Exception $e) {
            return $request->kernel->error('将文档加入知识库失败: '.$e->getMessage());
        }
    }

    public function getDownloadUrl(Request $request)
    {
        $fileId = $request->file_id;
        if (! $fileId) {
            return $request->kernel->error('缺少文件id');
        }
        $user = $request->kernel->user();

        $file = BailianFile::find($fileId);
        if ($file->user_id != $user->id) {
            return $request->kernel->error('您没有权限下载该文件');
        }
        return $request->kernel->success([
            'title' => $file->title,
            'url'   => $file->getDownloadUrl()
        ]);
    }

}
