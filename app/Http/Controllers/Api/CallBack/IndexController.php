<?php

namespace App\Http\Controllers\Api\CallBack;

use App\Http\Controllers\Controller;
use App\Models\AudioRoom;
use App\Models\AudioRoomsCallback;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class IndexController extends Controller
{
    private string $appId = '67a52003624a0e01a0a2eb30';

    public function aiAgentCall(Request $request)
    {
        $data      = $request->all();
        $eventData = json_decode($data['EventData'], true);
        $room      = AudioRoom::where('room_id', $eventData['RoomId'])->first();
        AudioRoomsCallback::updateOrCreate([
            'event_id' => $data['EventId'],
            'room_id'  => $room->id,
        ], [
            'type'       => $data['EventType'],
            'event_data' => $eventData,
            'created_at' => Carbon::parse($data['EventTime']),
        ]);
        match ($data['EventType']) {
            //            'VoiceChat' => $this->VoiceChat($eventData),
            //            'RoomCreate' => $this->RoomCreate($eventData),
            //            'RoomDestroy' => $this->RoomDestroy($eventData),
            'UserJoinRoom' => $this->UserJoinRoom($eventData),
            'UserLeaveRoom' => $this->UserLeaveRoom($eventData),
            default => '',
        };
    }

    private function UserJoinRoom($EventData)
    {
        if (Str::startsWith($EventData['UserId'], 'user_')) {
            $time = date('Y-m-d H:i:s', $EventData['Timestamp'] / 1000);
            AudioRoom::where('room_id', $EventData['RoomId'])->update([
                'user_join_room' => $time,
            ]);
        }
    }

    private function UserLeaveRoom($EventData)
    {
        $room = AudioRoom::where('room_id', $EventData['RoomId'])->first();
        if ($room && Str::startsWith($EventData['UserId'], 'user_')) {
            $room->dismissRoom();
        }
    }

}
