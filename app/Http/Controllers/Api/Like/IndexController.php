<?php

namespace App\Http\Controllers\Api\Like;

use App\Http\Controllers\Controller;
use App\Http\Resources\Like\LikeBusinessCollection;
use App\Models\Business;
use Illuminate\Http\Request;
use Modules\Interaction\Models\Like;

class IndexController extends Controller
{
    public function lists(Request $request)
    {
        $user = $request->kernel->user();

        //我的名片
        $myBusinessIds = $user->businesses()
            ->pluck('id')->toArray();

        $likes = Like::where('likeable_type', 'business')
            ->whereIn('likeable_id', $myBusinessIds)
            ->with(['likeable'])
            ->orderByDesc('created_at')
            ->paginate($request->pagesize ?: 10);
        return $request->kernel->success(new LikeBusinessCollection($likes));
    }

    public function setLike(Request $request)
    {
        $request->kernel->validate([
            'bid' => 'required|exists:business,id',
        ], [
            'bid.required' => '请传入要点赞的名片',
            'bid.exists'   => '名片不存在',
        ]);
        $request->bid;

        $user     = $request->kernel->user();
        $business = Business::find($request->bid);
        $exists   = Like::ofUser($user)
            ->ofItem($business)
            ->first();

        if ($exists) {
            $exists->delete();
            return $request->kernel->success(['is_like' => 0], '取消点赞');
        } else {
            Like::create([
                'likeable_type' => $business->getMorphClass(),
                'likeable_id'   => $business->getKey(),
                'user_id'       => $user->id,
            ]);
            return $request->kernel->success(['is_like' => 1], '点赞成功');
        }
    }

}
