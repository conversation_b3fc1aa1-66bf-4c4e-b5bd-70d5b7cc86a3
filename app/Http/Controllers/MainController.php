<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class MainController extends Controller
{
    public function index(Request $request)
    {
        // 从请求中获取命令
        $command = $request->kernel->getBody('CMD');

        // 创建一个新的请求对象，指向指定的路由
        $nextRequest = Request::create(
            uri: route($command),
            method: 'POST',
            parameters: array_merge([
                'kernel' => $request->kernel,
                'debug'  => $request->debug,
            ], $request->kernel->toArray())
        );

        // 设置新请求的授权头
        $nextRequest->headers->set('authorization', $request->headers->get('authorization'));

        // 处理新请求并返回响应
        return app()->handle($nextRequest);
    }
}