<?php

namespace App\Http\Controllers;

use App\Facades\Api;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Response;

abstract class ApiController extends Controller
{

    /**
     * Notes   : 成功数据
     *
     * @Date   : 2022/12/23 21:48
     * <AUTHOR> <Jason.C>
     * @param  mixed  $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function success(mixed $data = ''): JsonResponse
    {
        return $this->respond(
            data: $data,
            message: 'SUCCESS'
        );
    }

    /**
     * Notes   : 结果返回
     *
     * @Date   : 2022/12/23 21:48
     * <AUTHOR> <Jason.C>
     * @param  mixed  $data
     * @param  int  $code
     * @param  string  $message
     * @param  array  $header
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respond(
        mixed $data = '',
        int $code = 200,
        string $message = '',
        array $header = []
    ): JsonResponse {
        $response = [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
        return Response::json($response, 200, $header);
    }

    /**
     * Notes   : 失败消息
     *
     * @Date   : 2022/12/23 21:48
     * <AUTHOR> <Jason.C>
     * @param  string  $message
     * @param  int  $code
     * @return \Illuminate\Http\JsonResponse
     */
    public function failed(
        string $message = '',
        int $code = 500
    ): JsonResponse {
        return $this->respond(
            code: $code,
            message: $message
        );
    }

    /**
     * @throws \Exception
     */
    protected function checkPermission(Model $model, string $message = '数据不存在')
    {
        if ($model->user->isNot(Api::user())) {
            throw new Exception($message, 404);
        }
    }
}