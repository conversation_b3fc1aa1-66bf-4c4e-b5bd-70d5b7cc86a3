<?php

namespace App\Http\Resources\Realname;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RealnameResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'user_id'    => $this->user_id,
            'nickname'   => $this->nickname,
            'phone'      => $this->phone,
            'card_num'   => $this->card_num,
            'is_check'   => $this->is_check,
            'created_at' => $this->created_at->toDateTimeString(),
        ];
    }
}