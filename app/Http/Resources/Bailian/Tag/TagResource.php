<?php

namespace App\Http\Resources\Bailian\Tag;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TagResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'             => $this->id,
            'name'           => $this->name,
            'articles_count' => $this->articles_count,
            'created_at'     => (string) $this->created_at,
        ];
    }
} 