<?php

namespace App\Http\Resources\Bailian\Article;

use App\Http\Resources\Bailian\Knowledge\KnowledgeItemResource;
use App\Http\Resources\Bailian\Tag\TagResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleBaseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'              => $this->id,
            'title'           => $this->getTitle(),
            'content'         => $this->getResourceContent(),
            'description'     => $this->getDescription(),
            'type'            => [
                'value' => $this->type->value,
                'text'  => $this->type->toString(),
            ],
            'parent_id'       => $this->parent_id,
            'count'           => [
                'browse_count'   => $this->browse_count,
                'children_count' => $this->children_count,
            ],
            'tags'            => TagResource::collection($this->tags),
            'knowledge_item'  => $this->knowledgeItem ? new KnowledgeItemResource($this->knowledgeItem) : null,
            'can'             => $this->getCan($request->kernel->user()),
            'is_in_knowledge' => $this->isInKnowledge(),
            'created_at'      => (string) $this->created_at,
            'diff_for_humans' => $this->created_at->diffForHumans(),
            'time_scale'      => $this->getTimeScale($this),
        ];
    }

    public function getTimeScale()
    {
        $diffDay = $this->created_at->diffInDays();
        if ($diffDay < 1) {
            return '今天';
        }

        if ($diffDay < 7) {
            return '过去7天';
        }

        return '过去30天';
    }
}
