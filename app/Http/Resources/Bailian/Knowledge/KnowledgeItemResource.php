<?php

namespace App\Http\Resources\Bailian\Knowledge;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KnowledgeItemResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        return [
            'id'                => $this->id,
            'title'             => $this->title,
            'bailian_knowledge' => $this->bailianKnowledge ? new KnowledgeBaseInfoResource($this->bailianKnowledge) : null,
        ];
    }
}
