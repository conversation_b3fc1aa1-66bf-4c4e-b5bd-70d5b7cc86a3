<?php

namespace App\Http\Resources\Bailian\Knowledge;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class KnowledgeCollection extends BaseWateCollection
{
    /**
     * 将资源集合转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($knowledge) {
                return new KnowledgeBaseResource($knowledge);
            }),
        ], $this->page());
    }
}
