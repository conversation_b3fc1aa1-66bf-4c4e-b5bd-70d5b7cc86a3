<?php

namespace App\Http\Resources\Bailian\Knowledge;

use App\Http\Resources\Bailian\CategoryResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\User\Http\Resources\UserBaseInfoResource;

class KnowledgeNoAuthResource extends JsonResource
{

    /**
     * 将资源转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        $user = '';
        return [
            'id'                   => $this->id,
            'name'                 => $this->name,
            'bailian_knowledge_id' => $this->knowledge_id,
            'user'                 => $this->user ? new UserBaseInfoResource($this->user) : null,
            'description'          => $this->description,
            'source_type'          => [
                'value' => $this->source_type,
                'text'  => $this->source_type_text
            ],
            'sink_type'            => [
                'value' => $this->sink_type,
                'text'  => $this->sink_type_text,
            ],
            'status'               => $this->status,
            'level'                => [
                'value' => $this->level->value,
                'text'  => $this->level->toString(),
            ],
            'can'                  => $this->getCan($user),
            'count'                => $this->getCount(),
            'base_size'            => $this->getBaseSize(),
            'is_featured'          => $this->is_featured,
            'auto_join'            => (bool) $this->auto_join,
            'join_status'          => $this->getJoinStatus($user),
            'categories'           => $this->categories ? CategoryResource::collection($this->categories) : null,
            'share_url'            => $this->getShareUrl(),
            'created_at'           => (string) $this->created_at,
        ];
    }
}
