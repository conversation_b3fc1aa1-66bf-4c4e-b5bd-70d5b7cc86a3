<?php

namespace App\Http\Resources\Bailian\Member;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\User\Http\Resources\UserBaseInfoResource;

class KnowledgeMemberResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'user'       => new UserBaseInfoResource($this->user),
            'position'   => [
                'value' => $this->position,
                'text'  => $this->positionText,
            ],
            'status'     => [
                'value' => $this->status,
                'text'  => $this->status->toString(),
            ],
            'can'        => $this->getCan($request->kernel->user()),
            'created_at' => (string) $this->created_at,
        ];
    }
}
