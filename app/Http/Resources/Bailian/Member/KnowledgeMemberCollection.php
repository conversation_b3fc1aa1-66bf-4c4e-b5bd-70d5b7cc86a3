<?php

namespace App\Http\Resources\Bailian\Member;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class KnowledgeMemberCollection extends BaseWateCollection
{
    /**
     * 将资源集合转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($member) {
                return new KnowledgeMemberResource($member);
            }),
        ], $this->page());
    }
}
