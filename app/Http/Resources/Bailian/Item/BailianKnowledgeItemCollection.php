<?php

namespace App\Http\Resources\Bailian\Item;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class BailianKnowledgeItemCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($info) {
                return new BailianKnowledgeItemResource($info);
            }),
        ], $this->page());
    }
}
