<?php

namespace App\Http\Resources\Bailian\Item;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BailianKnowledgeItemBaseResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        return [
            'id'        => $this->id,
            'title'     => $this->title,
            'parent_id' => $this->parent_id,
        ];
    }

}
