<?php

namespace App\Http\Resources\Bailian;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'name'       => $this->name,
            'bailian_id' => $this->bailian_id,
            'type'       => $this->type,
            'parent_id'  => $this->parent_id,
            'status'     => $this->status,
            'created_at' => (string) $this->created_at,
        ];
    }
}
