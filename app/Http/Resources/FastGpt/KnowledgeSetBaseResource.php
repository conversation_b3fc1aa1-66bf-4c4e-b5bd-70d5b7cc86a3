<?php

namespace App\Http\Resources\FastGpt;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KnowledgeSetBaseResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'            => $this->id,
            'name'          => $this->name,
            'collection_id' => $this->collection_id,
            'dataset_id'    => $this->knowledge->dataset_id,
            'note_id'       => $this->note_id,
            'note_file_id'  => $this->note_file_id,
            'type'          => [
                'value' => $this->type->value,
                'text'  => $this->type->toString(),
            ],
            'children'      => $this->when($this->children, function () {
                return KnowledgeSetResource::collection($this->children);
            }),
            'created_at'    => (string) $this->created_at,
        ];
    }
}