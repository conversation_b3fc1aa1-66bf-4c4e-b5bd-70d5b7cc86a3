<?php

namespace App\Http\Resources\FastGpt\Article;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class ArticleAndKnowledgeCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return new ArticleAndKnowledgeResource($item);
            }),
        ], $this->page());
    }
}