<?php

namespace App\Http\Resources\FastGpt\Article;

use App\Http\Resources\InteractionResourceTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\User\Http\Resources\UserBaseInfoResource;

class ArticleBaseInteractionResource extends JsonResource
{
    use InteractionResourceTrait;

    public function toArray(Request $request)
    {
        $user = $request->kernel->user();
        return [
            'id'          => $this->id,
            'title'       => $this->title,
            'user'        => new UserBaseInfoResource($this->user),
            'interaction' => $this->getInteraction(),
            'can'         => $this->getCan($user),
            'created_at'  => (string) $this->created_at,
        ];
    }
}