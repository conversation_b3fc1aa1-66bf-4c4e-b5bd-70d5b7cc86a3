<?php

namespace App\Http\Resources\FastGpt\Article;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class ArticleCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return new ArticleBaseResource($item);
            }),
        ], $this->page());
    }
}