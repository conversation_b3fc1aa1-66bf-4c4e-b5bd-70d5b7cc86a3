<?php

namespace App\Http\Resources\FastGpt;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KnowledgeSetBaseInfoResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'name'       => $this->name,
            'knowledge'  => [
                'id'   => $this->knowledge->id,
                'name' => $this->knowledge->name,
            ],
            'created_at' => (string) $this->created_at,
        ];
    }
}