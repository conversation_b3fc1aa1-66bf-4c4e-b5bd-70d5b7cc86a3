<?php

namespace App\Http\Resources\FastGpt;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KnowledgeResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'          => $this->id,
            'name'        => $this->name,
            'dataset_id'  => $this->dataset_id,
            'type'        => $this->type,
            'level'       => [
                'value' => $this->level->value,
                'text'  => $this->level->toString()
            ],
            'description' => $this->description,
            'can'         => $this->getCan($request->kernel->user()),
            'sets_count'  => $this->knowledge_sets_count ?? 0,
            'created_at'  => (string) $this->created_at,
        ];
    }

}