<?php

namespace App\Http\Resources\FastGpt;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class KnowledgeSetArticleCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($info) {
                return new KnowledgeSetArticleResource($info);
            }),
        ], $this->page());
    }
}