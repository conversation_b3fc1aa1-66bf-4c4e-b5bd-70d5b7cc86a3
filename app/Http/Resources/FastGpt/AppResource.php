<?php

namespace App\Http\Resources\FastGpt;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AppResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'          => $this->id,
            'app_id'      => $this->app_id,
            'name'        => $this->name,
            'image'       => $this->image,
            'welcome'     => $this->welcome,
            'model'       => $this->model,
            'prologue'    => $this->prologue,
            'description' => $this->description,
            'level'       => [
                'value' => $this->level->value,
                'text'  => $this->level->toString(),
            ],
            'status'      => [
                'value' => $this->status,
                'text'  => $this->getStatusText(),
            ],
            'knowledges'  => KnowledgeBaseInfoResource::collection($this->getKnowledges()),
            'can'         => $this->getCan($request->kernel->user()),
            'created_at'  => (string) $this->created_at,
        ];
    }
}