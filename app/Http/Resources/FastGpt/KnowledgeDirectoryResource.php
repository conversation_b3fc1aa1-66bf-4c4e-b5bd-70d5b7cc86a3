<?php

namespace App\Http\Resources\FastGpt;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KnowledgeDirectoryResource extends JsonResource
{
    public function toArray(Request $request)
    {
        $children = $this->children()->orderBy('order')->latest('created_at')->get();
        return [
            'id'            => $this->id,
            'name'          => $this->name,
            'collection_id' => $this->collection_id,
            'dataset_id'    => $this->knowledge->dataset_id,
            'note_id'       => $this->note_id,
            'article_id'    => $this->fastgpt_knowledge_article_id,
            'type'          => [
                'value' => $this->type->value,
                'text'  => $this->type->toString(),
            ],
            'order'         => $this->order,
            'parent_id'     => $this->parent_id,
            'children'      => $this->when($children, function () use ($children) {
                return self::collection($children);
            }),
        ];
    }
}