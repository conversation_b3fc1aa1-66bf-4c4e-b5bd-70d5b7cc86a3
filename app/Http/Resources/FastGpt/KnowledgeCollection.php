<?php

namespace App\Http\Resources\FastGpt;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class KnowledgeCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge($this->page(), [
            'data' => $this->collection->map(function ($knowledge) {
                return new  KnowledgeResource($knowledge);
            }),
        ]);
    }
}