<?php

namespace App\Http\Resources\Report;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\User\Http\Resources\UserBaseHiddenResource;

class ReportItemResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'             => $this->id,
            'user'           => new UserBaseHiddenResource($this->user),
            'model'          => match ($this->modelable_type) {
                'user' => [
                    'model' => $this->modelable_type,
                    'id'    => $this->modelable_id,
                    'title' => '举报 用户:'.$this->modelable->showName,
                ],
                'unify_asset' => [
                    'model' => $this->modelable_type,
                    'id'    => $this->modelable_id,
                    'title' => '举报 作品:'.$this->modelable->no,
                ]
            },
            'report_content' => $this->report_content,
            'pictures'       => $this->pictureUrls,
            'status'         => [
                'code' => $this->status,
                'text' => $this->status_text,
            ],
            'cans'           => $this->cans(),
            'created_at'     => $this->created_at->toDateTimeString(),
        ];
    }
}