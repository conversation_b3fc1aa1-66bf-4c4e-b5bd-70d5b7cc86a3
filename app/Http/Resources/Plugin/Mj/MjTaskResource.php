<?php

namespace App\Http\Resources\Plugin\Mj;

use App\Http\Resources\InteractionResourceTrait;
use App\Http\Resources\User\OtherBaseInfoResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MjTaskResource extends JsonResource
{
    use InteractionResourceTrait;

    public function toArray(Request $request)
    {
        return [
            'id'            => $this->id,
            'author'        => new OtherBaseInfoResource($this->user),
            'no'            => $this->no,
            'task_id'       => $this->task_id,
            'prompt'        => $this->prompt,
            'inputs'        => $this->inputs,
            'cover'         => $this->coverUrlAttr,
            'type'          => $this->type,
            'type_text'     => $this->type_text,
            'status'        => [
                'code' => $this->status,
                'text' => $this->status_text,
            ],
            'error_message' => $this->error_message,
            'cans'          => $this->cans(),
            'created_at'    => $this->created_at->toDateTimeString(),
            'overed_at'     => $this->over_at?->toDateTimeString() ?: '',
            'interaction'   => $this->getInteraction(),
        ];
    }
}