<?php

namespace App\Http\Resources\Plugin\AiPPT;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AiPPtTaskResource extends JsonResource
{
    public function toArray(Request $request)
    {
        $showOutline = $this->additional['show_outline'] ?? false;
        $showContent = $this->additional['show_content'] ?? false;
        return [
            'id'         => $this->id,
            'title'      => $this->title,
            'audience'   => $this->audience,
            'scene'      => $this->scene,
            'tone'       => $this->tone,
            'language'   => $this->language,
            'outline'    => $showOutline ? $this->outline : [],
            'content'    => $showContent ? $this->content : [],
            'file_url'   => $this->fileUrlAttr,
            'status'     => [
                'code' => $this->status,
                'text' => $this->status_text,
            ],
            'created_at' => $this->created_at->toDateTimeString(),
        ];
    }
}