<?php

namespace App\Http\Resources\Plugin\Keling;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class ListCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return $item->getAssetResource();
            }),
        ], $this->page());
    }
}