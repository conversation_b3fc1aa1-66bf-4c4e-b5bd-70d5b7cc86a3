<?php

namespace App\Http\Resources\Like;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class LikeBusinessCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                $business = $item->user->businesses()
                    ->orderByDesc('is_default')
                    ->first();
                if ($business) {
                    return (new LikeBusinessResource($business))->additional([
                        'like' => $item,
                    ]);
                } else {
                    return (new LikeUserResource($item->user))->additional([
                        'like' => $item,
                    ]);
                }
            }),
        ], $this->page());
    }
}