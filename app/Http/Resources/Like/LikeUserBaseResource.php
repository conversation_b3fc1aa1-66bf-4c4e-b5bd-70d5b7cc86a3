<?php

namespace App\Http\Resources\Like;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LikeUserBaseResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'user_id'  => $this->user_id,
            'username' => $this->user->username,
            'nickname' => $this->user->info->nickname,
            'avatar'   => $this->user->info->avatar_url,
        ];
    }
}