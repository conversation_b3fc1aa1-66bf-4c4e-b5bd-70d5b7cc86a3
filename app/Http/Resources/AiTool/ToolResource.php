<?php

namespace App\Http\Resources\AiTool;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ToolResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'          => $this->id,
            'name'        => $this->name,
            'category'    => new CategoryResource($this->category),
            'cover'       => $this->cover_url,
            'description' => $this->description,
            'order'       => $this->order,
            'price'       => $this->price,
            'url'         => $this->url,
            'created_at'  => (string) $this->created_at,
        ];
    }
}