<?php

namespace App\Http\Resources\Resource;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SystemResourceResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'name'       => $this->name,
            'type'       => $this->type,
            'key'        => $this->key,
            'url'        => $this->cover_url,
            'order'      => $this->order,
            'created_at' => (string) $this->created_at,
        ];
    }
}