<?php

namespace App\Http\Resources\User;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class AccountLogCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return new AccountLogResource($item);
            }),
            'page' => $this->page(),
        ];
    }
}