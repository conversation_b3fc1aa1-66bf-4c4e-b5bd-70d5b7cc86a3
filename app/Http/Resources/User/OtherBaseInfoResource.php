<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Interaction\Models\Follow;

class OtherBaseInfoResource extends JsonResource
{
    public function toArray(Request $request)
    {
        $authUser = request()->kernel->guestUser();
        $can      = $authUser ? ($authUser->id != $this->id) : false;
        return [
            'user_id'  => $this->id,
            'username' => $this->username,
            'nickname' => $this->info?->nickname,
            'avatar'   => $this->info?->avatar_url,
            'follow'   => [
                'can'      => $can,
                'followed' => $can ? Follow::ofUser($authUser)
                    ->ofItem($this->resource)->exists() : false,
            ],
        ];
    }
}