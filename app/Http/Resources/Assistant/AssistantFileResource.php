<?php

namespace App\Http\Resources\Assistant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AssistantFileResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'file_id'       => $this->file_id,
            'name'          => $this->name,
            'original_name' => $this->storage?->original,
            'url'           => $this->storage?->path_url,
        ];
    }
}