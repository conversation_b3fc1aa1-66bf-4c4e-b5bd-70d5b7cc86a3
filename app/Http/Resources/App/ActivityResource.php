<?php

namespace App\Http\Resources\App;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ActivityResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'          => $this->id,
            'title'       => $this->title,
            'cover'       => $this->coverUrl,
            'description' => $this->description,
            'rules'       => $this->rules,
            'type'        => $this->type,
            'type_text'   => $this->type_text,
            'status'      => [
                'code' => $this->status,
                'text' => $this->status_text,
            ],
            'score'       => $this->score,
            'start_at'    => $this->start_at->toDateTimeString(),
            'end_at'      => $this->end_at->toDateTimeString(),
            'count'       => [
                'asset' => $this->items()->count(),
                'user'  => 0,
            ],
        ];
    }
}