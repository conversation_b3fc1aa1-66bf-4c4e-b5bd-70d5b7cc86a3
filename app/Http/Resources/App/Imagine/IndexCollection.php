<?php

namespace App\Http\Resources\App\Imagine;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class IndexCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
//        $items = $this->collection->reverse();
        return [
            'data' => $this->collection->map(function ($item) {
                return $item->assetable->getAssetResource();
            })->toArray(),
            'page' => $this->page(),
        ];
    }
}