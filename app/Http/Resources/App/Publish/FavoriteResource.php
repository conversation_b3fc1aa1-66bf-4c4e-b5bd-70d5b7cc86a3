<?php

namespace App\Http\Resources\App\Publish;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FavoriteResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'title'      => $this->favoriteable->getInteractionTitle(),
            'cover'      => $this->favoriteable->getInteractionCover(),
            'target'     => $this->favoriteable->getInteractionTarget(),
            'created_at' => $this->created_at->toDateTimeString(),
        ];
    }
}