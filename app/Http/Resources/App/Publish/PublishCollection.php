<?php

namespace App\Http\Resources\App\Publish;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class PublishCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return $item->assetable->getAssetResource();
            })->toArray(),
        ], [
            'page' => $this->page(),
        ]);
    }
}