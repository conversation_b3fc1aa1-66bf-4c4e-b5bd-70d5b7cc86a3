<?php

namespace App\Http\Resources\App\Publish;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class LikeCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return $item->likeable->assetable->getAssetResource();
            })->toArray(),
            'page' => $this->page(),
        ];
    }
}