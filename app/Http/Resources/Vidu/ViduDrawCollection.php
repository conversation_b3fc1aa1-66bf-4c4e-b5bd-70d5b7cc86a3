<?php

namespace App\Http\Resources\Vidu;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class ViduDrawCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return new ViduDrawResource($item);
            }),
        ], $this->page());
    }
}