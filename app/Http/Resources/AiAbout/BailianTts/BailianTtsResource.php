<?php

namespace App\Http\Resources\AiAbout\BailianTts;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BailianTtsResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            "voice_id"   => $this->voice_id,
            "people"     => $this->people,
            "url"        => $this->urlUrlAttr,
            "model"      => $this->model,
            "scenarios"  => $this->scenarios,
            "image_url"  => $this->imageUrlAttr,
            "lang"       => $this->lang,
            'created_at' => $this->created_at?->toDateTimeString() ?: '',
            'updated_at' => $this->updated_at?->toDateTimeString() ?: '',
        ];
    }
}
