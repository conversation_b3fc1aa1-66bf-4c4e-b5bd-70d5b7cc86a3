<?php

namespace App\Http\Resources\Notification;

use App\Http\Resources\BaseCollection;
use Modules\Notification\Http\Resources\NotificationResource;

class NotificationCollection extends BaseCollection
{
    public function toArray($request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return new NotificationResource($item);
            }),
            'page' => $this->page(),
        ];
    }
}
