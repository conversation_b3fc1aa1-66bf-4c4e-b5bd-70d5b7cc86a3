<?php

namespace App\Http\Resources\Draw;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ImageResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'no'       => $this->no,
            'user_id'  => $this->user_id,
            'message'  => $this->prompt,
            'form_url' => $this->image_url,
            'size'     => $this->size->name,
            'style'    => $this->style->name,
            'url'      => $this->cover_url,
            'cans'     => $this->cans(),
            'status'   => [
                'code' => $this->status,
                'text' => $this->status_text,
            ],
        ];
    }
}