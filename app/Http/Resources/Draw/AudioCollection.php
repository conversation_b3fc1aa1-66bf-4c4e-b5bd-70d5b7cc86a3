<?php

namespace App\Http\Resources\Draw;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class AudioCollection extends BaseWateCollection
{
    public function __construct($resource, protected bool $showCan = true)
    {
        parent::__construct($resource);
    }

    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return (new AudioResource($item))->additional([
                    'showCan' => $this->showCan
                ]);
            }),
        ], $this->page()); // TODO: Change the autogenerated stub
    }
}