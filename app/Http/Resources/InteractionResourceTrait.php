<?php

namespace App\Http\Resources;

use Modules\Interaction\Models\Favorite;
use Modules\Interaction\Models\Like;
use Modules\Interaction\Models\Subscribe;

trait InteractionResourceTrait
{
    public function getInteraction()
    {
        $user = request()->kernel->guestUser();
        return [
            'model'  => $this->getMorphClass(),
            'id'     => $this->getKey(),
            'status' => [
                'liked'     => $user && Like::ofUser($user)->withTarget([
                        'target_type' => $this->getMorphClass(),
                        'target_id'   => $this->getKey(),
                    ])->exists(),
                'favorite'  => $user && Favorite::ofUser($user)->withTarget([
                        'target_type' => $this->getMorphClass(),
                        'target_id'   => $this->getKey(),
                    ])->exists(),
                'subscribe' => $user && Subscribe::ofUser($user)->withTarget([
                        'target_type' => $this->getMorphClass(),
                        'target_id'   => $this->getKey(),
                    ])->exists(),
            ],
            'count'  => [
                'like'      => $this->likeable()->count(),
                'comment'   => $this->commentable()->count(),
                'favorite'  => $this->favoriteable()->count(),
                'subscribe' => $this->subscribable()->count(),
            ],
            'like'   => 0,
            'step'   => 0,
        ];
    }
}