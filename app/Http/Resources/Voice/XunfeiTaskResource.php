<?php

namespace App\Http\Resources\Voice;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class XunfeiTaskResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'        => $this->id,
            'task_id'   => $this->task_id,
            'segs'      => $this->logs->map(function ($item) {
                return [
                    'text_id'   => $item->text_id,
                    'seg_id'    => $item->seg_id,
                    'audio_url' => $item->audioUrlAttr,
                    'flag'      => $item->flag,
                ];
            })->toArray(),
            'voice_id'  => $this->voice_id,
            'model'     => $this->model,
            'people'    => $this->people,
            'scenarios' => $this->scenarios,
            'image_url' => $this->imageUrlAttr,
            'url'       => $this->urlUrlAttr,
            'status'    => [
                'code' => $this->status,
                'text' => $this->status_text,
            ],
            'cans'      => $this->cans(),
        ];
    }
}