<?php

namespace App\Http\Resources\Note;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NoteTagResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'name'       => $this->name,
            'note_count' => $this->notes_count ?? 0,
            'created_at' => (string) $this->created_at,
        ];
    }
}