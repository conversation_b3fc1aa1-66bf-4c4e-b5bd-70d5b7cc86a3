<?php

namespace App\Http\Resources\Note;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class NoteCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return new NoteBaseResource($item);
            }),
        ], $this->page());
    }
}