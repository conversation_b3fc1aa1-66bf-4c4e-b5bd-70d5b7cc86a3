<?php

namespace App\Http\Resources\Note;

use App\Http\Resources\InteractionResourceTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\User\Http\Resources\UserBaseInfoResource;

class NoteResource extends JsonResource
{
    use InteractionResourceTrait;

    public function toArray(Request $request)
    {
        return [
            'id'           => $this->id,
            'title'        => $this->title,
            'content'      => $this->content,
            'browse_count' => $this->browse_count ?? 0,
            'type'         => [
                'value' => $this->type,
                'text'  => $this->type_text
            ],
            'user'         => new UserBaseInfoResource($this->user),
            'tags'         => NoteTagBaseResource::collection($this->tags),
            'interaction'  => $this->getInteraction(),
            'created_at'   => (string) $this->created_at,
        ];
    }
}