<?php

namespace App\Http\Resources\VolcengineRtc;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ConfigResource extends JsonResource
{
    public function toArray(Request $request)
    {
        $voice = $this->getVoice();
        return [
            'role_id'         => $this->role->id,
            'voice_name'      => $voice->name ?? '',
            'voice_audio_url' => $voice->audio_url ?? '',
            'voice_type'      => $this->tts['audio']['voice_type'] ?? '',
            'pitch_rate'      => $this->tts['audio']['pitch_rate'] ?? 0,
            'speech_rate'     => $this->tts['audio']['speech_rate'] ?? 0,
            'role_name'       => $this->role->role_name,
            'logo'            => $this->role->logo,
            'description'     => $this->role->description,
            'image_model'     => $this->image_model,
        ];
    }
}