<?php

use App\Http\Controllers\Api\Department\IndexController;
use App\Http\Controllers\Api\Department\RoleController;
use App\Http\Controllers\Api\Department\StaffController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'departments'
], function (Router $router) {
    $router->post('', [IndexController::class, 'index'])->name('departments.index');
    $router->post('show', [IndexController::class, 'show'])->name('departments.show');
    $router->post('create', [IndexController::class, 'create'])->name('departments.create');
    $router->post('update', [IndexController::class, 'update'])->name('departments.update');
    $router->post('delete', [IndexController::class, 'delete'])->name('departments.delete');

    //角色
    $router->post('roles', [RoleController::class, 'index'])->name('departments.roles');
    $router->post('roles/join', [RoleController::class, 'join'])->name('departments.roles.join');
    $router->post('roles/leave', [RoleController::class, 'leave'])->name('departments.roles.leave');
    //员工
    $router->post('staff/index', [StaffController::class, 'index'])->name('departments.staffs');
    $router->post('staff/departments', [StaffController::class, 'departments'])->name('departments.staff.departments');
    $router->post('staff/join', [StaffController::class, 'join'])->name('departments.staff.join');
    $router->post('staff/remove', [StaffController::class, 'remove'])->name('departments.staff.remove');
});