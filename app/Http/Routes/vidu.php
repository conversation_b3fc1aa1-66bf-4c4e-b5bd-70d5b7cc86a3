<?php

use App\Http\Controllers\Api\Vidu\IndexController;
use App\Http\Controllers\Api\Vidu\TemplateController;
use App\Http\Controllers\Api\Vidu\AudioController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
    'prefix'     => 'vidu'
], function (Router $router) {
    // 视频模板相关路由
    $router->post('categories', [TemplateController::class, 'categories'])->name('vidu.template.categories');
    $router->post('index', [TemplateController::class, 'index'])->name('vidu.template.list');
    $router->post('show', [TemplateController::class, 'show'])->name('vidu.template.show');

    // 视频生成相关路由
    $router->post('draw', [IndexController::class, 'draw'])->name('vidu.draw');
    $router->post('draw/index', [IndexController::class, 'index'])->name('vidu.draw.index');
    $router->post('query', [IndexController::class, 'query'])->name('vidu.query');

    // 可控音效相关路由
    $router->post('audio/draw', [AudioController::class, 'draw'])->name('vidu.audio.draw');
    $router->post('audio/index', [AudioController::class, 'index'])->name('vidu.audio.index');
    $router->post('audio/query', [AudioController::class, 'query'])->name('vidu.audio.query');
    $router->post('audio/delete', [AudioController::class, 'destroy'])->name('vidu.audio.delete');
});
