<?php

use App\Http\Controllers\Api\Business\BusinessController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'business'
], function (Router $router) {
    $router->post('add', [BusinessController::class, 'add'])->name('business.addBusiness');
    $router->post('my', [BusinessController::class, 'my'])->name('business.myBusiness');
    $router->post('delete', [BusinessController::class, 'delete'])->name('business.delBusiness');
    $router->post('switch', [BusinessController::class, 'switch'])->name('business.switchBusiness');
    $router->post('copy', [BusinessController::class, 'copy'])->name('business.copyBusiness');
    $router->post('resources', [BusinessController::class, 'resources'])->name('business.resources');
    $router->post('detail', [BusinessController::class, 'detail'])->name('business.detailBusiness');
    $router->post('send', [BusinessController::class, 'send'])->name('business.sendBusiness');
    $router->post('browse', [BusinessController::class, 'browse'])->name('business.browseBusiness');
    $router->post('exchange', [BusinessController::class, 'exchange'])->name('business.exchangeBusiness');
    $router->post('agree', [BusinessController::class, 'agreeExchage'])->name('business.receiveBusiness');
    $router->post('info', [BusinessController::class, 'businessInfo'])->name('business.businessData');
    $router->post('visitor_my', [BusinessController::class, 'visitorMy'])->name('business.visitorBusiness');
    $router->post('my_visitor', [BusinessController::class, 'myVisitor'])->name('business.myVisitorBusiness');
    $router->post('qrcode', [BusinessController::class, 'qrcode'])->name('business.generateQrCode');
});