<?php

use App\Http\Controllers\Api\Company\CompanyController;
use App\Http\Controllers\Api\Company\IndustryController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'company'
], function (Router $router) {
    $router->post('unverifiedCompany', [CompanyController::class, 'unverifiedCompany'])
        ->name('company.unverifiedCompany');
    $router->post('authentication', [CompanyController::class, 'authentication'])->name('company.authentication');
    $router->post('set_code', [CompanyController::class, 'setCode'])->name('company.setCode');
    $router->post('workAuthentication', [CompanyController::class, 'workAuthentication'])
        ->name('company.workAuthentication');
    $router->post('companies', [CompanyController::class, 'companies'])->name('company.getCompany');
    $router->post('info', [CompanyController::class, 'info'])->name('company.companyDetail');
    $router->post('update', [CompanyController::class, 'update'])->name('company.update');
    $router->post('users', [CompanyController::class, 'users'])->name('company.getList');
    $router->post('staffs', [CompanyController::class, 'staffs'])->name('company.commpanyStaff');
    $router->post('removeManage', [CompanyController::class, 'removeManage'])->name('company.removeManage');
    $router->post('updateManage', [CompanyController::class, 'updateManage'])->name('company.updateManage');
    $router->post('addManage', [CompanyController::class, 'addManage'])->name('company.addManage');
    $router->post('searchUser', [CompanyController::class, 'searchUser'])->name('company.searchUser');
    $router->post('updateOpen', [CompanyController::class, 'updateOpen'])->name('company.updateOpen');
    $router->post('leaveWork', [CompanyController::class, 'leaveWork'])->name('company.leaveWork');
    $router->post('qrcode', [CompanyController::class, 'qrcode'])->name('company.qrcode');
    $router->post('manual', [CompanyController::class, 'manual'])->name('company.manual');
    $router->post('uploadExcel', [CompanyController::class, 'uploadExcel'])->name('company.uploadExcel');
    $router->post('checkUser', [CompanyController::class, 'checkUser'])->name('company.checkUser');
    $router->post('userInfo', [CompanyController::class, 'userInfo'])->name('company.checkDetail');
    $router->post('industryList', [IndustryController::class, 'index'])->name('company.industryList');
    $router->post('transference', [CompanyController::class, 'transference'])->name('company.transference');
});
