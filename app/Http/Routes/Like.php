<?php
use App\Http\Controllers\Api\Like\IndexController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('like/setLike', [IndexController::class, 'setLike'])->name('like.setLike');
    $router->post('like/lists', [IndexController::class, 'lists'])->name('like.likeList');

});
