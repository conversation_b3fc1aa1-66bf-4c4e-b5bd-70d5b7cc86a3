<?php

use App\Http\Controllers\App\Asset\ActivityController;
use App\Http\Controllers\App\Asset\AiUnifyAssetController;
use App\Http\Controllers\App\Asset\IndexController;
use App\Http\Controllers\App\Asset\ScoreController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('app/asset/detail', [IndexController::class, 'detail'])
        ->name('app.Asset.Detail');
    $router->post('app/asset/deletes', [AiUnifyAssetController::class, 'deletes'])
        ->name('app.Asset.Deletes');
    $router->post('app/asset/query', [AiUnifyAssetController::class, 'query'])
        ->name('app.Asset.Query');
    $router->post('app/asset/activity/init', [ActivityController::class, 'init'])
        ->name('app.Asset.Activity.Init');
    $router->post('app/asset/activity/list', [ActivityController::class, 'index'])
        ->name('app.Asset.Activity.Lists');
    $router->post('app/asset/activity/info', [ActivityController::class, 'info'])
        ->name('app.Asset.Activity.Info');

    $router->post('app/asset/score/mj', [ScoreController::class, 'mj'])
        ->name('app.Asset.Score.Mj');
    $router->post('app/asset/score/keling', [ScoreController::class, 'keling'])
        ->name('app.Asset.Score.Keling');
    $router->post('app/asset/score/jm', [ScoreController::class, 'jm'])
        ->name('app.Asset.Score.JM');
    $router->post('app/asset/score/suno', [ScoreController::class, 'suno'])
        ->name('app.Asset.Score.Suno');
});