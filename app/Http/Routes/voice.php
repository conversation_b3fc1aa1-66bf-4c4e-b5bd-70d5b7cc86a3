<?php

use App\Http\Controllers\Api\Voice\IndexController;
use App\Http\Controllers\Api\Voice\MyVoiceController;
use App\Http\Controllers\Api\Voice\VolcenginVoiceController;
use App\Http\Controllers\Api\Voice\XunfeiController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    /**
     * 定义POST请求路由，用于获取系统语音列表。
     * 路径: voice/systemVoiceLists
     * 对应控制器方法: IndexController::systemVoiceLists()
     * 路由名称: voice.systemVoiceLists
     */
    $router->post('voice/systemVoiceLists', [IndexController::class, 'systemVoiceLists'])
        ->name('voice.systemVoiceLists');

    $router->post('voice/xunfei/getVcnList', [XunfeiController::class, 'getVcnList'])
        ->name('voice.Xunfei.getVcnList');

    $router->post('voice/xunfei/createTask', [XunfeiController::class, 'createTask'])
        ->name('voice.Xunfei.createTask');

    $router->post('voice/xunfei/getTrainText', [XunfeiController::class, 'getTrainText'])
        ->name('voice.Xunfei.getTrainText');

    $router->post('voice/xunfei/addAudio', [XunfeiController::class, 'addAudio'])
        ->name('voice.Xunfei.addAudio');

    $router->post('voice/xunfei/submitTask', [XunfeiController::class, 'submitTask'])
        ->name('voice.Xunfei.submitTask');
    $router->post('voice/xunfei/lists', [XunfeiController::class, 'lists'])
        ->name('voice.Xunfei.lists');
    $router->post('voice/xunfei/query', [XunfeiController::class, 'query'])
        ->name('voice.Xunfei.Query');

    $router->post('voice/xunfei/getAuth', [XunfeiController::class, 'getAuth'])
        ->name('voice.Xunfei.getAuth');
    /**
     * 定义POST请求路由，用于创建新的语音条目。
     * 路径: voice/create
     * 对应控制器方法: IndexController::create()
     * 路由名称: voice.create
     */
    $router->post('voice/create', [IndexController::class, 'create'])
        ->name('voice.create');

    /**
     * 定义POST请求路由，用于获取当前用户的语音列表。
     * 路径: voice/myVoice
     * 对应控制器方法: MyVoiceController::index()
     * 路由名称: voice.MyVoice
     */
    $router->post('voice/myVoice', [MyVoiceController::class, 'index'])
        ->name('voice.MyVoice');

    /**
     * 定义POST请求路由，用于删除语音条目。
     * 路径: voice/delete
     * 对应控制器方法: IndexController::delete()
     * 路由名称: voice.MyDelete
     */
    $router->post('voice/delete', [IndexController::class, 'delete'])
        ->name('voice.MyDelete');
    $router->post('voice/update', [IndexController::class, 'update'])
        ->name('voice.MyUpdate');

    //火山声音复刻
    $router->post('voice/volcengine/train', [VolcenginVoiceController::class, 'train'])
        ->name('voice.volcengine.train');
});

//哎呀呀 莫要难过嘛，你看嘛 生活头总有起起伏伏噻，遇到点不开心的事情太正常了，你就把那些烦心事都丢到脑壳后头嘛，像丢垃圾一样丢的远远的，我就在这陪着你呀，要是心里头憋得慌就一股脑倒给我嘛，我会一直听着的哟，
