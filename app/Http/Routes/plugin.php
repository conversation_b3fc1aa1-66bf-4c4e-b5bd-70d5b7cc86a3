<?php

use App\Http\Controllers\Api\Plugin\AiPPTController;
use App\Http\Controllers\Api\Plugin\AiPPTToolsController;
use App\Http\Controllers\Api\Plugin\JiMengController;
use App\Http\Controllers\Api\Plugin\JiMengToolController;
use App\Http\Controllers\Api\Plugin\KelingController;
use App\Http\Controllers\Api\Plugin\KelingImageController;
use App\Http\Controllers\Api\Plugin\MjController;
use App\Http\Controllers\Api\Plugin\MjDrawController;
use App\Http\Controllers\Api\Plugin\MjDrawEditController;
use App\Http\Controllers\Api\Plugin\MjDrawGoodsController;
use App\Http\Controllers\Api\Plugin\MjToolsController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'plugin/klingai',
], function (Router $router) {
    $router->post('/createKlMessageVideos', [KelingController::class, 'createKlMessageVideos'])
        ->name('plugin.createKlMessageVideos');//文生视频
    $router->post('/createKlLibraries', [KelingController::class, 'createKlLibraries'])
        ->name('plugin.createKlLibraries');//随机推荐词

    $router->post('/createKLVideos', [KelingController::class, 'create'])->name('plugin.createKLVideos');//首尾帧
    $router->post('/createKLMultVideos', [KelingController::class, 'createKLMultVideos'])
        ->name('plugin.createKLMultVideos');//多图参考
    $router->post('/createKLSpecial', [KelingController::class, 'createKLSpecial'])
        ->name('plugin.createKLSpecial');//创意视频
    $router->post('/createKLShape', [KelingController::class, 'createKLShape'])
        ->name('plugin.createKLShape');//对口型

    $router->post('/KlSpecialEffects', [KelingController::class, 'KlSpecialEffects'])
        ->name('plugin.KlSpecialEffects');//创意列表
    $router->post('/kelinUpload', [KelingController::class, 'upload'])->name('plugin.KLUpload');//可灵上传文件
    $router->post('/ttsList', [KelingController::class, 'ttslist'])->name('plugin.KLTTSList');//TTS列表
    $router->post('/ttsgen', [KelingController::class, 'tts'])->name('plugin.KLTTSGen');//tts生成
    $router->post('/lip_sync_recommend_videos', [KelingController::class, 'recommend_videos'])
        ->name('plugin.recommend_videos');//推荐视频
    $router->post('/recommendAudio', [KelingController::class, 'recommendAudio'])->name('plugin.recommendAudio');//推荐音频
    $router->post('/firstRecommend', [KelingController::class, 'firstRecommend'])
        ->name('plugin.KL.firstRecommend');//首尾帧推荐图
    $router->post('/multRecommend', [KelingController::class, 'multRecommend'])
        ->name('plugin.KL.multRecommend');//多图参考推荐图

    $router->post('/KlQuery', [KelingController::class, 'KlQuery'])->name('plugin.KlQuery');//任务轮询
    $router->post('/lists', [KelingController::class, 'lists'])->name('plugin.KL.lists');   //任务轮询

});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'plugin/klimage',
], function (Router $router) {
    $router->post('/options', [KelingImageController::class, 'options'])->name('plugin.KLI.options');//AI模特设置
    $router->post('/modelCreate', [KelingImageController::class, 'modelCreate'])
        ->name('plugin.KLI.modelCreate');//创建模特
    $router->post('/modelList', [KelingImageController::class, 'modelList'])
        ->name('plugin.KLI.modelList');//我的模特
    $router->post('/recoModel', [KelingImageController::class, 'recoModel'])
        ->name('plugin.KLI.recoModel');//官方模特
    $router->post('/recoClothes', [KelingImageController::class, 'recoClothes'])
        ->name('plugin.KLI.recoClothes');//推荐服装
    $router->post('/uploadModel', [KelingImageController::class, 'uploadModel'])
        ->name('plugin.KLI.uploadModel');
    $router->post('/uploadClothes', [KelingImageController::class, 'uploadClothes'])
        ->name('plugin.KLI.uploadClothes');
    $router->post('/aitryon', [KelingImageController::class, 'aitryon'])
        ->name('plugin.KLI.Aitryon');//试装
    $router->post('/lists', [KelingImageController::class, 'lists'])->name('plugin.KLI.lists');//任务轮询
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'plugin/mjimage',
], function (Router $router) {
    $router->post('/getTypes', [MjController::class, 'getTypes'])
        ->name('plugin.MJ.My.getTypes');//获取应用类目
    $router->post('/getTask', [MjController::class, 'getTask'])
        ->name('plugin.MJ.My.getTask');//获取应用类目

    $router->post('/changeFace', [MjDrawController::class, 'changeFace'])
        ->name('plugin.MJ.changeFace');//AI换脸
    $router->post('/expansion', [MjDrawController::class, 'expansion'])
        ->name('plugin.MJ.expansion');//AI扩图
    $router->post('/anime', [MjDrawController::class, 'anime'])
        ->name('plugin.MJ.anime');//AI转动漫
    $router->post('/lineDraft', [MjDrawController::class, 'lineDraft'])
        ->name('plugin.MJ.lineDraft');//AI线稿渲染
    $router->post('/artisticFont', [MjDrawController::class, 'artisticFont'])
        ->name('plugin.MJ.artisticFont');//AIc创意文字
    $router->post('/qrcode', [MjDrawController::class, 'qrcode'])
        ->name('plugin.MJ.qrcode');//AI二维码
    $router->post('/mjBlend', [MjDrawController::class, 'mjBlend'])
        ->name('plugin.MJ.mjBlend');//AI图片融合
    $router->post('/transfer', [MjDrawController::class, 'transfer'])
        ->name('plugin.MJ.transfer');//AI风格迁移
    $router->post('/mirror', [MjDrawController::class, 'mirror'])
        ->name('plugin.MJ.mirror');//AI写真

    $router->post('/aiProduct', [MjDrawGoodsController::class, 'aiProduct'])
        ->name('plugin.MJ.aiProduct');//AI商品图
    $router->post('/capture', [MjDrawGoodsController::class, 'capture'])
        ->name('plugin.MJ.capture');//AI抠图

    $router->post('/redraw', [MjDrawEditController::class, 'redraw'])
        ->name('plugin.MJ.redraw');//AI区域重绘
    $router->post('/magnify', [MjDrawEditController::class, 'magnify'])
        ->name('plugin.MJ.magnify');//AI无损放大
    $router->post('/enhance', [MjDrawEditController::class, 'enhance'])
        ->name('plugin.MJ.enhance');//AI画质增强
    $router->post('/definition', [MjDrawEditController::class, 'definition'])
        ->name('plugin.MJ.definition');//AI动漫细节修复
    $router->post('/repair', [MjDrawEditController::class, 'repair'])
        ->name('plugin.MJ.repair');//AI老照片修复
    $router->post('/colour', [MjDrawEditController::class, 'colour'])
        ->name('plugin.MJ.colour');//AI老照片上色
    $router->post('/imgToLine', [MjDrawEditController::class, 'imgToLine'])
        ->name('plugin.MJ.imgToLine');//AI图片转线稿
    $router->post('/sketch', [MjDrawEditController::class, 'sketch'])
        ->name('plugin.MJ.sketch');//AI图片转素描
    $router->post('/imgAnalysis', [MjDrawEditController::class, 'imgAnalysis'])
        ->name('plugin.MJ.imgAnalysis');//AI图片解析
    $router->post('/Jigsaw', [MjDrawEditController::class, 'Jigsaw'])
        ->name('plugin.MJ.Jigsaw');//AI图片解析

    $router->post('/comfyDrawStyle', [MjToolsController::class, 'comfyDrawStyle'])
        ->name('plugin.MJ.comfyDrawStyle');//AI附录
    $router->post('/qrcodeStyle', [MjToolsController::class, 'qrcodeStyle'])
        ->name('plugin.MJ.qrcodeStyle');//AI附录
    $router->post('/createQrCode', [MjToolsController::class, 'createQrCode'])
        ->name('plugin.MJ.createQrCode');//AI附录
    $router->post('/styleTransfer', [MjToolsController::class, 'styleTransfer'])
        ->name('plugin.MJ.styleTransfer');//V3风格
    $router->post('/mirrorStyle', [MjToolsController::class, 'mirrorStyle'])
        ->name('plugin.MJ.mirrorStyle');//写真风格
    $router->post('/captureStyle', [MjToolsController::class, 'captureStyle'])
        ->name('plugin.MJ.captureStyle');//写真风格

    $router->post('/query', [MjDrawController::class, 'query'])
        ->name('plugin.MJ.query');//结果轮询
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'plugin/jimeng',
], function (Router $router) {
    $router->post('/genTextVideo', [JiMengController::class, 'genTextVideo'])->name('plugin.JM.genTextVideo');//文本转视频
    $router->post('/genImageVideo', [JiMengController::class, 'genImageVideo'])->name('plugin.JM.genImageVideo');//文本转视频
    $router->post('/syncLip', [JiMengController::class, 'syncLip'])->name('plugin.JM.syncLip');//对口型
    $router->post('/genImage', [JiMengController::class, 'genImage'])->name('plugin.JM.genImage');//对口型

    $router->post('/getModel', [JiMengToolController::class, 'getModel'])
        ->name('plugin.JM.getModel');//获取模型列表
    $router->post('/getPanelInfo', [JiMengToolController::class, 'getPanelInfo'])
        ->name('plugin.JM.getPanelInfo');//音色分类
    $router->post('/getFeed', [JiMengToolController::class, 'getFeed'])
        ->name('plugin.JM.getFeed');//获取音色
    $router->post('/genTTS', [JiMengToolController::class, 'genTTS'])
        ->name('plugin.JM.genTTS');//试听音色
    $router->post('/checkResource', [JiMengToolController::class, 'checkResource'])
        ->name('plugin.JM.checkResource');//检测资源
    $router->post('/checkQuery', [JiMengToolController::class, 'checkQuery'])
        ->name('plugin.JM.checkQuery');//检测资源
    $router->post('/imageSize', [JiMengToolController::class, 'imageSize'])
        ->name('plugin.JM.imageSize');//检测资源
    $router->post('/taskList', [JiMengController::class, 'taskList'])
        ->name('plugin.JM.taskList');//即梦列表
    $router->post('/Square', [JiMengController::class, 'Square'])
        ->name('plugin.JM.Square');//即梦列表
    $router->post('/query', [JiMengController::class, 'query'])->name('plugin.JM.query');//文本转视频
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
    'prefix'     => 'plugin/aippt',
], function (Router $router) {
    $router->post('/ShareDetail', [AiPPTController::class, 'ShareDetail'])
        ->name('plugin.AiPPT.ShareDetail');//分享详情
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'plugin/aippt',
], function (Router $router) {
    $router->post('/Outline', [AiPPTController::class, 'Outline'])
        ->name('plugin.AiPPT.Outline');//生成大纲
    $router->post('/genContent', [AiPPTController::class, 'genContent'])
        ->name('plugin.AiPPT.GenContent');//生成PPT

    $router->post('/Lists', [AiPPTController::class, 'lists'])
        ->name('plugin.AiPPT.Lists');//记录

    $router->post('/deletes', [AiPPTController::class, 'deletes'])
        ->name('plugin.AiPPT.Delete');//删除

    $router->post('/share', [AiPPTController::class, 'share'])
        ->name('plugin.AiPPT.Share');//删除
    
    $router->post('/detail', [AiPPTController::class, 'detail'])
        ->name('plugin.AiPPT.Detail');//详情

    $router->post('/editOutLine', [AiPPTController::class, 'editOutLine'])
        ->name('plugin.AiPPT.editOutLine');//修改大纲
    $router->post('/editContent', [AiPPTController::class, 'editContent'])
        ->name('plugin.AiPPT.editContent');//修改内容

    $router->post('/tools/audience', [AiPPTToolsController::class, 'audience'])
        ->name('plugin.AiPPT.audience');//受众
    $router->post('/tools/scene', [AiPPTToolsController::class, 'scene'])
        ->name('plugin.AiPPT.scene');//场景
    $router->post('/tools/tone', [AiPPTToolsController::class, 'tone'])
        ->name('plugin.AiPPT.tone');//语气
    $router->post('/tools/language', [AiPPTToolsController::class, 'language'])
        ->name('plugin.AiPPT.language');//语言
    $router->post('/tools/templates', [AiPPTToolsController::class, 'templates'])
        ->name('plugin.AiPPT.templates');//模板

});

