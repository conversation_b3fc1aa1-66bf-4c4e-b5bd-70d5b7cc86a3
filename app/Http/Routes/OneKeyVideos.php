<?php

use App\Http\Controllers\Api\OneKeyVideos\IndexController;
use App\Http\Controllers\Api\OneKeyVideos\VideoController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('images/create', [IndexController::class, 'create'])->name('create.images');
    $router->post('images/foreach', [IndexController::class, 'lunxun'])->name('create.foreach');
    $router->post('videos/videocreate', [VideoController::class, 'create'])->name('create.video');
    $router->post('videos/foreach', [VideoController::class, 'lunxun'])->name('videos.foreach');
    $router->post('videos/download', [VideoController::class, 'download'])->name('videos.download');
});
