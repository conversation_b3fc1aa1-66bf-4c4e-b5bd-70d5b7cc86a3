<?php

use App\Http\Controllers\Api\Report\IndexController as ReportController;
use App\Http\Controllers\App\ChatController;
use App\Http\Controllers\App\IndexController;
use App\Http\Controllers\App\Publish\ImagineController;
use App\Http\Controllers\App\Publish\PublishController;
use App\Http\Controllers\App\Publish\ScrollController;
use App\Http\Controllers\App\User\AccountController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('app/chat/index', [ChatController::class, 'index'])
        ->name('app.chatLogs');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('app/account/ScoreLog', [AccountController::class, 'ScoreLog'])
        ->name('user.Account.ScoreLog');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'report'
], function (Router $router) {
    $router->post('app/report/store', [ReportController::class, 'store'])
        ->name('app.Report.Store');
    $router->post('app/report/lists', [ReportController::class, 'lists'])
        ->name('app.Report.Lists');
    $router->post('app/report/detail', [ReportController::class, 'detail'])
        ->name('app.Report.Detail');
    $router->post('app/report/delete', [ReportController::class, 'delete'])
        ->name('app.Report.Delete');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
], function (Router $router) {
    $router->post('app/index', [IndexController::class, 'index'])
        ->name('app.index');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
    'prefix'     => 'publish/scroll'
], function (Router $router) {
    $router->post('send', [ScrollController::class, 'send'])
        ->middleware(['api', 'wateAuth'])
        ->name('app.Publish.Scroll.send');
    $router->post('lists', [ScrollController::class, 'lists'])
        ->name('app.Publish.Scroll.lists');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'publish'
], function (Router $router) {
    //我的收藏
    $router->post('/MyFavorite', [PublishController::class, 'MyFavorite'])
        ->name('app.Publish.MyFavorite');
    $router->post('/MyLike', [PublishController::class, 'MyLike'])
        ->name('app.Publish.MyLike');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'publish'
], function (Router $router) {
    //发现页面
    $router->post('/index', [PublishController::class, 'index'])
        ->name('app.Publish.Index');
    //我的作品
    $router->post('/MyWorkInit', [PublishController::class, 'MyWorkInit'])
        ->name('app.Publish.MyWorkInit');
    $router->post('/MyWork', [PublishController::class, 'MyWork'])
        ->name('app.Publish.MyWork');
    $router->post('/WorkPublish', [PublishController::class, 'WorkPublish'])
        ->name('app.Publish.WorkPublish');
    $router->post('/WorkPublishDown', [PublishController::class, 'WorkPublishDown'])
        ->name('app.Publish.WorkPublishDown');
    //我的收藏
    $router->post('/MyFavorite', [PublishController::class, 'MyFavorite'])
        ->name('app.Publish.MyFavorite');
    $router->post('/MyLike', [PublishController::class, 'MyLike'])
        ->name('app.Publish.MyLike');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'imagine'
], function (Router $router) {
    $router->post('/index', [ImagineController::class, 'index'])
        ->name('app.Imagine.Index');
    $router->post('/publish', [ImagineController::class, 'publish'])
        ->name('app.Imagine.Publish');
    $router->post('/PublishDown', [ImagineController::class, 'PublishDown'])
        ->name('app.Imagine.PublishDown');
    $router->post('/Delete', [ImagineController::class, 'Delete'])
        ->name('app.Imagine.Delete');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
    'prefix'     => 'publish'
], function (Router $router) {
    $router->post('/category', [PublishController::class, 'category'])
        ->name('app.Publish.Category');
    $router->post('/tags', [PublishController::class, 'tags'])
        ->name('app.Publish.tags');
});
