<?php

use App\Http\Controllers\Api\AiSearch\ImageController;
use App\Http\Controllers\Api\AiSearch\IndexController;
use App\Http\Controllers\Api\AiSearch\IndexV2Controller;
use App\Http\Controllers\Api\AiSearch\NewSearchController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
    'prefix'     => 'aisearch',
], function (Router $router) {
    $router->post('gethot', [IndexController::class, 'getHot'])->name('aisearch.gethot');
    $router->post('getAppHot', [IndexController::class, 'appSearch'])->name('aisearch.getAppHot');
    $router->post('index', [IndexController::class, 'index'])->name('Aisearch.index');
    $router->post('AiSearchImage', [ImageController::class, 'AiSearchImage'])->name('tool.aisearch_image');
    $router->post('new/index', [NewSearchController::class, 'index'])->name('Aisearch.New.index');
    $router->post('getAppHotV2', [IndexV2Controller::class, 'getHot'])->name('aisearch.V2getAppHot');
});
Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('aisearch/getLog', [IndexController::class, 'getLog'])->name('aisearch.getLog');
});



