<?php

use App\Http\Controllers\Api\Note\AuthorController;
use App\Http\Controllers\Api\Note\FileController;
use App\Http\Controllers\Api\Note\IndexController;
use App\Http\Controllers\Api\Note\KnowledgeController;
use App\Http\Controllers\Api\Note\TagController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('notes', [IndexController::class, 'index'])->name('note.index');
    $router->post('notes/store', [IndexController::class, 'store'])->name('note.create');
    $router->post('notes/update', [IndexController::class, 'update'])->name('note.update');
    $router->post('notes/delete', [IndexController::class, 'delete'])->name('note.detele');
    $router->post('notes/show', [IndexController::class, 'show'])->name('note.show');
    $router->post('notes/archive', [IndexController::class, 'archive'])->name('note.archive');
    $router->post('notes/top', [IndexController::class, 'setTop'])->name('note.top');
    $router->post('notes/list', [IndexController::class, 'list'])->name('note.list');
    $router->post('notes/update_title', [IndexController::class, 'updateTitle'])->name('note.update_title');
    // 标签
    $router->post('notes/tag', [IndexController::class, 'setTag'])->name('note.set_tag');
    $router->post('notes/tags', [TagController::class, 'index'])->name('note.tag.index');
    $router->post('notes/tag/add', [TagController::class, 'store'])->name('note.tag.add');
    $router->post('notes/tag/update', [TagController::class, 'update'])->name('note.tag.update');
    $router->post('notes/tag/delete', [TagController::class, 'destroy'])->name('note.tag.delete');

    // 知识库
    $router->post('notes/knowledge', [KnowledgeController::class, 'moveToKnowledge'])->name('note.move_knowledge');
    $router->post('notes/remove_knowledge', [KnowledgeController::class, 'removeToKnowledge'])
        ->name('note.remove_knowledge');
    $router->post('notes/knowledge/update', [KnowledgeController::class, 'update'])->name('note.knowledge_update');
    $router->post('notes/knowledge/create', [KnowledgeController::class, 'store'])->name('note.knowledge_create');
    $router->post('notes/knowledge_notes', [KnowledgeController::class, 'index'])->name('note.knowledge_notes');

    //草稿
    $router->post('notes/draft/save', [IndexController::class, 'cacheSave'])->name('note.draft.save');
    $router->post('notes/draft/get', [IndexController::class, 'cacheGet'])->name('note.draft.get');

    //附件管理
    $router->post('notes/files', [FileController::class, 'index'])->name('note.file.index');
    $router->post('notes/file/store', [FileController::class, 'store'])->name('note.file.create');
    $router->post('notes/file/update', [FileController::class, 'update'])->name('note.file.update');
    $router->post('notes/file/delete', [FileController::class, 'delete'])->name('note.file.delete');
    $router->post('notes/file/show', [FileController::class, 'show'])->name('note.file.show');
    $router->post('notes/file/move_knowledge', [FileController::class, 'moveToKnowledge'])
        ->name('note.file.move_knowledge');
    $router->post('notes/file/remove_knowledge', [FileController::class, 'removeToKnowledge'])
        ->name('note.file.remove_knowledge');

    //我浏览过和收藏过的笔记的作者
    $router->post('notes/authors', [AuthorController::class, 'index'])->name('note.authors');
});


