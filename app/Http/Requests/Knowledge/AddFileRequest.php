<?php

namespace App\Http\Requests\Knowledge;

use Illuminate\Foundation\Http\FormRequest;

class AddFileRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'datasetId' => 'required',
            'file'      => 'required',
        ];
    }

    public function messages()
    {
        return [
            'datasetId.required' => '知识库ID不能为空',
            'file.required'      => '文件地址不能为空',
        ];
    }

}