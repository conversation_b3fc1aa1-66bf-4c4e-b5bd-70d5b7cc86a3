<?php

namespace App\Http\Requests\Knowledge;

use App\Exceptions\ValidatorException;
use App\Models\CompanyStaff;
use App\Models\CompanyUser;
use App\Models\Knowledge;
use Illuminate\Foundation\Http\FormRequest;

class AssistantRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'image'    => ['required', 'url'], // 假设image是图片的URL
            'nickname' => ['required', 'max:255'], // 昵称是必填的，且最大长度为255
            //            'personality' => ['required', 'max:255'], // 性格描述最大长度为255（可选）
            //            'identity'    => ['required', 'max:255'], // 身份标识最大长度为255（可选）
            'prologue' => ['required', 'max:500'], // 序言或简介最大长度为500（可选）
            //            'datasetId' => 'required'
        ];
    }

    public function messages()
    {
        return [
            'image.required'       => '图片URL不能为空',
            'image.url'            => '图片URL格式错误',
            'nickname.required'    => '昵称不能为空',
            'nickname.max'         => '昵称长度不能超过255个字符',
            'personality.max'      => '性格描述长度不能超过255个字符',
            'personality.required' => '性格描述长度不能不能为空',
            'identity.max'         => '身份标识长度不能超过255个字符',
            'identity.required'    => '身份标识长度不能不能为空',
            'prologue.max'         => '序言或简介长度不能超过500个字符',
            'prologue.required'    => '序言或简介长度不能为空',
            //'datasetId.required'   => '知识库ID不能为空',
        ];
    }
}