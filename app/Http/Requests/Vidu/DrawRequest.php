<?php

namespace App\Http\Requests\Vidu;

use App\Models\PluginViduTemplate;
use Illuminate\Foundation\Http\FormRequest;

class DrawRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'template'     => 'required|string',
            'images'       => 'required|array|min:1',
            'images.*'     => ['required', 'string', 'url'],
            'prompt'       => 'required|string|max:1500',
            'seed'         => 'nullable|integer',
            'aspect_ratio' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    $template = PluginViduTemplate::query()->where('scene', request()->template)->first();
                    if (! $template) {
                        $fail('模板不存在');
                    }
                    $aspectRatios = $template->getAspectRatios();
                    if (! in_array($value, $aspectRatios)) {
                        $fail('比例参数值无效，可选值: '.implode(',', $aspectRatios));
                    }
                },
            ],
            'extra'        => [
                'nullable',
                'required_if:template,beast_companion,exotic_princess',
                'string',
                'max:50',
                function ($attribute, $value, $fail) {
                    if (! request()->template) {
                        return;
                    }

                    switch (request()->template) {
                        case 'beast_companion':
                            if (! in_array($value, ['auto', 'bear', 'tiger', 'elk', 'snake', 'lion', 'wolf'])) {
                                $fail('extra参数值无效，可选值: auto, bear, tiger, elk, snake, lion, wolf');
                            }
                            break;

                        case 'exotic_princess':
                            if (! in_array($value, [
                                'auto', 'denmark', 'uk', 'africa', 'china', 'mexico', 'switzerland', 'russia', 'italy',
                                'korea', 'thailand', 'india', 'japan'
                            ])) {
                                $fail('extra参数值无效，可选值: auto, denmark, uk, africa, china, mexico, switzerland, russia, italy, korea, thailand, india, japan');
                            }
                            break;
                    }
                }
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'template.required'   => '缺少模版参数',
            'template.string'     => '模版参数必须是字符串',
            'images.required'     => '缺少图片',
            'images.array'        => '图片必须是数组',
            'images.min'          => '至少需要一张图片',
            'prompt.required'     => '缺少文本提示词',
            'prompt.max'          => '文本提示词不能超过1500个字符',
            'seed.integer'        => '随机种子必须是整数',
            'aspect_ratio.string' => '比例参数必须是字符串',
            'area.string'         => '异域公主特效专属参数必须是字符串',
            'beast.string'        => '与兽同行特效专属参数必须是字符串',
            'extra.required'      => '缺少extra参数',
            'extra.string'        => 'extra参数必须是字符串',
            'extra.max'           => 'extra参数不能超过50个字符',
        ];
    }

}
