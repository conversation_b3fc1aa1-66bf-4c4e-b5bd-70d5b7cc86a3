<?php

namespace App\Http\Requests\Bailian\Article;

use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class CreateArticleRequest extends BaseFormRequest
{
    /**
     * 获取验证规则
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'title'     => ['nullable', 'string', 'max:255'],
            'details'   => [
                'required', 'string', function ($attribute, $value, $fail) {
                    // 对于MEETING类型，检查content['data']是否为空
                    if ($this->input('type') == BailianArticleTypeEnum::MEETING->value) {
                        $content      = json_decode($value, true);
                        $meeting_info = $content['meeting_info'] ?? [];
                        if (empty($meeting_info)) {
                            $fail('请输入会议信息');
                        }
                        return;
                    }

                    // 其他类型需要检查content['data']不能为空
                    $content     = json_decode($value, true);
                    $contentText = $content['data'] ?? '';
                    if (empty($contentText)) {
                        $fail('请输入笔记内容');
                    }
                }
            ],
            'type'      => ['required', Rule::enum(BailianArticleTypeEnum::class)],
            'parent_id' => ['nullable', 'exists:bailian_articles,id'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'title.required'   => '标题不能为空',
            'title.max'        => '标题最多255个字符',
            'details.required' => '请输入笔记内容',
            'type.required'    => '类型不能为空',
            'type.enum'        => '类型无效',
            'parent_id.exists' => '上级笔记不存在',
        ];
    }
}
