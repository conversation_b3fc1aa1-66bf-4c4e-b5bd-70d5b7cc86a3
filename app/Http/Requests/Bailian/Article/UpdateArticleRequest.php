<?php

namespace App\Http\Requests\Bailian\Article;

use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class UpdateArticleRequest extends BaseFormRequest
{
    /**
     * 获取验证规则
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'id'        => ['required', 'integer', 'exists:bailian_articles,id'],
            'title'     => ['nullable', 'string', 'max:255'],
            'details'   => ['required', 'string'],
            'type'      => ['required', Rule::enum(BailianArticleTypeEnum::class)],
            'parent_id' => ['nullable', 'exists:bailian_articles,id'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'title.required'   => '标题不能为空',
            'title.max'        => '标题最多255个字符',
            'details.required' => '内容不能为空',
            'type.required'    => '类型不能为空',
            'type.enum'        => '类型无效',
            'parent_id.exists' => '上级笔记不存在',
            'id.required'      => '文章id必须是数字',
            'id.exists'        => '文章不存在',
        ];
    }
}
