<?php

namespace App\Http\Requests\Bailian\Article;

use App\Http\Requests\BaseFormRequest;

class SetTagRequest extends BaseFormRequest
{
    /**
     * 获取验证规则
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'article_id' => ['required', 'exists:bailian_articles,id'],
            'tag_ids'    => ['required', 'array'],
            'tag_ids.*'  => ['required', 'exists:bailian_tags,id'],
        ];
    }

    /**
     * 获取验证错误消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'article_id.required' => '笔记ID不能为空',
            'article_id.exists'   => '笔记不存在',
            'tag_ids.required'    => '标签不能为空',
            'tag_ids.array'       => '标签必须是数组',
            'tag_ids.*.exists'    => '标签不存在',
        ];
    }
} 