<?php

namespace App\Http\Requests\Bailian\File;

use Illuminate\Foundation\Http\FormRequest;

class AddToKnowledgeRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'knowledge_id' => 'required|integer|exists:bailian_knowledge,id',
            'file_id'      => 'required|integer|exists:bailian_files,id',
        ];
    }

    public function messages(): array
    {
        return [
            'knowledge_id.required' => '知识库id不能为空',
            'knowledge_id.exists'   => '知识库不存在',
            'file_id.required'      => '文档id不能为空',
            'file_id.exists'        => '文档不存在',
        ];
    }

}
