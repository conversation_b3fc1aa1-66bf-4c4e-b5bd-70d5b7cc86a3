<?php

namespace App\Http\Requests\Bailian\File;

use Illuminate\Foundation\Http\FormRequest;

class UpdateFileRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'hash'    => ['required'],
            'file_id' => [
                'required',
                'integer',
                'exists:bailian_files,id'
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'hash.required'    => '文件hash',
            'file_id.required' => '缺少文档id',
            'file_id.exists'   => '文档不存在',
            'file_id.integer'  => '文档id必须为整数',
        ];
    }

}
