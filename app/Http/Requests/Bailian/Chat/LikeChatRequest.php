<?php

namespace App\Http\Requests\Bailian\Chat;

use Illuminate\Foundation\Http\FormRequest;

class LikeChatRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'bailian_chat_id' => 'required|integer|exists:bailian_knowledge_chats,id',
            'is_liked'        => 'required|integer|in:0,1'
        ];
    }

    public function messages(): array
    {
        return [
            'message.required'               => '请输入消息内容',
            'message.string'                 => '消息内容必须是字符串',
            'message.max'                    => '消息内容不能超过255个字符',
            'bailian_knowledge_ids.required' => '请选择知识库',
            'bailian_knowledge_ids.string'   => '知识库必须是字符串',
            'session_file_ids.string'        => '请选择文件',
        ];
    }

}
