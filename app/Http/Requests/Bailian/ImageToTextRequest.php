<?php

namespace App\Http\Requests\Bailian;

use Illuminate\Foundation\Http\FormRequest;

class ImageToTextRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'path_url' => 'required|string|url',
        ];
    }

    public function messages(): array
    {
        return [
            'path_url.required' => '图片地址不能为空',
            'path_url.string'   => '图片地址必须为字符串',
            'path_url.url'      => '图片地址必须为url地址',
        ];
    }

}
