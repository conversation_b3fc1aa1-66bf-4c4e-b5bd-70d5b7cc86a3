<?php

namespace App\Http\Requests\Bailian\Member;

use App\Http\Requests\BaseFormRequest;

class DeleteMemberRequest extends BaseFormRequest
{
    /**
     * 验证规则
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'member_id' => 'required|integer|exists:bailian_knowledge_members,id'
        ];
    }

    /**
     * 错误消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'member_id.required' => '成员id不能为空',
            'member_id.integer'  => '成员id必须是数字',
            'member_id.exists'   => '成员不存在',
        ];
    }
}
