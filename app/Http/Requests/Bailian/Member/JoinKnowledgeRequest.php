<?php

namespace App\Http\Requests\Bailian\Member;

use App\Http\Requests\BaseFormRequest;

class JoinKnowledgeRequest extends BaseFormRequest
{
    /**
     * 验证规则
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'knowledge_id' => 'required|integer|exists:bailian_knowledge,id'
        ];
    }

    /**
     * 错误消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'knowledge_id.required' => '知识库ID不能为空',
            'knowledge_id.integer'  => '知识库ID必须为整数',
            'knowledge_id.exists'   => '知识库不存在',

        ];
    }
}
