<?php

namespace App\Http\Requests\FastGpt\Knowledge;

use App\Models\Enums\FastgptKnowledgeLevelEnum;
use Illuminate\Foundation\Http\FormRequest;

class AddKnowledgeRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'company_id' => ['nullable', 'numeric', 'exists:App\Models\Company,id'],
            //            'department_id' => [
            //                'nullable',
            //                'numeric',
            //                'exists:Modules\User\Models\Department,id',
            //                'required_if:level,'.FastgptKnowledgeLevelEnum::DEPARTMENT->value.','.FastgptKnowledgeLevelEnum::USER->value,
            //            ],
            'name'       => ['required'],
            'level'      => ['required', 'in:'.FastgptKnowledgeLevelEnum::valuesToString()],
        ];
    }

    public function messages(): array
    {
        return [
            'company_id.required' => '缺少企业id',
            'company_id.numeric'  => '企业id只能是数字',
            'company_id.exists'   => '企业id不存在',
            'level.required'      => '缺少权限',
            'level.in'            => '权限错误',
            'name.required'       => '知识库名称必填',
        ];
    }

}
