<?php

namespace App\Http\Requests\FastGpt\App;

use App\Models\Enums\FastgptAppLevelEnum;
use Illuminate\Foundation\Http\FormRequest;

class AddAppRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'level'       => ['required', 'in:'.FastgptAppLevelEnum::valuesToString()],
            'name'        => 'required',
            'dataset_ids' => 'required',
            //            'company_id'    => 'required',
            //            'department_id' => [
            //                'nullable',
            //                'numeric',
            //                'exists:Modules\User\Models\Department,id',
            //                'required_if:level,'.FastgptAppLevelEnum::DEPARTMENT->value.','.FastgptAppLevelEnum::USER->value,
            //            ],
        ];
    }

    public function messages(): array
    {
        return [
            'department_id.numeric' => '部门id只能是数字',
            'department_id.exists'  => '部门id不存在',
            'level.required'        => '缺少应用权限',
            'level.in'              => '权限错误',
            'dataset_ids.required'  => '缺少知识库',
            'name.required'         => '智能体名称必填',
        ];
    }

}
