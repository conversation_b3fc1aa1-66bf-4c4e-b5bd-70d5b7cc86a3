<?php

namespace App\Http\Requests\FastGpt\App;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAppRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'app_id'      => 'required',
            'name'        => 'required',
            'dataset_ids' => 'required',
            //            'permission' => 'required',
            //            'level'      => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'app_id.required'      => '缺少应用id',
            'name.required'        => '缺少名称',
            'dataset_ids.required' => '缺少知识库',
            'permission.required'  => '缺少权限',
            'level.required'       => '缺少level',
        ];
    }

}
