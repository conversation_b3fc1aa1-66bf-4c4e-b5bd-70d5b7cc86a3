<?php

namespace App\Http\Requests\FastGpt\Article;

use Illuminate\Foundation\Http\FormRequest;

class AddKnowledgeByNoteRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'knowledge_id' => 'required|integer|exists:fastgpt_knowledge,id',
            'note_id'      => 'required|integer|exists:notes,id',
            'name'         => 'required',
            'details'      => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'knowledge_id.required' => '缺少知识库id',
            'knowledge_id.integer'  => '知识库id必须是数字',
            'knowledge_id.exists'   => '知识库不存在',
            'note_id.required'      => '缺少小记id',
            'note_id.integer'       => '小记id必须是数字',
            'note_id.exists'        => '小记不存在',
            'name.required'         => '缺少标题',
            'details.required'      => '缺少内容',
        ];
    }

}
