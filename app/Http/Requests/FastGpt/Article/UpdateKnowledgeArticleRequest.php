<?php

namespace App\Http\Requests\FastGpt\Article;

use Illuminate\Foundation\Http\FormRequest;

class UpdateKnowledgeArticleRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'fastgpt_article_id' => 'required|integer|exists:fastgpt_knowledge_articles,id',
            'name'               => 'required',
            'details'            => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'fastgpt_article_id.required' => '缺少文档id',
            'fastgpt_article_id.integer'  => '文档id必须是数字',
            'fastgpt_article_id.exists'   => '文档不存在',
            'name.required'               => '缺少标题',
            'details.required'            => '缺少内容',
        ];
    }

}
