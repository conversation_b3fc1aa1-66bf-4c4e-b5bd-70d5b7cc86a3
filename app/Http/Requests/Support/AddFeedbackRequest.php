<?php

namespace App\Http\Requests\Support;

use App\Http\Requests\BaseFormRequest;

class AddFeedbackRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'content'  => 'required',
            'pictures' => 'nullable|string',
        ];
    }

    public function messages(): array
    {
        return [
            'details.required' => '缺少内容',
            'picture.required' => '缺少图片',
            'pictures.string'  => '图片必须是字符串',
        ];
    }
}