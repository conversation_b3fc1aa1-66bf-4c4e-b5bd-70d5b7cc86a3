<?php

namespace App\Http\Requests\Note;

use Illuminate\Foundation\Http\FormRequest;

class NoteRemoveKnowledgeRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'knowledge_set_id' => 'required|integer|exists:fastgpt_knowledge_sets,id',
        ];
    }

    public function messages(): array
    {
        return [
            'knowledge_set_id.required' => '缺少知识库id',
            'knowledge_set_id.integer'  => '知识库id必须是数字',
            'knowledge_set_id.exists'   => '知识库不存在',
        ];
    }

}
