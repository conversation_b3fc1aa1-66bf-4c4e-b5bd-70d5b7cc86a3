<?php

namespace App\Http\Requests\Note;

use Illuminate\Foundation\Http\FormRequest;

class UpdateNoteRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'details' => ['required'],
            'note_id' => 'required|integer|exists:notes,id',
            'tag_ids' => 'nullable|string',
            'type'    => 'required|integer',
        ];
    }

    public function messages(): array
    {
        return [
            'note_id.required' => '缺少id',
            'details.required' => '缺少内容',
            'tag_ids.required' => '缺少标签',
            'tag_ids.string'   => '标签只能是字符串',
            'type.required'    => '缺少类型',
        ];
    }

}
