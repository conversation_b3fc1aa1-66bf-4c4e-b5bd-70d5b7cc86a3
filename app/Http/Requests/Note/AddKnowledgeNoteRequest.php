<?php

namespace App\Http\Requests\Note;

use Illuminate\Foundation\Http\FormRequest;

class AddKnowledgeNoteRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'knowledge_id' => 'nullable|integer|exists:fastgpt_knowledge,id',
            'name'         => 'required',
            'details'      => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'knowledge_id.required' => '缺少知识库id',
            'knowledge_id.integer'  => '知识库id必须是数字',
            'knowledge_id.exists'   => '知识库不存在',
            'name.required'         => '缺少标题',
            'details.required'      => '缺少内容',
        ];
    }

}
