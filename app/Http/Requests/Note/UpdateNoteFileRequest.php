<?php

namespace App\Http\Requests\Note;

use Illuminate\Foundation\Http\FormRequest;

class UpdateNoteFileRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'hash'         => ['required'],
            'note_file_id' => [
                'required',
                'integer',
                'exists:note_files,id'
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'hash.required'         => '文件hash',
            'note_file_id.required' => '缺少附件id',
            'note_file_id.exists'   => '附件不存在',
            'note_file_id.integer'  => '附件id必须为整数',
        ];
    }

}
