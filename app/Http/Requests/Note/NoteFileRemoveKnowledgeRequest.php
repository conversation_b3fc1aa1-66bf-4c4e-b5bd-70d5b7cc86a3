<?php

namespace App\Http\Requests\Note;

use Illuminate\Foundation\Http\FormRequest;

class NoteFileRemoveKnowledgeRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'knowledge_set_id' => 'required|integer|exists:fastgpt_knowledge_sets,id',
        ];
    }

    public function messages(): array
    {
        return [
            'knowledge_set_id.required' => '缺少知识库集合id',
            'knowledge_set_id.integer'  => '知识库集合id必须是数字',
            'knowledge_set_id.exists'   => '知识库集合不存在',
        ];
    }

}
