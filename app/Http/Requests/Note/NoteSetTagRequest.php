<?php

namespace App\Http\Requests\Note;

use Illuminate\Foundation\Http\FormRequest;

class NoteSetTagRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'note_id' => 'required|integer|exists:notes,id',
            'tag_ids' => 'nullable|string',
        ];
    }

    public function messages(): array
    {
        return [
            'note_id.required' => '缺少id',
            'tag_ids.required' => '缺少标签',
            'tag_ids.string'   => '标签只能是字符串',
        ];
    }

}
