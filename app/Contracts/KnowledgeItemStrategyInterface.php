<?php

namespace App\Contracts;

use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeItem;

/**
 * 知识库项目策略接口
 */
interface KnowledgeItemStrategyInterface
{
    /**
     * 添加项目到知识库
     *
     * @param  mixed  $item  项目实例
     * @param  BailianKnowledge  $knowledge  知识库
     * @param  string  $categoryId  分类ID
     * @param  array  $options  选项
     * @return void
     */
    public function addToKnowledge(
        $item,
        BailianKnowledge $knowledge,
        string $categoryId,
        array $options
    ): ?BailianKnowledgeItem;

    /**
     * 从知识库移除项目
     *
     * @param  mixed  $item  项目实例
     * @return void
     */
    public function removeFromKnowledge($item): void;

    /**
     * 同步项目到阿里云知识库
     *
     * @param  mixed  $item  项目实例
     * @param  BailianKnowledge  $knowledge  知识库
     * @param  string  $categoryId  分类ID
     * @return void
     */
    public function syncToAlibaba($item, BailianKnowledge $knowledge, string $categoryId): void;

    /**
     * 根据ID从知识库中移除项目
     *
     * @param  int  $itemId  项目ID
     * @param  int|null  $storageId  存储ID（可选）
     * @param  bool  $deleteSelf  是否删除项目本身
     * @return bool
     */
    public function removeFromKnowledgeById(int $itemId, ?int $storageId = null, bool $deleteSelf = false): bool;

    /**
     * 获取支持的模型类名
     *
     * @return string
     */
    public function getSupportedModelClass(): string;
}
