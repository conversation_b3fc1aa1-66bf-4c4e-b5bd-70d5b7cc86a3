<?php

use App\Exceptions\ValidatorException;
use App\Facades\Sigma;
use App\Models\SystemConfig;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\IOFactory;

if (! function_exists('wateGetSystemPrompt')) {
    /**
     * Notes   : 获取系统提示语
     *
     * @Date   : 2023/7/19 15:12
     * @param  string  $fileName
     * @return string
     */
    function wateGetSystemPrompt(string $fileName): string
    {
        return file_get_contents(app_path(sprintf('Prompts/%sPrompt.md', $fileName)));
    }
}
if (! function_exists('hideMobilePhoneNo')) {
    /**
     * Notes   : 隐藏字符串中间的N位
     *
     * @Date   : 2023/7/19 15:12
     * <AUTHOR> <Jason.C>
     * @param  string  $mobile  手机号
     * @param  int  $len  隐藏位数
     * @param  string  $char  填充符号
     * @return string
     */
    function hideMobilePhoneNo(
        string $mobile,
        int $len = 4,
        string $char = '*'
    ): string {
        if ($len >= strlen($mobile)) {
            $len = ceil(strlen($mobile) / 2);
        }
        $fill = str_repeat($char, $len);

        $residue = strlen($mobile) - $len;

        return substr($mobile, 0, intval($residue / 2)).$fill.substr(
                $mobile,
                -ceil($residue / 2)
            );
    }
}

if (! function_exists('getOssPrivateUrl')) {
    function getOssPrivateUrl(string $url): string
    {
        if (empty($url)) {
            return '';
        } elseif (Str::startsWith($url, 'http')) {
            if (Str::contains($url, 'watestar.oss-cn-hangzhou.aliyuncs.com')) {
                $url = Str::between($url, 'aliyuncs.com', '?');
                return Storage::url($url);
            }
            return $url;
        } else {
            return Storage::url($url);
        }
    }
}

if (! function_exists('array2tree')) {
    /**
     * 数组转换为树型结构，用于无限极分类
     *
     * @param  array  $list
     * @param  int  $parentId
     * @param  string  $parentNodeName
     * @param  string  $primaryKey
     * @param  string  $childNodeName
     * @return array
     */
    function array2tree(
        array $list,
        int $parentId = 0,
        string $primaryKey = 'id',
        string $parentNodeName = 'parent_id',
        string $childNodeName = 'children'
    ): array {
        $resultArr = [];
        if (empty($list)) {
            return [];
        }
        foreach ($list as $key => $item) {
            if ($item[$parentNodeName] == $parentId) {
                unset($list[$key]);
                $item[$childNodeName] = array2tree(
                    $list,
                    $item[$primaryKey],
                    $primaryKey,
                    $parentNodeName,
                    $childNodeName
                );
                $resultArr[$key]      = $item;
            }
        }
        return $resultArr;
    }
}

if (! function_exists('list2tree')) {
    /**
     * Notes   : 无限级分类2
     *           这个速度要比递归稳定一些
     *
     * @Date   : 2023/7/5 10:48
     * <AUTHOR> <Jason.C>
     * @param  array  $list
     * @param  int  $parentId
     * @param  string  $primaryKey
     * @param  string  $parentNodeName
     * @param  string  $childNodeName
     * @return array
     */
    function list2tree(
        array $list,
        string $primaryKey = 'id',
        string $parentNodeName = 'parent_id',
        string $childNodeName = 'children',
        int $parentId = 0
    ): array {
        $tree  = [];
        $refer = [];
        foreach ($list as $key => $data) {
            $refer[$data[$primaryKey]] = &$list[$key];
        }
        foreach ($list as $key => $data) {
            $pid = $data[$parentNodeName];
            if ($parentId == $pid) {
                $tree[] = &$list[$key];
            } elseif (isset($refer[$pid])) {
                $parent                   = &$refer[$pid];
                $parent[$childNodeName][] = &$list[$key];
            }
        }
        return $tree;
    }
}

if (! function_exists('createOrderNo')) {
    /**
     * Notes   : 创建订单编号
     *
     * @Date   : 2023/7/25 09:30
     * <AUTHOR> <Jason.C>
     * @param  string  $prefix  编号前缀
     * @param  int  $length
     * @return string
     * @throws \Exception
     */
    function createOrderNo(string $prefix = '', int $length = 18): string
    {
        if ($length <= 13 || $length > 24) {
            throw new RuntimeException('数字长度必须大于13位且小于24位');
        }
        $rand = $length - 13;

        $time = explode(' ', microtime());
        $no   = date('ymdHis').sprintf("%0{$rand}d", $time[0] * (10 ** $rand));

        return $prefix.Sigma::orderNo($no);
    }
}

if (! function_exists('formatBytes')) {
    /**
     * Notes   : 格式化字节大小
     *
     * @Date   : 2023/8/1 17:37
     * <AUTHOR> <Jason.C>
     * @param  int  $size
     * @return string
     */
    function formatBytes(int $size): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        for ($i = 0; $size >= 1024 && $i < 5; $i++) {
            $size /= 1024;
        }
        return round($size, 2).$units[$i];
    }
}

if (! function_exists('calculateDistance1')) {
    /**
     * Notes   : 获取两个经纬度之间的距离
     *
     * @Date   : 2023/12/26 21:53
     * <AUTHOR> <Jason.C>
     * @param  float  $lat1  A点经度
     * @param  float  $lng1  A点纬度
     * @param  float  $lat2  B点经度
     * @param  float  $lng2  B点纬度
     * @param  float  $earthRadius  [6371]地球平均曲率半径；[6378.137]经度系数，1米所对应的经度为0.000011；[6370.996]地球半径系数
     * @return float  两点之间的距离，单位m
     */
    function calculateDistance1(
        float $lat1,
        float $lng1,
        float $lat2,
        float $lng2,
        float $earthRadius = 6371
    ): float {
        $radLat1 = deg2rad($lat1);
        $radLat2 = deg2rad($lat2);
        $a       = $radLat1 - $radLat2;
        $b       = deg2rad($lng1) - deg2rad($lng2);
        $s       = 2 * asin(sqrt(pow(
                    sin($a / 2),
                    2
                ) + cos($radLat1) * cos($radLat2) * pow(sin($b / 2), 2)));
        $s       = $s * $earthRadius * 1000;
        return round($s, 2);
    }
}

if (! function_exists('lecho')) {
    function lecho($key)
    {
        $type = session('language_type', config('languageDefault.key'));
        return config('language.'.$key.'.'.$type, '');
    }
}

if (! function_exists('alibaba')) {
    function alibaba($url, $paramsType, $params, $method = "GET")
    {
        $appcode = SystemConfig::getValue('ALI_APPCODE');

        try {
            $client   = new Client();
            $response = $client->request($method, $url, [
                'headers'   => [
                    'Authorization' => 'APPCODE '.$appcode,
                ],
                'verify'    => false,
                $paramsType => $params,
            ]);

            if ($response->getStatusCode() == 200) {
                $result = json_decode($response->getBody()->getContents(), true);
                if ($result['code'] == 200) {
                    return $result['data'];
                }
                if (isset($result['message'])) {
                    throw new ValidatorException($result['message']);
                }
                if (isset($result['msg'])) {
                    throw new ValidatorException($result['msg']);
                }
                throw new ValidatorException($result);
            }
        } catch (ClientException $clientException) {
            $response = $clientException->getResponse();
            if ($response) {
                $body = json_decode($response->getBody()->getContents(), true);
                if (isset($body['message'])) {
                    throw new ValidatorException($body['message']);
                }
                if (isset($body['msg'])) {
                    throw new ValidatorException($body['msg']);
                }
                throw new ValidatorException($body);
            }
            throw new ValidatorException($clientException->getMessage());
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
        throw new ValidatorException("验证失败");
    }
}

if (! function_exists('getKeyTitle')) {
    /**
     * 改变数组[key=>value],[[key=>key,title=>value]]
     *
     * @param $array
     * @return mixed
     */
    function getKeyTitle($array, $keyTitle = 'key', $valueTitle = 'title')
    {
        return collect($array)->map(function ($title, $key) use (
            $keyTitle,
            $valueTitle
        ) {
            return [
                $keyTitle   => $key,
                $valueTitle => $title,
            ];
        })->values();
    }
}
if (! function_exists('excel')) {
    function excel($filePath)
    {
        try {
            $spreadsheet = IOFactory::load($filePath);
            $worksheet   = $spreadsheet->getActiveSheet();

            // 获取行数和列数
            $highestRow    = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();

            $data = [];
            // 遍历 Excel 数据
            for ($row = 2; $row <= $highestRow; $row++) {
                $rowData = [];
                for ($col = 'A'; $col <= $highestColumn; $col++) {
                    $cellValue = $worksheet->getCell($col.$row)->getValue();
                    $rowData[] = $cellValue ?: "";
                }
                $data[] = $rowData;
            }
            return $data;
        } catch (Exception $e) {
            throw new ValidatorException($e->getMessage());
        }
    }
}

if (! function_exists('calcTokens')) {
    function calcTokens($str)
    {
        $total = 0;
        //统计汉字数量
        preg_match_all("/[\x{4e00}-\x{9fa5}]/u", $str, $chineseMatches);
        $total += count($chineseMatches[0]);
        //统计英文字母数量
        preg_match_all("/[a-zA-Z]/", $str, $letterMatches);
        $total += count($letterMatches[0]);
        //统计数字数量
        preg_match_all("/[0-9]/", $str, $numberMatches);
        $total += count($numberMatches[0]);
        //统计标点符号数量
        preg_match_all("/[^\w\s]|_/", $str, $punctuationMatches);
        $total += count($punctuationMatches[0]);
        //统计空格数量
        preg_match_all("/\s/", $str, $spaceMatches);
        $total += count($spaceMatches[0]);

        return $total;
    }
}

if (! function_exists('getArrKeyToString')) {
    function getArrKeyToString($array)
    {
        return implode(',', array_keys($array));
    }
}
if (! function_exists('validateUrlWithUnicode')) {
    function validateUrlWithUnicode($url)
    {
        // 先尝试直接验证
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            return $url;
        }

        // 编码所有非标准URL字符（保留协议、域名、路径分隔符等必要字符）
        $encoded_url = urlToUnicode($url);

        return filter_var($encoded_url, FILTER_VALIDATE_URL);
    }
}

if (! function_exists('urlToUnicode')) {
    function urlToUnicode($url)
    {
        return preg_replace_callback('/[^A-Za-z0-9\-_.~:\/]/', function ($match) {
            return rawurlencode($match[0]);
        }, $url);
    }
}
