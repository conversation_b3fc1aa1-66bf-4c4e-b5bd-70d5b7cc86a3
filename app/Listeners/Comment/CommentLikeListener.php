<?php

namespace App\Listeners\Comment;

use App\Events\CommentLikeEvent;
use App\Notifications\UserCommentLikeNotice;
use Illuminate\Contracts\Queue\ShouldQueue;

class CommentLikeListener implements ShouldQueue
{
    public string $connection = 'redis';

    public int $delay = 0;

    public int $tries = 1;

    public function handle(CommentLikeEvent $event): void
    {
        $like   = $event->like;
        $target = $like->target;//点赞对象 评论
        $target->user->notify(new UserCommentLikeNotice($like));
    }
}