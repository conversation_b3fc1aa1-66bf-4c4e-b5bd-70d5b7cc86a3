<?php

namespace App\Listeners\Like;

use App\Events\LikeCreatedEvent;
use App\Models\AiUnifyAsset;
use App\Models\Business;
use App\Notifications\UserLikeUnifyAssetNotice;
use App\Packages\ImPush\ImPush;
use Illuminate\Contracts\Queue\ShouldQueue;
use Log;

class LikeCreatedListener implements ShouldQueue
{
    public string $connection = 'redis';

    public int $delay = 0;

    public int $tries = 1;

    public function handle(LikeCreatedEvent $event): void
    {
        $like     = $event->like;
        $likeable = $like->likeable;
        $user     = $likeable->user;

        if (! $likeable) {
            Log::warning('Likeable is null for like ID: '.$like->id);
            return;
        }
        if (! $user) {
            Log::warning('User is null for like ID: '.$like->id);
            return;
        }

        //发送推送
        if ($likeable instanceof Business) {
            $imUser = $user->imUser;

            if ($imUser) {
                ImPush::push()->batchPush([$imUser->im_user_id], '有人给您点赞', '点赞提醒');
            }
        }

        //发送通知
        if ($likeable instanceof AiUnifyAsset) {
            $user->notify(new UserLikeUnifyAssetNotice($like));
        }
    }
}