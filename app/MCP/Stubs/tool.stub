<?php

namespace {{ namespace }};

use App\MCP\ToolsBase;
use Exception;
use Illuminate\Support\Facades\Validator;
use OPGG\LaravelMcpServer\Enums\ProcessMessageType;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class {{ className }}  extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return '{{ toolName }}';
    }

    public function description(): string
    {
        return 'Description of {{ className }} - explain what this tool does and its purpose';
    }

    public function inputSchema(): array
    {
        return [
            'type' => 'object',
            'properties' => [
                'param1' => [
                    'type' => 'string',
                    'description' => 'First parameter description - be specific about expected format and purpose',
                ],
            ],
            'required' => ['param1'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title' => '{{ className }}',
            'readOnlyHint' => false,
            'destructiveHint' => false,
            'idempotentHint' => false,
            'openWorldHint' => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        $validator = Validator::make($arguments, [
            'param1' => ['required', 'string'],
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $param1 = $arguments['param1'];
        try {

            $result = [
                'param1' => $param1,
            ];
            return $result;
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
