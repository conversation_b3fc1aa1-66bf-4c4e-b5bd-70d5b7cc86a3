<?php

namespace App\MCP\Traits;

use OpenAI;
use OpenAI\Client;

class OpenAiToolClient
{
    protected Client $client;

    public function __construct(string $apiKey, string $baseUri)
    {
        $this->client = OpenAI::factory()
            ->withApiKey($apiKey)
            ->withBaseUri($baseUri)
            ->withHttpClient(new \GuzzleHttp\Client([
                'verify' => false,
            ]))
            ->make();
    }

    public function chat(
        string $modelId,
        string $system,
        string $prompt,
        int $maxTokens = 1024,
        array $other = []
    ): string {
        $params   = [
            'model'      => $modelId,
            'messages'   => [
                [
                    'role'    => 'system',
                    'content' => $system,
                ],
                [
                    'role'    => 'user',
                    'content' => $prompt,
                ],
            ],
            'max_tokens' => $maxTokens,
        ];
        $params   = array_merge($params, $other);
        $response = $this->client->chat()->create($params);
        return $response->choices[0]->message->content;
    }
}