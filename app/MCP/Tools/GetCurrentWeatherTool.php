<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\BailianAssistant\Tools\Gaode;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class GetCurrentWeatherTool extends ToolsBase implements ToolInterface
{

    /**
     * Get the tool name.
     *
     * @return string
     */
    public function name(): string
    {
        return 'get_current_weather';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return '查询天气的工具，需要用户说出城市的名称';
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            'type'       => 'object',//不用动
            'properties' => [
                'city' => [
                    'type'        => 'string',//类型
                    'description' => '要查询天气的城市名称',
                ],

            ],
            'required'   => ['city'],//必填字段
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '查询天气'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            $city   = $arguments['city'];
            $gaode  = new Gaode();
            $adCity = $gaode->getAdCodeCity($city);
            $wather = $gaode->getWeather($adCity);
            return $this->success($wather);
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    public function getTtsContent(array $params): string
    {
        $city    = $params[0];
        $casts   = $city['casts'][0];
        $weather = $casts['nightweather'] == $casts['dayweather'] ? $casts['nightweather'] : $casts['nightweather'].'转'.$casts['dayweather'];
        return sprintf('%s今日天气情况.%s.%s至%s摄氏度.%s风%s',
            $city['city'],
            $weather,
            $casts['nighttemp'],
            $casts['daytemp'],
            $casts['daywind'],
            str_replace('-', '至', $casts['daypower']).'级',
        );
    }
}
