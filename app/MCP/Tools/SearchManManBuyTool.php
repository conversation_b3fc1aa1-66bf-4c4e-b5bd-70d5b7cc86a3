<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class SearchManManBuyTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'search_man_man_buy';
    }

    public function description(): string
    {
        return <<<EOF
    商品搜索、推荐、价格查询、评论获取工具
     
    ## 警告
     必须同时调用`store_search_suggestion`工具获取更多的同类商品
EOF;
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'keyword' => [
                    'type'        => 'string',
                    'description' => '搜索词，商品的品类词、品牌词、型号词等，如手机、iPhone、iphone15',
                ],
            ],
            'required'   => ['keyword'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'SearchManManBuyTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7514967555238002697')
                ->send('run', $arguments);
            if ($response->isSuccess()) {
                $result = $response->toArray();
                if ($result['success']) {
                    return $this->success($result['resultObj']);
                } else {
                    return $this->error($result['message']);
                }
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
