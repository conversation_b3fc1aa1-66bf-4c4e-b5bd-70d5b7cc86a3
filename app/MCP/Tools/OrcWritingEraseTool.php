<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\MCP\Traits\YoudaoAiClient;
use Exception;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class OrcWritingEraseTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'orc_writing_erase';
    }

    public function description(): string
    {
        return '试卷手写体擦除，要确认处理的图片内容是一张试卷，想要擦出掉试卷/图片中的手写体使用此工具';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'image_url' => [
                    'type'        => 'string',
                    'description' => '图片URL地址',
                ],
                // Add more parameters as needed
            ],
            'required'   => ['image_url'],
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '试卷手写体擦除'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        Validator::make($arguments, [
            'image_url' => ['required', 'string'],
            // Add more validation rules as needed
        ])->validate();
        $imageUrl = $arguments['image_url'] ?? '';
        if (blank($imageUrl)) {
            return $this->error('图片URL地址不能为空');
        }
        try {
            $kernel = new YoudaoAiClient('https://openapi.youdao.com');
            $result = $kernel->OrcWritingErase($imageUrl);

            if ($result['code'] == 200) {
                $eraseEnhanceImg = base64_decode($result['data']['eraseEnhanceImg'], true);

                $ext = getimagesizefromstring($eraseEnhanceImg);

                $ext      = image_type_to_extension($ext[2]);
                $pathName = sprintf('aiassistant/%s/%s%s',
                    date('Y/m/d'),
                    Str::uuid()->toString(),
                    $ext
                );
                Storage::put($pathName, $eraseEnhanceImg);
                return $this->success([
                    'image_url' => Storage::url($pathName),
                ]);
            } else {
                return $this->error($result['message']);
            }
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

}
