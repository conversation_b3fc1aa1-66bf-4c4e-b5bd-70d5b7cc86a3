<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\MCP\Traits\YoudaoAiClient;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class EssayCorrectionTool extends ToolsBase implements ToolInterface
{

    /**
     * Get the tool name.
     *
     * @return string
     */
    public function name(): string
    {
        return 'essay_correction';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return '作文批改、评分工具，区分英文作文或中文作文, 并给出对应的批改结果。要区分中文作文和英文作文。**不要修改工具输出的内容**';
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'image_url' => [
                    'type'        => 'string',
                    'description' => '图片url',
                ],
                'content'   => [
                    'type'        => '作文内容',
                    'description' => '图片url',
                ],
                'language'  => [
                    'type'        => 'string',
                    'description' => '语言',
                    'enum'        => ['zh', 'en']
                ]
            ],
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '作文批改'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            $imageUrl = $arguments['image_url'] ?? '';
            $content  = $arguments['content'] ?? '';
            $language = $arguments['language'] ?? 'zh';
            if (blank($imageUrl) && blank($content)) {
                return $this->error('没有识别到需要评判的作文内容');
            }
            if (! blank($imageUrl)) {
                $composition = file_get_contents($imageUrl);
                $url         = 'image';
            } else {
                $composition = $content;
                $url         = 'text';
            }
            $url    = match ($language) {
                'zh' => 'correct_writing_cn_'.$url,
                'en' => 'correct_writing_'.$url,
            };
            $youdao = new YoudaoAiClient('https://openapi.youdao.com/v2');
            $result = $youdao->composition($url, $composition);
            if ($result['code'] == 200) {
                list($output, $orgContent) = match ($language) {
                    'zh' => $this->checkZhDoc($result['data']),
                    'en' => $this->checkEnDoc($result['data']),
                };
                return $this->success([
                    'OriginalArticle' => $orgContent,
                    'Analysis'        => $output,
                ]);
            } else {
                return $this->error($result['message']);
            }
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    protected function checkZhDoc(array $result): array
    {
        $result           = collect($result);
        $orgContent       = $result->get('orgContent', '');
        $scoreCollection  = collect($result->get('scoreCollection', []));
        $perspectiveScore = collect($scoreCollection->get('perspectiveScore', []));
        $score            = $scoreCollection->get('score');
        $scoreArray       = [
            'sentimentSincerity' => $perspectiveScore->get('sentimentSincerity', ''),
            'essayFluence'       => $perspectiveScore->get('essayFluence', ''),
            'structureStrict'    => $perspectiveScore->get('structureStrict', ''),
            'goodSent'           => $perspectiveScore->get('goodSent', ''),
        ];
        $scoreArray       = array_merge($scoreArray, [
            'sentimentSincerityDoc' => $this->sentimentSincerity($scoreArray['sentimentSincerity']),
            'essayFluenceDoc'       => $this->essayFluence($scoreArray['essayFluence']),
            'structureStrictDoc'    => $this->structureStrict($scoreArray['structureStrict']),
            'goodSentDoc'           => $this->goodSent($scoreArray['goodSent']),
        ]);

        $commentCollection  = collect($result->get('commentCollection', []));
        $detailedEvaluation = collect($result->get('detailedEvaluation', []));
        $correctedContent   = collect($result->get('correctedContent', []));
        $data               = view('mcp.tools.zh_essay_correction', compact(
            'orgContent',
            'score',
            'scoreArray',
            'commentCollection',
            'detailedEvaluation',
            'correctedContent'
        ));
        return [$data->toHtml(), $orgContent];
    }

    private function sentimentSincerity(string $score)
    {
        return match ($score) {
            '0' => '感受不深刻，缺乏感染力。',
            '1' => '情感缺乏深度。',
            '2' => '语言简朴，感情平淡，情意不够深切。',
            '3' => '感情真实，发自肺腑。',
            '4' => '情感饱满，情真意切，能深深打动读者。',
            '5' => '感情真挚动人，情感丰沛，极富感染力。',
            default => '',
        };
    }

    private function essayFluence(string $score)
    {
        return match ($score) {
            '0' => '语句不通顺，表述不清晰，令人费解。',
            '1' => '语句欠通，缺乏连贯性。',
            '2' => '语句基本通顺，偶有不畅。',
            '3' => '语句通顺流畅。',
            '4' => '语句流畅连贯，自然通达。',
            '5' => '语言平滑晓畅，可读性强。',
            default => '',
        };
    }

    private function structureStrict(string $score)
    {
        return match ($score) {
            '0' => '条理不清，结构混乱。',
            '1' => '层次不清，结构较乱。',
            '2' => '层次欠清楚，结构较合理。',
            '3' => '层次较分明，结构完整。',
            '4' => '层次清晰，结构完整。',
            '5' => '层次清晰，结构严谨。',
            default => '',
        };
    }

    private function goodSent(string $score)
    {
        return match ($score) {
            '0' => '',
            '1' => '用词过于平淡，缺乏文采。',
            '2' => '偶尔使用好词好句，语言平淡质朴。',
            '3' => '能够适当运用好词好句，语言生动活泼。',
            '4' => '遣词造句优美，文章富有文采。',
            '5' => '本文语言极其优美，文章极富文学气息。',
            default => '',
        };
    }

    protected function checkEnDoc(array $result): array
    {
        return $result;
    }
}
