<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class SearchHotFormDouyinTool extends ToolsBase implements ToolInterface
{
    public function name(): string
    {
        return 'search_hot_form_douyin';
    }

    public function description(): string
    {
        return '抖音热点榜插件为用户提供实时的抖音热搜数据，它能够自动获取当下抖音平台最热门的话题，并对这些数据进行格式化处理。插件支持使用中文标识热点类型，如“新”“热”“爆”等，同时会展示每个热点的热度值以及数据更新时间。借助该插件，用户可以及时洞察社交媒体的流行趋势，快速发现热门话题，更好地把握网络动态。';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => (object) [],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'SearchHotFormDouyinTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7512761877715419146')
                ->send('run', []);
            $data     = array_chunk($response->toArray(), 15, true)[0] ?? [];
            return $this->success($data);
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
