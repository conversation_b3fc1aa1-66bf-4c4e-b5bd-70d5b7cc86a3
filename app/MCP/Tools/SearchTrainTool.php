<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class SearchTrainTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'search_train';
    }

    public function description(): string
    {
        return '[查车票]：根据出发日期（默认当天）、出发地、目的地、[车票类型]、[车次(可查多个车次)]、[出发时间范围]查询车票信息，返回车次、车票类型、车票余量、车票价格、车票折扣、放票时间、停止检票剩余时间、历时等几乎所有详细信息。';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'from_station' => [
                    'type'        => 'string',
                    'description' => '出发站',
                ],
                'to_station'   => [
                    'type'        => 'string',
                    'description' => '到达站',
                ],
                'train_codes'  => [
                    'type'        => 'string',
                    'description' => '车次代码（英文逗号分隔可查询多个）（按车次查询不分页，也不管时间范围），如：G105',
                ],
                'train_date'   => [
                    'type'        => 'string',
                    'default'     => date('Y-m-d'),
                    'description' => '出发时间格式（YYYY-MM-DD）',
                ],
                'time_min'     => [
                    'type'        => 'string',
                    'default'     => '00:00',
                    'description' => '出发时间范围最小值，格式：HH:mm',
                ],
                'time_max'     => [
                    'type'        => 'string',
                    'default'     => '23:59',
                    'description' => '出发时间范围最大值，格式：HH:mm',
                ],
                'page_no'      => [
                    'type'        => 'integer',
                    'default'     => 1,
                    'description' => '要查询的页数',
                ],
            ],
            'required'   => ['from_station', 'to_station'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'SearchTrainTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7514932454781911051')
                ->send('run', [
                    'from_station' => $arguments['from_station'],
                    'to_station'   => $arguments['to_station'],
                    'train_codes'  => $arguments['train_codes'] ?? '',
                    'train_date'   => $arguments['train_date'] ?? '',
                    'time_min'     => $arguments['time_min'] ?? '00:00',
                    'time_max'     => $arguments['time_max'] ?? '23:59',
                    'page_no'      => $arguments['page_no'] ?? 1,
                ]);
            if ($response->isSuccess()) {
                $result = $response->toArray();
                if ($result['code'] === 1) {
                    return $this->success($result);
                } else {
                    return $this->error($result['message']);
                }
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
