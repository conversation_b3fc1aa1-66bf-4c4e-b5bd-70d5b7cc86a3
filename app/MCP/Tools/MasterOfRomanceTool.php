<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class MasterOfRomanceTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'master_of_romance';
    }

    public function description(): string
    {
        return <<<EOF
功能：根据用户输入的需要查看的语录类型，如笑话、安慰语录等，为用户提供相应的语录内容。
使用场景：当用户在不同场景下需要不同类型的语录时都可使用。
例如
- 在聚会想活跃气氛时可使用“笑话”“趣味笑话”；
- 朋友伤心难过需要安慰时可查找“安慰语录”；
- 想更换 QQ 签名时可选择“QQ 签名”；
- 谈恋爱想表达心意可使用“爱情语录”“情话”等。
- 当想要感悟生活时，“人生话语”“随机一言”“经典语录”能带来启发；
- 心情低落时“毒鸡汤”可以来一剂别样宽慰；
- 文艺表达时“诗词”“文案温柔”是好选择；
- 玩闹时“骚话”“舔狗日记”增添趣味；
- 表现社会态度用“社会语录”；
- 抒发伤感情绪用“伤感语录”；
- 体现自我状态选用“我在人间凑数的日子”“网易语录”。 
EOF;
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'msg' => [
                    'type'        => 'string',
                    'enum'        => [
                        '笑话', '安慰语录', 'QQ签名', '趣味笑话', '随机一言', '英汉语录', '毒鸡汤', '爱情语录',
                        '文案温柔', '伤感语录',
                        '舔狗日记', '社会语录', '诗词', '骚话', '经典语录', '情话', '人生话语', '我在人间凑数的日子',
                        '网易语录'
                    ],
                    'description' => '需要生成的语录类型',
                ],
            ],
            'required'   => ['msg'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'MasterOfRomanceTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7514959819950932002')
                ->send('run', [
                    'msg' => $arguments['msg'],
                ]);
            if ($response->isSuccess()) {
                $result = $response->toArray();
                return $this->success($result);
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
