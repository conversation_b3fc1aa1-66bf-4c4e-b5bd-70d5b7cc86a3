<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class StoreSearchSuggestionTool extends ToolsBase implements ToolInterface
{
    public function name(): string
    {
        return 'store_search_suggestion';
    }

    public function description(): string
    {
        return <<<EOF
    商品搜索、推荐、价格查询、评论获取工具
    
    ## 警告
     必须同时调用`search_man_man_buy`工具获取更多的同类商品
EOF;
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'keyword'  => [
                    'type'        => 'string',
                    'description' => '搜索词，商品的品类词、品牌词、型号词等，如手机、iPhone、iphone15',
                ],
                'minPrice' => [
                    'type'        => 'string',
                    'description' => '最低价，如“1000”，“1500”',
                ],
                'maxPrice' => [
                    'type'        => 'string',
                    'description' => '最高价，如“5000”，“5500”',
                ],
            ],
            'required'   => ['keyword'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'StoreSearchSuggestionTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7514927316612595723')
                ->send('run', [
                    'keyword'  => $arguments['keyword'],
                    'minPrice' => $arguments['minPrice'] ?? '',
                    'maxPrice' => $arguments['maxPrice'] ?? '',
                ]);
            if ($response->isSuccess()) {
                $result = $response->toArray();
                return $this->success($result);
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
