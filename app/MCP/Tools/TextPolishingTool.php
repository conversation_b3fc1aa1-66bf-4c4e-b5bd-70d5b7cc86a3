<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\MCP\Traits\OpenAiToolClient;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class TextPolishingTool extends ToolsBase implements ToolInterface
{

    public function description(): string
    {
        return '文本文章润色、改写、美化工具';
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'article_title'   => [
                    'type'        => 'string',
                    'description' => '要润色文章的标题',
                ],
                'article_content' => [
                    'type'        => 'string',
                    'description' => '要润色文章的内容',
                ],
                // Add more parameters as needed
            ],
            'required'   => ['article_title', 'article_content'],
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '文章润色'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            $articleTitle   = $arguments['article_title'] ?? '';
            $articleContent = $arguments['article_content'] ?? '';

            $system = wateGetSystemPrompt('TextPolishing');

            if ($system === false) {
                return $this->error('工具SystemPrompt错误');
            }
            $prompt = <<<EOF
            文章标题：{$articleTitle}
            文章内容：{$articleContent}
            EOF;
            $openAi = new OpenAiToolClient(
                'sk-6625f866cdcd41feb700ae01715e75df',
                'https://dashscope.aliyuncs.com/compatible-mode/v1',
            );
            $result = $openAi->chat('qwen-plus-latest', $system, $prompt, 8000, [
                'enable_search'  => true,
                'search_options' => [
                    'forced_search'   => true,
                    'search_strategy' => 'pro',
                ],
            ]);
            return $this->success([
                'article' => $result,
            ]);
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    public function name(): string
    {
        return 'text_polishing';
    }
}
