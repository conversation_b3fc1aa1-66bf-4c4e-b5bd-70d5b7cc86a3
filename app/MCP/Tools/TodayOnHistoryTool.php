<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class TodayOnHistoryTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'today_on_history';
    }

    public function description(): string
    {
        return '用户想要知道在历史上今天这个日期发生了什么事情，调用此工具。将结果全部告诉用户';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => (object) [
            ],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'TodayOnHistoryTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7512724998127206412')
                ->send('run', [
                    'date' => now()->format('md'),
                ]);
            if ($response->isSuccess()) {
                $result = $response->toArray();
                if ($result['code'] === '1') {
                    $number = count($result['data']);
                    return $this->success($result['data'], "一共{$number}条，完成的一个不落的告诉用户");
                } else {
                    return $this->error('未知错误');
                }
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
