<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\MCP\Traits\AliCloudMarketClient;
use Exception;
use Illuminate\Support\Facades\Validator;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class QueryBankCardTool extends ToolsBase implements ToolInterface
{
    protected string $appCode = 'de2aaac75e674e6f963434be21ed235c';

    public function name(): string
    {
        return 'query_bank_card';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return <<<EOF
# 银行卡归属信息查询,用户查询银行卡信息使用此工具
## 说明
- 银行卡号码一半16~19位数字组成
- 请注意认真识别用户输入的是否是银行卡号码
EOF;
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'card_no' => [
                    'type'        => 'string',
                    'description' => '银行卡号,16~19位数字组成',
                ],
            ],
            'required'   => ['card_no'],
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '银行卡归属信息查询'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            Validator::make($arguments, [
                'card_no' => ['required', 'string'],
            ])->validate();

            $cardNo = $arguments['card_no'] ?? '';
            if (blank($cardNo)) {
                return $this->error('请输入银行卡号');
            }
            $aliCloud = new AliCloudMarketClient('https://bankaddress.shumaidata.com/', $this->appCode);

            $data = $aliCloud->get('bankaddress', [
                'bankcard' => $cardNo,
            ]);
            if ($data['code'] === 200) {
                return $this->success($data['data']);
            } else {
                return $this->error($data['msg']);
            }
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }
}
