<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use Illuminate\Support\Facades\Validator;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class FetchWebUrlTool extends ToolsBase implements ToolInterface
{
    public function name(): string
    {
        return 'fetch_web_url';
    }

    public function description(): string
    {
        return '抓取分析网页内容，需要调用此工具';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'url' => [
                    'type'        => 'string',
                    'description' => 'url地址',
                ],
            ],
            'required'   => ['url'], // Specify which parameters are mandatory
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'FetchWebUrlTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        $validator = Validator::make($arguments, [
            'url' => ['required', 'string'],
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $url = $arguments['url'];
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7512402844689154063')
                ->send('run', [
                    'input' => $url,
                ]);
            if ($response->isSuccess()) {
                $data = $response->toArray();
                if ($data['code'] === 0) {
                    return $this->success($data['data']);
                } else {
                    return $this->error($data['error_msg']);
                }
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
