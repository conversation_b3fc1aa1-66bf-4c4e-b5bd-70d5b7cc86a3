<?php

namespace App\MCP\Tools;

use App\Jobs\Plugin\JmDrawJob;
use App\MCP\ToolsBase;
use App\Models\AiUnifyAsset;
use App\Models\PluginJmDraw;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class GenImageTool extends ToolsBase implements ToolInterface
{

    /**
     * Get the tool name.
     *
     * @return string
     */
    public function name(): string
    {
        return 'gen_image';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return <<<EOF
    这是一个生成图片的工具
    输入参数：
    prompt: 图片的描述，图片上的文字用半角双引号包裹，风格等使用半角中括号包裹，可以有可以没有，如果用户未指定描述则你需要协助用户拟定一个图片描述。
    image_url: 参考图片的url地址，不要传入上下文中从未出现过的图片.仅支持png,jpeg,jpg格式
    wateauth: 用户的鉴权信息
EOF;
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            'type'       => 'object',//不用动
            'properties' => [
                'prompt'    => [
                    'type'        => 'string',//类型
                    'description' => '图片的描述，图片上的文字用半角双引号包裹，风格等使用半角中括号包裹。',
                ],
                'image_url' => [
                    'type'        => 'string',
                    'format'      => 'uri',
                    'description' => '参考图片的url地址，不要传入上下文中从来没有出现过的图片.仅支持png,jpeg,jpg格式',
                ],
                'wateauth'  => [
                    'type'        => 'string',
                    'description' => '用户的鉴权信息',
                ],
            ],
            'required'   => ['prompt', 'wateauth'],//必填字段
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '瓦特1.0图片生成'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            $user           = $this->getUser($arguments);//获取参数中的AUTH
            $prompt         = $arguments['prompt'];
            $imageUrl       = $arguments['image_url'] ?? '';
            $type           = PluginJmDraw::TYPE_NORMAL_IMAGE;
            $sampleStrength = 5;
            if (! blank($imageUrl)) {
                $sampleStrength = 1;
                $type           = PluginJmDraw::TYPE_IMAGE_IMAGE;
            }
            $data = [
                'user_id' => $user->id,
                'prompt'  => $prompt,
                'type'    => $type,
                'inputs'  => blank($imageUrl) ? [] : [
                    [
                        'url' => $imageUrl,
                    ]
                ],
                'params'  => [
                    'sample_strength' => $sampleStrength,
                    'ratio'           => '9:16',
                    'width'           => 864,
                    'height'          => 1152,
                ],
            ];
            $log  = PluginJmDraw::create($data);
            JmDrawJob::dispatchSync($log);
            $log->refresh();
            if ($log->status == AiUnifyAsset::STATUS_SUCCESS) {
                $tip = '';
                if ($log->score > 0) {
                    $tip = "本次内容使用积分{$log->score}分";
                }
                return $this->success([
                    'media_type' => $log->media_type,
                    'asset_id'   => $log->asset->id,
                    'asset_no'   => $log->asset->no,
                    'url'        => $log->coverUrlAttr,
                    'tip'        => ! blank($tip) ? '提示:'.$tip : '',
                ]);
            } else {
                return $this->error($log->error_message);
            }
        } catch (Exception $exception) {
            info($exception);
            return $this->error($exception->getMessage());
        }
    }
}
