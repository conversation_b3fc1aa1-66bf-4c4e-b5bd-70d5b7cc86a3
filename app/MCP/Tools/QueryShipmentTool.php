<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\MCP\Traits\AliCloudMarketClient;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class QueryShipmentTool extends ToolsBase implements ToolInterface
{

    /**
     * Get the tool name.
     *
     * @return string
     */
    public function name(): string
    {
        return 'query_shipment';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return '根据快递单号查询快递信息的工具';
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            //不用动
            'type'       => 'object',
            'properties' => [
                //字段名称
                'no'          => [
                    'type'        => 'string',//类型
                    'description' => '检测到的快递单号',
                    //参数描述
                ],
                'mobile_last' => [
                    'type'        => 'string',//类型
                    'description' => '寄/收件人手机号后四位',
                    //参数描述
                ],
            ],
            'required'   => ['no'],
            //必填字段
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '查询快递单号'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            $no         = $arguments['no'] ?? '';
            $mobileLast = $arguments['mobile_last'] ?? '';
            if (blank($no)) {
                return $this->error('请输入单号');
            }
            $appcode = "de2aaac75e674e6f963434be21ed235c";

            $aliCloud = new AliCloudMarketClient('https://wuliu.market.alicloudapi.com', $appcode);
            $data     = $aliCloud->get('kdi', [
                'no' => $no.(blank($mobileLast) ? '' : ':'.$mobileLast),
            ]);
            if ($data['status'] === '0') {
                return $this->success($data['result']);
            } else {
                return $this->error($data['msg']);
            }
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }
}
