<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\MCP\Traits\AliCloudMarketClient;
use Exception;
use Illuminate\Support\Facades\Validator;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class QueryPersonFaceTool extends ToolsBase implements ToolInterface
{
    protected string $appCode = 'de2aaac75e674e6f963434be21ed235c';

    public function name(): string
    {
        return 'query_person_face';
    }

    public function description(): string
    {
        return <<<EOF
要分析照片中人物的颜值、年龄、心情请调用此工具。
EOF;
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'image_url' => [
                    'type'        => 'string',
                    'description' => '需要分析的图片链接url',
                ],
            ],
            'required'   => ['image_url'],
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '分析人物肖像'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            Validator::make($arguments, [
                'image_url' => ['required', 'url'],
            ])->validate();

            $imageUrl = $arguments['image_url'] ?? '';
            if (blank($imageUrl)) {
                return $this->error('请上传图片');
            }
            $aliCloud = new AliCloudMarketClient('https://kzaifacev2.market.alicloudapi.com', $this->appCode);
            $data     = $aliCloud->post('api/ai/detect_face', [
                'image' => 'image',
                'url'   => $imageUrl,
            ]);
            if ($data['code'] === 200) {
                return $this->success($data['data']['images'] ?? []);
            } else {
                return $this->error($data['msg']);
            }
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }
}
