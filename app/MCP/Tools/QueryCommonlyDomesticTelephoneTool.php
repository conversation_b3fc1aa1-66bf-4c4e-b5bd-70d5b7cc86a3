<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class QueryCommonlyDomesticTelephoneTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'query_commonly_domestic_telephone';
    }

    public function description(): string
    {
        return <<<EOF
# 查询全国常用电话信息
- 功能介绍：通过输入关键词或单位来查询对应的全国常用电话号码。
- 使用场景：当用户需要快速获取政府机构、公共服务部门、企业等的电话号码时，可使用本工具进行查询，以便及时联系到相关单位或部门解决问题。
EOF;
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'word' => [
                    'type'        => 'string',
                    'description' => '所要查询的单位',
                ],
            ],
            'required'   => ['word'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'QueryCommonlyDomesticTelephoneTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7514955488518553626')
                ->send('run', [
                    'word' => $arguments['word'],
                ]);
            if ($response->isSuccess()) {
                $result = $response->toArray();
                if ($result['code'] == 200) {
                    return $this->success($result['result']);
                } else {
                    return $this->error($result['msg']);
                }
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
