<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class MovieCurrentlyBeingReleasedTool extends ToolsBase implements ToolInterface
{

    /**
     * Get the tool name.
     *
     * @return string
     */
    public function name(): string
    {
        return 'get_currently_being_movie_released';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return '获取正在上映或即将上映的影片';
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => (object) []
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '查询上映或即将上映的影片'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        set_time_limit(0);
        $response = Coze::workFlow()
            ->setWorkFlowId('7512395918776352806')
            ->send('run', [
                'pageIndex' => 1,
                'pageSize'  => 10,
            ]);
        if ($response->isSuccess()) {
            $data = $response->toArray();
            if ($data['status_code'] === 0) {
                return $this->success($data['data']['return_value']);
            } else {
                return $this->error($data['message']);
            }
        } else {
            return $this->error($response->getMessage());
        }
    }

    public function getTtsError(string $message): string
    {
        return $message;
    }

    public function getTtsContent(array $params): string
    {
        foreach ($params as $key => $item) {
            $sunny .= '第'.($key + 1).'部电影.';
            $star  = "";
            if (! empty($item['leading_role'] && explode(',', $item['leading_role'])[0])) {
                $star  = explode(',', $item['leading_role'])[0];
                $sunny .= '由'.$star.'主演的';
            }
            $dongci = "已在";
            if (strtotime($item['open_day']) > time()) {
                $dongci = '即将在';
            }
            $sunny .= $item['show_name'].$dongci.date('m-d', strtotime($item['open_day'])).'上映.';
        }
        return $sunny;
    }
}
