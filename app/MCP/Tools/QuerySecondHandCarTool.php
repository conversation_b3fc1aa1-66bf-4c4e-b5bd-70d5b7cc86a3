<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class QuerySecondHandCarTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'query_second_hand_car';
    }

    public function description(): string
    {
        return '当你查询二手车的售卖信息时候可以使用此工具，可以获得二手车的价格、二手车车况图片等信息.';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'city'      => [
                    'type'        => 'string',
                    'description' => '期望查询二手车的城市',
                ],
                'series'    => [
                    'type'        => 'string',
                    'description' => '期望查询的车系，如宝马1系、奥迪a4等',
                ],
                'brand'     => [
                    'type'        => 'string',
                    'description' => '期望查询的汽车品牌，如宝马、奥迪',
                ],
                'grade'     => [
                    'type'        => 'string',
                    'description' => '期望查询的二手车类型、如中型suv、紧凑型轿车等',
                ],
                'price_tag' => [
                    'type'        => 'string',
                    'description' => '期望查询二手车的价格范围',
                ],
            ],
            'required'   => ['city'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'QuerySecondHandCarTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $city     = $arguments['city'];
            $series   = $arguments['series'] ?? '';
            $brand    = $arguments['brand'] ?? '';
            $grade    = $arguments['grade'] ?? '';
            $priceTag = $arguments['price_tag'] ?? '';
            $response = Coze::workFlow()
                ->setWorkFlowId('7516835341815431177')
                ->send('run', [
                    'city'      => $city,
                    'series'    => $series,
                    'brand'     => $brand,
                    'grade'     => $grade,
                    'price_tag' => $priceTag,
                ]);
            if ($response->isSuccess()) {
                return $this->success($response->toArray());
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
