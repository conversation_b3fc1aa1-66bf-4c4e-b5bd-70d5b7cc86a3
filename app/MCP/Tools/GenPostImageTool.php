<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Illuminate\Support\Facades\Validator;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class GenPostImageTool extends ToolsBase implements ToolInterface
{

    /**
     * Get the tool name.
     *
     * @return string
     */
    public function name(): string
    {
        return 'gen_post_image';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return <<<EOF
        你是一个海报设计专家，帮助用户设计海报
        ## 技能1：抽取海报分类
            根据用户输入的内容来理解用户想要设计的海报类型，比如：宣传、招聘、活动、会议等等
         
        ## 技能2：提炼优化描述内容
            提炼并优化用户针对海报的描述
        ## 技能3：优化结构
         结构化信息：主题 / 用途 / 场景 / 受众 / 关键信息（时间 / 地点 / 亮点）
EOF;
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'prompt'   => [
                    'type'        => 'string',
                    'description' => '用户对海报的描述',
                ],
                'category' => [
                    'type'        => 'string',
                    'description' => '海报分类',
                ],
                'font'     => [
                    'type'        => 'string',
                    'description' => '附着文字',
                ],
                // Add more parameters as needed
            ],
            'required'   => ['prompt'],
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '海报生成'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        Validator::make($arguments, [
            'prompt' => ['required', 'string'],
        ])->validate();

        $prompt   = $arguments['prompt'] ?? '';
        $category = $arguments['category'] ?? '';
        $font     = $arguments['font'] ?? '';

        $response = Coze::workFlow()
            ->setWorkFlowId('7512401513802743860')
            ->send('run', [
                'input' => <<<EOF
    海报描述：{$prompt}
    分类：{$category}
    文字：{$font}
EOF,
            ]);
        if ($response->isSuccess()) {
            $body = $response->toArray();
            if ($body['code'] === 200) {
                $images       = $body['data']['result'] ?? [];
                $resultImages = [];
                foreach ($images as $image) {
                    $resultImages[] = [
                        'image_url' => $image['imageUrl'],
                    ];
                }
                return $this->success($resultImages);
            } else {
                return $this->error($body['msg']);
            }
        } else {
            return $this->error($response->getMessage());
        }
    }
}
