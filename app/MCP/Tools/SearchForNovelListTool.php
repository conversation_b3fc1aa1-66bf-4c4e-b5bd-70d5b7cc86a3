<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class SearchForNovelListTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'search_for_novel_list';
    }

    public function description(): string
    {
        return '搜索、推荐小说的工具，提炼问题关键信息分析出书名\关键字、标签、作者、书名、频道、状态，返回小说列表。**先于(联网搜索/知识库)内容**';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'author'   => [
                    'type'        => 'string',
                    'description' => '作者',
                ],
                'bookName' => [
                    'type'        => 'string',
                    'description' => '书名或关键字',
                ],
                'gender'   => [
                    'type'        => 'integer',
                    "enum"        => [1, 2],
                    'description' => '频道，1 男频、2女频',
                    'default'     => 0,
                ],
                'status'   => [
                    'type'        => 'integer',
                    "enum"        => [1, 2],
                    'description' => '状态，可选值 1 完结、2连载',
                    'default'     => 0,
                ],
                'tag'      => [
                    'type'        => 'string',
                    'description' => '小说的标签，例如：网友、系统、修仙、都市等',
                ],
            ],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'SearchForNovelListTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7512737193154314292')
                ->send('run', $arguments);
            if ($response->isSuccess()) {
                $result = $response->toArray();
                if ($result['code'] !== 0) {
                    return $this->error($result['msg']);
                }
                $result = $result['data'];
                if (! $result['ok']) {
                    return $this->error($result['msg']);
                }
                $result = $result['books'];
                $data   = collect($result)->map(function ($item) {
                    return [
                        'author'      => $item['author'],
                        'title'       => $item['title'],
                        'cover'       => $item['cover'],
                        'link'        => $item['jumpLink'],
                        'category'    => $item['majorCate'],
                        'short_intro' => $item['shortIntro'],
                        'score'       => $item['ratingScore'],
                        'word_count'  => $item['wordCount'],
                    ];
                })->toArray();
                $number = count($data);
                return $this->success($data, "一共{$number}本小说，请全部告知用户");
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
