<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class WebPageScreenshotTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'web_page_screenshot';
    }

    public function description(): string
    {
        return <<<EOF
可以通过输入网址，快速获取该网页的高质量截图，并以图片 URL 形式返回。
 - 功能介绍：输入网址后能迅速获取对应网页的高质量截图。
 - 使用场景：当需要快速获取特定网页的截图用于文档制作、报告展示、社交媒体分享等时，可以方便地使用该工具获取截图的 URL 并进行后续使用。
EOF;
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'url'      => [
                    'type'        => 'string',
                    'description' => '目标网址',
                ],
                'isMobile' => [
                    'type'        => 'boolean',
                    'default'     => false,
                    'description' => '是否是手机可访问的网页',
                ],
            ],
            'required'   => ['url'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'WebPageScreenshotTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7515239782063341608')
                ->send('run', [
                    'url'      => $arguments['url'],
                    'isMobile' => $arguments['isMobile'] ?? false,
                ]);
            if ($response->isSuccess()) {
                $result = $response->toArray();
                if ($result['code'] === 0) {
                    return $this->success($result);
                } else {
                    return $this->error($result['message']);
                }
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
