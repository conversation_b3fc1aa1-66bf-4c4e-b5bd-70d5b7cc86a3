<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class QueryBilibiliHotTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'query_bilibili_hot';
    }

    /**
     * Very important -- A human-readable description of what your tool does for LLMs.
     *
     * This description is shown in MCP client UIs and documentation,
     * also the LLM will see this description to determine if it should use this tool.
     *
     * Be clear and concise about the tool's functionality and purpose.
     *
     * @return string The tool description
     */
    public function description(): string
    {
        return '搜索（Bilibili、B站、哔哩哔哩）热门内容,可以根据返回内容的编号搜索视频列表';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'limit' => [
                    'type'        => 'integer',
                    'description' => '搜索数量',
                    'default'     => 10
                ],
            ],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'B站热门搜索',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        $limit = $arguments['limit'] ?? 10;
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7512712426476732426')
                ->send('run', [
                    'limit' => $limit,
                ]);
            if ($response->isSuccess()) {
                $result = [];
                $i      = 1;
                foreach ($response->toArray()['data']['list'] as $item) {
                    $result[$i] = $item['keyword'];
                    $i++;
                }
                return $this->success($result);
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
