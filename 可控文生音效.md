# 可控文生音效

可控文生音效，支持通过输入提示词，生成一段可控时序的音效（**不支持输出背景音乐 BGM**）。用户可以基于时间轴，灵活控制多个事件音效的触发时长。

```
POST https://api.vidu.cn/ent/v2/timing2audio
```

## 请求头

| 字段          | 值                   | 描述                               |
| :------------ | :------------------- | :--------------------------------- |
| Content-Type  | application/json     | 数据交换格式                       |
| Authorization | Token {your api key} | 将 {your api key} 替换为您的 token |

## 请求体

| 参数名         | 类型           | 必填 | 描述                                                         |
| :------------- | :------------- | :--- | :----------------------------------------------------------- |
| model          | String         | 是   | 模型名称 可选值：audio1.0                                    |
| duration       | Float          | 可选 | 音频时长 默认 10，可选范围：2～10 秒内                       |
| timing_prompts | Array [object] | 是   | 可控音效参数。描述多个音效事件及各自时间区间，支持多个事件重叠。 - 单个事件最大提示词字符数：1500 字符 - from、to 必须在 [0, duration] 区间内 |
| seed           | Int            | 可选 | 随机种子 随机种子，若不传或传0则自动生成随机数，传固定值生成确定性结果 |
| callback_url   | String         | 可选 | Callback 协议 需要您在创建任务时主动设置 callback_url，请求方法为 POST，当视频生成任务有状态变化时，Vidu 将向此地址发送包含任务最新状态的回调请求。回调请求内容结构与查询任务API的返回体一致 回调返回的"status"包括以下状态： - processing 任务处理中 - success 任务完成（如发送失败，回调三次） - failed 任务失败（如发送失败，回调三次） |

```
curl -X POST -H "Authorization: Token {your_api_key}" -H "Content-Type: application/json" -d '
'{
  "model": "audio1.0",
  "duration": 10,
  "timing_prompts": [
    {
      "from": 0.0,
      "to": 3.0,
      "prompt": "清晨的鸟叫声"
    },
    {
      "from": 3.0,
      "to": 6.0,
      "prompt": "远处传来火车驶过的声音"
    },
    {
      "from": 5.0,
      "to": 9.5,
      "prompt": "海浪轻轻拍打沙滩"
    }
  ],
  "seed": 0
}' https://api.vidu.cn/ent/v2/timing2audio
```

## 响应体

| 字段           | 类型   | 描述                                                         |
| :------------- | :----- | :----------------------------------------------------------- |
| task_id        | String | Vidu 生成的任务ID                                            |
| state          | String | 处理状态 可选值： -created 创建成功 -queueing 任务排队中 -processing 任务处理中 -success 任务成功 -failed 任务失败 |
| model          | String | 本次调用的模型名称                                           |
| duration       | Int    | 本次调用的视频时长参数                                       |
| timing_prompts | Array  | 本次调用中定义的多个音频事件参数                             |
| seed           | Int    | 本次调用的随机种子参数                                       |
| created_at     | String | 任务创建时间                                                 |

```
{
  "task_id": "your_task_id_here",
  "state": "created",
  "model": "audio1.0",
  "duration": 10,
  "timing_prompts": [
    {
      "from": 0.0,
      "to": 3.0,
      "prompt": "清晨的鸟叫声"
    },
    {
      "from": 3.0,
      "to": 6.0,
      "prompt": "远处传来火车驶过的声音"
    },
    {
      "from": 5.0,
      "to": 9.5,
      "prompt": "海浪轻轻拍打沙滩"
    }
  ],
  "seed": 0,
  "created_at": "2025-01-01T10:00:00.000Z"
}
```