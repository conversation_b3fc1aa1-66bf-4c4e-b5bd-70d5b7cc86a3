<?php

namespace Modules\Socialite\Enums;

use App\Traits\EnumMethods;

enum MessageType: string
{
    use EnumMethods;

    case TEXT  = 'text';
    case IMAGE = 'image';
    case VOICE = 'voice';
    case VIDEO = 'video';
    case MUSIC = 'music';
    case NEWS  = 'news';

    const TYPE_MAP = [
        self::TEXT->value  => '文本消息',
        self::IMAGE->value => '图片消息',
        self::VOICE->value => '语音消息',
        self::VIDEO->value => '视频消息',
        self::MUSIC->value => '音乐消息',
        self::NEWS->value  => '图文消息',
    ];

    public function toString(): string
    {
        return self::TYPE_MAP[$this->value];
    }
}
