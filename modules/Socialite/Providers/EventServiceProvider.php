<?php

namespace Modules\Socialite\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Socialite\Events\WechatMediumCreatedEvent;
use Modules\Socialite\Events\WechatMediumDeletedEvent;
use Modules\Socialite\Listeners\WechatMediumCreatedListener;
use Modules\Socialite\Listeners\WechatMediumDeletedListener;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        WechatMediumCreatedEvent::class => [
            WechatMediumCreatedListener::class
        ],
        WechatMediumDeletedEvent::class => [
            WechatMediumDeletedListener::class
        ],
    ];

    protected $observers = [

    ];
}