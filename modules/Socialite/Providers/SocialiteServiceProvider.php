<?php

namespace Modules\Socialite\Providers;

use App\Models\Configuration;
use App\Models\User;
use EasyWeChat\MiniApp\Application as MiniAccount;
use EasyWeChat\OfficialAccount\Application as OfficialAccount;
use EasyWeChat\OpenPlatform\Application as OpenAccount;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;
use League\OAuth2\Client\Provider\Apple;
use Modules\Socialite\Exceptions\WechatAccountException;
use Modules\Socialite\Models\Alipay;
use Modules\Socialite\Models\Apple as UserApple;
use Modules\Socialite\Models\Wechat;

class SocialiteServiceProvider extends ServiceProvider
{
    protected string $moduleName = 'Socialite';

    protected string $moduleNameLower = 'socialite';

    public function boot(): void
    {
        User::resolveRelationUsing('wechat', fn(User $user) => $user->hasOne(Wechat::class));
        User::resolveRelationUsing('apple', fn(User $user) => $user->hasOne(UserApple::class));

        User::macro('getAppleName', function () {
            if (! $this->apple) {
                throw new WechatAccountException('用户未绑定苹果账号', 83011);
            }
            return $this->apple->name;
        });

        User::macro('getAppleEmail', function () {
            if (! $this->apple) {
                throw new WechatAccountException('用户未绑定苹果账号', 83011);
            }
            return $this->apple->email;
        });

        User::macro('getWechatOpenid', function () {
            return $this->wechat->wx_openid ?? '';
        });

        User::macro('getMiniOpenid', function () {
            return $this->wechat->mini_openid ?? '';
        });

        User::macro('getUnionId', function () {
            return $this->wechat->union_id ?? '';
        });

        User::resolveRelationUsing('alipay', fn(User $user) => $user->hasOne(Alipay::class));

        User::macro('getAlipayOpenid', function () {
            if (! $this->alipay) {
                throw new WechatAccountException('用户未绑定开放平台', 83021);
            }
            return $this->alipay->alipay_openid;
        });
    }

    public function register(): void
    {
        $this->registerConfig();
        $this->registerEasyWechat();
    }

    protected function registerConfig(): void
    {
        Configuration::registerModuleConfig($this->moduleName);

        Config::set('horizon.defaults.socialite', [
            'connection'          => 'redis',
            'queue'               => ['socialite'],
            'balance'             => 'auto',
            'autoScalingStrategy' => 'time',
            'maxProcesses'        => 1,
            'tries'               => 1,
        ]);

        Config::set('logging.channels.socialite', [
            'driver' => 'daily',
            'path'   => storage_path('logs/socialites/log.log'),
            'level'  => config('logging.channels.daily.level'),
            'days'   => 0,
        ]);
    }

    protected function registerEasyWechat(): void
    {
        $this->app->singleton('wechat.official_account', function (Application $laravelApp) {
            $account = [
                'app_id'  => config('socialite.WECHAT_APP_ID'),
                'secret'  => config('socialite.WECHAT_SECRET'),
                'token'   => config('socialite.WECHAT_TOKEN'),
                'aes_key' => config('socialite.WECHAT_AES_KEY'),
            ];
            $app     = new OfficialAccount($account);

            if (is_callable([$app, 'setCache'])) {
                $app->setCache($laravelApp['cache.store']);
            }

            if (is_callable([$app, 'setRequestFromSymfonyRequest'])) {
                $app->setRequestFromSymfonyRequest($laravelApp['request']);
            }

            return $app;
        });

        $this->app->singleton('wechat.mini', function (Application $laravelApp) {
            $account = [
                'app_id' => config('socialite.WECHAT_MINI_APP_ID'),
                'secret' => config('socialite.WECHAT_MINI_SECRET'),
            ];

            $app = new MiniAccount($account);

            if (is_callable([$app, 'setCache'])) {
                $app->setCache($laravelApp['cache.store']);
            }

            if (is_callable([$app, 'setRequestFromSymfonyRequest'])) {
                $app->setRequestFromSymfonyRequest($laravelApp['request']);
            }

            return $app;
        });

        $this->app->singleton('wechat.open_platform', function (Application $laravelApp) {
            $account = [
                'app_id'  => config('socialite.WECHAT_OPEN_APP_ID'),
                'secret'  => config('socialite.WECHAT_OPEN_SECRET'),
                'token'   => config('socialite.WECHAT_OPEN_TOKEN'),
                'aes_key' => config('socialite.WECHAT_OPEN_AES_KEY'),
            ];

            $app = new OpenAccount($account);

            if (is_callable([$app, 'setCache'])) {
                $app->setCache($laravelApp['cache.store']);
            }

            if (is_callable([$app, 'setRequestFromSymfonyRequest'])) {
                $app->setRequestFromSymfonyRequest($laravelApp['request']);
            }

            return $app;
        });

        $this->app->singleton('wechat.open_app', function (Application $laravelApp) {
            $account = [
                'app_id' => config('socialite.WECHAT_APP_ID'),
                'secret' => config('socialite.WECHAT_APP_SECRET'),
            ];

            $app = new OpenAccount($account);

            if (is_callable([$app, 'setCache'])) {
                $app->setCache($laravelApp['cache.store']);
            }

            if (is_callable([$app, 'setRequestFromSymfonyRequest'])) {
                $app->setRequestFromSymfonyRequest($laravelApp['request']);
            }

            return $app;
        });
//
//        $this->app->singleton('wechat.work', function (Application $laravelApp) {
//            $account = [
//                'corp_id' => config('socialite.WECHAT_WORK_CORP_ID'),
//                'secret'  => config('socialite.WECHAT_WORK_SECRET'),
//                'token'   => config('socialite.WECHAT_WORK_TOKEN'),
//                'aes_key' => config('socialite.WECHAT_WORK_AES_KEY'),
//            ];
//
//            $app = new WorkAccount($account);
//
//            if (is_callable([$app, 'setCache'])) {
//                $app->setCache($laravelApp['cache.store']);
//            }
//
//            if (is_callable([$app, 'setRequestFromSymfonyRequest'])) {
//                $app->setRequestFromSymfonyRequest($laravelApp['request']);
//            }
//
//            return $app;
//        });
//
//        $this->app->singleton('apple.provider', function (Application $laravelApp) {
//            $config = [
//                'clientId'    => config('socialite.APPLE_CLIENT_ID'),
//                'teamId'      => config('socialite.APPLE_TEAM_ID'),
//                'keyFileId'   => config('socialite.APPLE_KEY_FILE_ID'),
//                'keyFilePath' => Storage::disk('certs')->path(config('socialite.APPLE_KEY_FILE_PATH')),
//                'redirectUri' => config('socialite.APPLE_REDIRECT_URI'),
//            ];
//            $app    = new Apple($config);
//
//            if (is_callable([$app, 'setCache'])) {
//                $app->setCache($laravelApp['cache.store']);
//            }
//
//            if (is_callable([$app, 'setRequestFromSymfonyRequest'])) {
//                $app->setRequestFromSymfonyRequest($laravelApp['request']);
//            }
//
//            return $app;
//        });
    }

    public function provides(): array
    {
        return [
            'wechat.official_account',
            'wechat.mini',
            'wechat.open_platform',
            'wechat.work',
            'apple.provider',
        ];
    }
}
