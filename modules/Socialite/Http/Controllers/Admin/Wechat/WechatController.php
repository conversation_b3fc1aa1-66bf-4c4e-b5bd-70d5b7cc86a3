<?php

namespace Modules\Socialite\Http\Controllers\Admin\Wechat;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Widgets\Card;
use Modules\Socialite\Models\Wechat;

class WechatController extends AdminController
{
    protected string $title = '微信公众号';

    public function index(Content $content): Content
    {
        return $content->header($this->title)
            ->description('概览')
            ->body($this->dashboard());
    }

    protected function dashboard(): Row
    {
        $row = new Row();

        $row->column(6, function (Column $column) {
            $column->row(Card::make('回调地址', route('api.socialite.wechat.callback')));
        });

        return $row;
    }

    public function accounts(Content $content): Content
    {
        return $content->header($this->title)
            ->description('列表')
            ->body($this->grid());
    }

    protected function grid(): Grid
    {
        return Grid::make(Wechat::class, function (Grid $grid) {
            $grid->quickSearch(['user.username', 'user.info.nickname'])
                ->placeholder('用户名/用户昵称');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->like('wx_openid', '公众号ID');
                $filter->like('mini_openid', '小程序ID');
                $filter->like('union_id', '开放平台ID');
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->disableRowSelector();

            $grid->column('user_id');
            $grid->column('用户')
                ->display(fn() => $this->user->showName);
            $grid->column('wx_openid', '公众号ID')
                ->copyable();
            $grid->column('mini_openid', '小程序ID')
                ->copyable();
            $grid->column('union_id', '开放平台ID')
                ->copyable();
            $grid->column('created_at');
        });
    }
}