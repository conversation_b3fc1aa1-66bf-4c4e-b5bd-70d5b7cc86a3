<?php

namespace Modules\Socialite\Http\Controllers\Admin\Wechat;

use App\Admin\Traits\WithUploads;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Socialite\Enums\MediaType;
use Modules\Socialite\Http\Actions\DownloadMediumTool;
use Modules\Socialite\Http\Actions\PublishMediumTool;
use Modules\Socialite\Models\WechatMedium;
use RuntimeException;

class MediumController extends AdminController
{
    use WithUploads;

    protected string $title = '素材管理';

    public function grid(): Grid
    {
        return Grid::make(WechatMedium::latest(), static function (Grid $grid) {
            $grid->disableEditButton();

            $grid->quickSearch(['title', 'introduction'])
                ->placeholder('标题/描述');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('type', '素材类型')
                    ->select(MediaType::TYPE_MAP);
                $filter->like('title', '素材标题');
                $filter->like('introduction', '素材描述');
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->tools([new DownloadMediumTool, new PublishMediumTool]);

            $grid->column('id');
            $grid->column('ext.url')
                ->thumb(32);
            $grid->column('title', '素材标题')
                ->limit(20);
            $grid->column('introduction', '素材描述')
                ->limit(20);
            $grid->column('type', '素材类型')
                ->using(MediaType::TYPE_MAP)
                ->label();
            $grid->column('file', '素材文件')
                ->display(function () {
                    return $this->cover_url;
                })
                ->limit(20);
            $grid->column('media_id', 'MEDIA_ID')
                ->copyable();
            $grid->column('status', '上传状态')
                ->bool();
        });
    }

    public function form(): Form
    {
        return Form::make(WechatMedium::class, function (Form $form) {
            $form->text('title', '素材标题')
                ->required();
            $form->textarea('introduction', '素材描述');
            $form->radio('type')
                ->options(MediaType::TYPE_MAP)
                ->when(MediaType::IMAGE->value, function (Form $form) {
                    $map = $this->getMediumTypeMap(MediaType::IMAGE->value);
                    $this->cover($form, $map['f'], $map['n'])->help($map['d']);
                })
                ->when(MediaType::VOICE->value, function (Form $form) {
                    $map = $this->getMediumTypeMap(MediaType::VOICE->value);
                    $this->file($form, $map['f'], $map['n'])->help($map['d']);
                })
                ->when(MediaType::VIDEO->value, function (Form $form) {
                    $map = $this->getMediumTypeMap(MediaType::VIDEO->value);
                    $this->file($form, $map['f'], $map['n'])->help($map['d']);
                })
                ->when(MediaType::THUMB->value, function (Form $form) {
                    $map = $this->getMediumTypeMap(MediaType::THUMB->value);
                    $this->cover($form, $map['f'], $map['n'])->help($map['d']);
                })
                ->required();
            $form->hidden('file');

            $mediumExtName = $this->getMediumExtName();
            $form->saving(function (Form $form) use ($mediumExtName) {
                try {
                    $type = $form->input('type');
                    $file = $form->input($mediumExtName.'.'.$type);
                    $desc = $form->input('introduction');
                    $preg = match ($type) {
                        MediaType::IMAGE->value => "/^.+\.(bmp|png|jpeg|jpg|gif)$/",
                        MediaType::VOICE->value => "/^.+\.(mp3|wma|wav|amr)$/",
                        MediaType::VIDEO->value => "/^.+\.mp4$/",
                        MediaType::THUMB->value => "/^.+\.(jpeg|jpg)$/",
                        default => throw new RuntimeException('素材类型不正确'),
                    };
                    if (! in_array($type, MediaType::values(), true)) {
                        throw new RuntimeException('素材类型不正确');
                    }
                    if (! preg_match($preg, $file)) {
                        throw new RuntimeException('素材文件格式不正确');
                    }
                    if ($type === MediaType::VIDEO->value && empty($desc)) {
                        throw new RuntimeException('视频素材描述必须填写');
                    }
                    $form->deleteInput($mediumExtName);
                    $form->file = $file;
                } catch (Exception $exception) {
                    return $form->response()->error($exception->getMessage());
                }
            });
        });
    }

    private function getMediumExtName(): string
    {
        return 'ext';
    }

    private function getMediumTypeMap(string $type, string $key = 'array'): array|string
    {
        $map = [
            MediaType::IMAGE->value => [
                'f' => $this->getMediumExtName().'.'.MediaType::IMAGE->value,
                'n' => MediaType::IMAGE->toString().'素材',
                'd' => '<span style="color: red">图片（image）: 10MB以内，支持bmp/png/jpeg/jpg/gif格式</span>',
            ],
            MediaType::VOICE->value => [
                'f' => $this->getMediumExtName().'.'.MediaType::VOICE->value,
                'n' => MediaType::VOICE->toString().'素材',
                'd' => '<span style="color: red">语音（voice）：2MB以内，播放长度不超过60s，mp3/wma/wav/amr格式</span>',
            ],
            MediaType::VIDEO->value => [
                'f' => $this->getMediumExtName().'.'.MediaType::VIDEO->value,
                'n' => MediaType::VIDEO->toString().'素材',
                'd' => '<span style="color: red">视频（video）：10MB以内，支持MP4格式</span>',
            ],
            MediaType::THUMB->value => [
                'f' => $this->getMediumExtName().'.'.MediaType::THUMB->value,
                'n' => MediaType::THUMB->toString().'素材',
                'd' => '<span style="color: red">缩略图（thumb）：64KB以内，支持JPG格式</span>',
            ],
        ];
        if (! isset($map[$type])) {
            throw new RuntimeException('素材类型不正确');
        }

        return $map[$type];
    }

    public function ajax(Request $request): LengthAwarePaginator
    {
        $key  = $request->get('q');
        $type = $request->get('type');

        return WechatMedium::where('type', $type)
            ->where('status', 1)
            ->whereNotNull('media_id')
            ->where('title', 'like', "%$key%")
            ->paginate(null, ['id', 'title as text']);
    }
}