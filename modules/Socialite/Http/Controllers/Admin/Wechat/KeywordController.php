<?php

namespace Modules\Socialite\Http\Controllers\Admin\Wechat;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Socialite\Enums\MediaType;
use Modules\Socialite\Enums\MessageType;
use Modules\Socialite\Models\WechatKeyword;
use Modules\Socialite\Models\WechatMedium;

class KeywordController extends AdminController
{
    protected string $title = '关键词回复';

    public function grid(): Grid
    {
        return Grid::make(WechatKeyword::class, static function (Grid $grid) {
            $grid->quickSearch(['keyword'])
                ->placeholder('关键字');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('type', '回复类型')
                    ->select(MessageType::TYPE_MAP);
                $filter->like('keyword', '关键字');
                $filter->equal('fuzzy', '模糊匹配')
                    ->radio([
                        0 => '否',
                        1 => '是',
                    ]);
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->column('id');
            $grid->column('keyword', '关键字');
            $grid->column('fuzzy', '模糊匹配')
                ->bool();
            $grid->column('type', '回复类型')
                ->display(fn($type) => $type->toString());
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(WechatKeyword::class, static function (Form $form) {
            $form->text('keyword', '关键字')
                ->required()
                ->help('特殊关键字 【SUBSCRIBE】，将在关注的时候触发');
            $form->radio('fuzzy', '模糊匹配')
                ->options([
                    0 => '关闭',
                    1 => '开启',
                ])
                ->default(0);
            $form->radio('type')
                ->required()
                ->options(MessageType::TYPE_MAP)
                ->default(MessageType::TEXT->value)
                ->when(MessageType::TEXT->value, function (Form $form) {
                    $form->textarea('message.text.content', '消息内容');
                })
                ->when(MessageType::IMAGE->value, function (Form $form) {
                    $form->select('message.image.id', '素材')
                        ->options(function ($id) {
                            $medium = WechatMedium::find($id);
                            if ($medium) {
                                return [$id => $medium->title];
                            } else {
                                return [];
                            }
                        })
                        ->help('必填项，请选择要回复的公众号图片素材')
                        ->ajax(route('admin.socialite.wechat.media.ajax', ['type' => MessageType::IMAGE->value]));
                    $form->hidden('message.image.media_id');
                })
                ->when(MessageType::VOICE->value, function (Form $form) {
                    $form->select('message.voice.id', '素材')
                        ->options(function ($id) {
                            $medium = WechatMedium::find($id);
                            if ($medium) {
                                return [$id => $medium->title];
                            } else {
                                return [];
                            }
                        })
                        ->help('必填项，请选择要回复的公众号语音素材')
                        ->ajax(route('admin.socialite.wechat.media.ajax', ['type' => MessageType::VOICE->value]));
                    $form->hidden('message.voice.media_id');
                })
                ->when(MessageType::VIDEO->value, function (Form $form) {
                    $form->select('message.video.id', '素材')
                        ->options(function ($id) {
                            $medium = WechatMedium::find($id);
                            if ($medium) {
                                return [$id => $medium->title];
                            } else {
                                return [];
                            }
                        })
                        ->help('必填项，请选择要回复的公众号视频素材')
                        ->ajax(route('admin.socialite.wechat.media.ajax', ['type' => MessageType::VIDEO->value]));
                    $form->hidden('message.video.media_id');
                    $form->text('message.video.title', '标题')
                        ->help('非必填项，留空将自动获取素材对应标题');
                    $form->text('message.video.description', '描述')
                        ->help('非必填项，留空将自动获取素材对应描述');
                })
                ->when(MessageType::MUSIC->value, function (Form $form) {
                    $form->text('message.music.title', '音乐标题');
                    $form->text('message.music.description', '音乐描述');
                    $form->url('message.music.music_url', '音乐链接');
                    $form->url('message.music.hq_music_url', '高质量音乐链接');
                    $form->text('message.music.thumb_media_id', '缩略图素材ID');
                })
                ->when(MessageType::NEWS->value, function (Form $form) {
                    $form->table('message.news', '图文消息', function ($form) {
                        $form->text('title', '图文标题');
                        $form->text('description', '图文描述');
                        $form->url('pic_url', '图片链接');
                        $form->url('url', '跳转链接');
                    });
                });

            $form->saving(function (Form $form) {
                $type    = $form->input('type');
                $message = $form->message ?? [];
                if (in_array($type, MediaType::values(), true)) {
                    if (empty($message[$type]['id'])) {
                        return $form->response()->error('请选择素材');
                    }
                    $medium = WechatMedium::find($message[$type]['id']);
                    if (empty($medium)) {
                        return $form->response()->error('请选的素材不存在');
                    }
                    $message[$type]['id']       = $medium->id;
                    $message[$type]['media_id'] = $medium->media_id;
                    if ($type === MediaType::VIDEO->value) {
                        $message[$type]['title']       = $message[$type]['title'] ?: $medium->title;
                        $message[$type]['description'] = $message[$type]['description'] ?: $medium->introduction;
                    }
                    $form->message = $message;
                }
            });
        });
    }
}