<?php

namespace Modules\Socialite\Http\Controllers\Admin\Alipay;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Socialite\Models\Alipay;

class AlipayController extends AdminController
{
    protected string $title = '支付宝';

    public function grid(): Grid
    {
        return Grid::make(Alipay::class, function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->disableRowSelector();

            $grid->column('user_id');
            $grid->column('用户')
                ->display(fn() => $this->user->showName);
            $grid->column('openid', '开放平台ID');
            $grid->column('created_at');
        });
    }
}