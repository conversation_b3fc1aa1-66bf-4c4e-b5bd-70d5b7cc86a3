<?php

namespace Modules\Socialite\Http\Controllers\Admin\Qq;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Socialite\Models\Qq;

class QqController extends AdminController
{
    protected string $title = 'QQ';

    public function grid(): Grid
    {
        return Grid::make(Qq::class, function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->disableRowSelector();

            $grid->column('user_id');
            $grid->column('用户')
                ->display(fn() => $this->user->showName);
            $grid->column('openid', '开放平台ID');
            $grid->column('created_at');
        });
    }
}