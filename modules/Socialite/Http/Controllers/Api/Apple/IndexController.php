<?php

namespace Modules\Socialite\Http\Controllers\Api\Apple;

use App\Models\User;
use Firebase\JWT\JWT;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use League\OAuth2\Client\Token\AccessToken;
use Modules\Socialite\Http\Controllers\Api\Controller;
use Modules\Socialite\Http\Requests\AppleBindMobileRequest;
use Modules\Socialite\Models\Apple;

class IndexController extends Controller
{
    public function oauth()
    {
        return $this->success();
    }

    public function code(Request $request)
    {
        if (blank($request->code)) {
            return $this->failed('请传入CODE');
        }

        try {
            JWT::$leeway = 60;
            $app         = app('apple.provider');
            $token       = $app->getAccessToken('authorization_code', [
                'code' => $request->code
            ]);
            Log::channel('socialite')->info('Access Token: '.json_encode($token));
            $user = $app->getResourceOwner($token);
            $data = $user->toArray();

            Log::channel('socialite')->info('User Info: '.json_encode($data));

            $sub   = $data['sub'];
            $email = $user->getEmail();

            $apple = Apple::where('sub', $sub)->first();
            if (! $apple) {
                $apple = Apple::create([
                    'sub'   => $sub,
                    'email' => $email
                ]);
            }

            $data = [
                'need_mobile' => true,
                'token'       => '',
                'is_new'      => true,
                'type'        => 'Bearer',
                'sub'         => $sub,
            ];
            if ($apple->user_id > 0) {
                $user                = $apple->user;
                $token               = $this->loginUser($user);
                $data['need_mobile'] = false;
                $data['token']       = $token->plainTextToken;
                $data['is_new']      = false;
            }

            return $this->success($data);
        } catch (\League\OAuth2\Client\Provider\Exception\IdentityProviderException $exception) {
            // 处理 invalid_grant 错误
            if ($exception->getMessage() === 'invalid_grant') {
                return $this->failed('授权码已过期或已被撤销，请重新进行授权。');
            }

            // 记录其他异常信息
            Log::channel('socialite')->error('Apple Login Error: '.$exception->getMessage());
            return $this->failed($exception->getMessage());
        } catch (\Exception $exception) {
            Log::channel('socialite')->error('Apple Login Error: '.$exception->getMessage());
            return $this->failed($exception->getMessage());
        }
    }

    /**
     * Notes: 绑定手机号
     *
     * @Author: 玄尘
     * @Date: 2024/6/26 上午11:46
     * @param  \Modules\Socialite\Http\Requests\AppleBindMobileRequest  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function bindMobile(AppleBindMobileRequest $request)
    {
        $username = $request->username;
        $sub      = $request->sub;

        $user  = User::where('username', $username)->first();
        $apple = Apple::where('sub', $sub)->first();
        if (! $apple) {
            return $this->failed('未找到苹果登录信息，请重试！');
        }
        $isNew = true;
        if ($user) {
            $isNew = false;

            if ($user->apple) {
                if ($user->apple->sub) {
                    return $this->failed('该用户已经绑定苹果账号');
                } elseif ($user->apple->id != $apple->id) {
                    $user->apple->delete();
                    $apple->user_id = $user->id;
                    $apple->save();
                }
            } else {
                $apple->user_id = $user->id;
                $apple->save();
            }
        } else {
            $user = User::create([
                'username'    => $username,
                'apple_model' => $apple,
            ]);
        }
        $token = $this->loginUser($user);
        return $this->success([
            'token'  => $token->plainTextToken,
            'is_new' => $isNew,
            'type'   => 'Bearer',
        ]);
    }
}