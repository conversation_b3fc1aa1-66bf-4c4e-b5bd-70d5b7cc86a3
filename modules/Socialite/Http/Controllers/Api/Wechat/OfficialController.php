<?php

namespace Modules\Socialite\Http\Controllers\Api\Wechat;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Barryvdh\Debugbar\Facades\Debugbar;
use Closure;
use EasyWeChat\OfficialAccount\Message;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Socialite\Http\Controllers\Api\Wechat\Handles\EventMessage;
use Modules\Socialite\Http\Controllers\Api\Wechat\Handles\SubscribeMessage;
use Modules\Socialite\Http\Controllers\Api\Wechat\Handles\TextMessage;
use Modules\Socialite\Http\Requests\ShareRequest;
use Modules\Socialite\Http\Requests\WechatConfigRequest;
use Modules\Socialite\Models\WechatMessage;

class OfficialController extends ApiController
{
    public function index(Request $request)
    {
        if (class_exists('Barryvdh\Debugbar\Facades\Debugbar')) {
            Debugbar::disable();
        }
        
        $server = app('wechat.official_account')->getServer();

        $server->with(function (Message $message, Closure $next) {
            WechatMessage::create([
                'message' => $message,
            ]);

            return $next($message);
        });

        $server->addEventListener('CLICK', function (Message $message) {
            return new EventMessage($message);
        });
        $server->addEventListener('subscribe', function (Message $message) {
            return new SubscribeMessage($message);
        });

        $server->addMessageListener('text', function (Message $message) {
            return new TextMessage($message);
        });
        $server->addMessageListener('image', function (Message $message) {
            return 'image';
        });
        $server->addMessageListener('voice', function (Message $message) {
            return 'voice';
        });
        $server->addMessageListener('video', function (Message $message) {
            return 'video';
        });
        $server->addMessageListener('shortvideo', function (Message $message) {
            return 'shortvideo';
        });
        $server->addMessageListener('location', function (Message $message) {
            return 'location';
        });
        $server->addMessageListener('link', function (Message $message) {
            return 'link';
        });
        $server->addMessageListener('file', function (Message $message) {
            return '文件';
        });

        return $server->serve();
    }

    public function config(WechatConfigRequest $request)
    {
        $app    = app('wechat.official_account');
        $config = $app->getUtils()->buildJsSdkConfig(
            url: $request->safe()->offsetGet('page_url'),
            jsApiList: $request->safe()->offsetGet('js_api_list'),
            openTagList: [],
            debug: false,
        );

        return $this->success($config);
    }

    public function share(ShareRequest $request): JsonResponse
    {
        $user = Api::user();
        return $this->success([
            'title'  => $user->info?->nickname.config('socialite.WECHAT_SHARE_TITLE'),
            'desc'   => config('socialite.WECHAT_SHARE_SUB_TITLE'),
            'link'   => $request->safe()->offsetGet('link'),
            'imgUrl' => config('socialite.WECHAT_SHARE_IMG_URL'),
        ]);
    }
}