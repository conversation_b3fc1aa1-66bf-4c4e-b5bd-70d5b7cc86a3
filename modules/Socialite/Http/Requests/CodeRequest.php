<?php

namespace Modules\Socialite\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class CodeRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'code'  => 'required',
            'scope' => 'required|bail|in:snsapi_base,snsapi_userinfo',
        ];
    }

    public function messages(): array
    {
        return [
            'code.required'  => 'CODE缺失',
            'scope.required' => '作用域缺失',
            'scope.in'       => '作用域不正确',
        ];
    }
}
