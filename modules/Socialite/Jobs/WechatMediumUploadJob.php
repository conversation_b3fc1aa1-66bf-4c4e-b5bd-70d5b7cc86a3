<?php

namespace Modules\Socialite\Jobs;

use EasyWeChat\Kernel\Form\File;
use EasyWeChat\Kernel\Form\Form;
use Illuminate\Support\Facades\Storage;
use Modules\Socialite\Enums\MediaType;
use Modules\Socialite\Models\WechatMedium;
use RuntimeException;

class WechatMediumUploadJob extends SocialiteBaseJob
{
    public function __construct(protected WechatMedium $medium)
    {
    }

    /**
     * @throws \JsonException
     */
    public function handle(): void
    {
        $this->pushMedium($this->medium);
    }

    /**
     * Notes    : 素材上传
     *
     * <AUTHOR> 丁明远
     * @Date    : 2023/10/20 0020 上午 11:24
     * @param  \Modules\Socialite\Models\WechatMedium  $medium
     * @throws \JsonException
     */
    public function pushMedium(WechatMedium $medium): void
    {
        if (! empty($medium->status)) {
            return;
        }
        $type  = $medium->type ?? '';
        $file  = $medium->file ?? '';
        $title = $medium->title ?? '';
        $desc  = $medium->introduction ?? '';
        if (empty($file)) {
            throw new RuntimeException('未上传素材文件');
        }
        $preg = match ($type) {
            MediaType::IMAGE->value => "/^.+\.(bmp|png|jpeg|jpg|gif)$/",
            MediaType::VOICE->value => "/^.+\.(mp3|wma|wav|amr)$/",
            MediaType::VIDEO->value => "/^.+\.mp4$/",
            MediaType::THUMB->value => "/^.+\.(jpeg|jpg)$/",
            default => throw new RuntimeException('素材类型不正确'),
        };
        if (! preg_match($preg, $file)) {
            throw new RuntimeException('素材文件格式不正确');
        }
        if (! Storage::exists($file)) {
            throw new RuntimeException('素材文件不存在');
        }
        if ($type === MediaType::VIDEO->value && (empty($title) || empty($desc))) {
            throw new RuntimeException('视频素材缺少标题或描述');
        }

        $description = ['title' => $title, 'introduction' => $desc];
        $options     = Form::create(
            [
                'type'        => $type,
                'media'       => File::fromPath($medium->cover_url ?? ''),
                'description' => json_encode($description, JSON_THROW_ON_ERROR),
            ]
        )->toArray();
        $result      = app('wechat.official_account')->getClient()->post('cgi-bin/material/add_material', $options);
        $response    = json_decode($result->getContent(), true, 512, JSON_THROW_ON_ERROR);
        if (is_array($response)) {
            $medium->ext = $response;
            $medium->save();
        }
        if (! empty($response['errcode'])) {
            throw new RuntimeException($response['errmsg'] ?? '', $response['errcode']);
        }
        if (! empty($response['media_id'])) {
            $medium->media_id = $response['media_id'];
            $medium->status   = 1;
            $medium->save();
        }
    }
}