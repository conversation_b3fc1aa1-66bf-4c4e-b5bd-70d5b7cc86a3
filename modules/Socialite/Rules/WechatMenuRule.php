<?php

namespace Modules\Socialite\Rules;

use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class WechatMenuRule implements ValidationRule, DataAwareRule
{
    protected array $data;

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $parentId = $this->data['parent_id'];
        if ($parentId == '0' && mb_strwidth($value) > 12) {
            $fail('一级菜单最多4个汉字');
        } elseif ($parentId != '0' && mb_strwidth($value) > 16) {
            $fail('二级菜单最多8个汉字');
        }
    }

    public function setData(array $data): void
    {
        $this->data = $data;
    }
}