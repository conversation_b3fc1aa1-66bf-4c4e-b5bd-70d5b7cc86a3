<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('socialite_apples', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->default(0)->comment('用户id');
            $table->string('name')->nullable()->comment('AppleName');
            $table->string('email')->nullable()->comment('AppleEMail');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('socialite_apples');
    }
};
