<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Socialite\Enums\MessageType;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('socialite_wechat_keywords', function (Blueprint $table) {
            $table->id();
            $table->string('keyword');
            $table->boolean('fuzzy');
            $table->enum('type', MessageType::values());
            $table->json('message');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('socialite_wechat_keywords');
    }
};
