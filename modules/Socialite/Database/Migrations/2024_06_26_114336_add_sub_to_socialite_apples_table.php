<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('socialite_apples', function (Blueprint $table) {
            $table->string('sub')->comment('AppleSub')->nullable()->after('user_id');
        });
    }

    public function down(): void
    {
        Schema::table('socialite_apples', function (Blueprint $table) {
            $table->dropColumn(['sub']);
        });
    }
};
