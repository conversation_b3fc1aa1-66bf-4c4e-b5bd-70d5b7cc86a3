<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('socialite_tiktoks', function (Blueprint $table) {
            $table->id();
            $table->user()
                ->default(0);
            $table->string('openid', 64)
                ->unique();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('socialite_tiktoks');
    }
};
