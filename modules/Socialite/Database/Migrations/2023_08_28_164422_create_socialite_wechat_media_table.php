<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Socialite\Enums\MediaType;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('socialite_wechat_media', static function (Blueprint $table) {
            $table->id();
            $table->enum('type', MediaType::values())
                ->default(MediaType::IMAGE)
                ->comment('素材类型');
            $table->string('title')
                ->nullable()
                ->comment('素材标题');
            $table->string('introduction')
                ->nullable()
                ->comment('素材描述');
            $table->string('file')
                ->nullable()
                ->comment('素材文件');
            $table->string('media_id')
                ->nullable()
                ->comment('素材id');
            $table->boolean('status')
                ->default(0)
                ->comment('发布状态');
            $table->boolean('ext')
                ->nullable()
                ->comment('扩展字段');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('socialite_wechat_media');
    }
};
