<?php

namespace Modules\Company\Http\Forms;

use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Modules\Company\Models\Staff;

class StaffAuditForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        try {
            $staff  = Staff::find($this->payload['staff_id']);
            $status = $input['status'];
            $reason = $input['reject_reason'];
            if ($status == Staff::STATUS_APPROVED) {
                $staff->agree();
            }
            if ($status == Staff::STATUS_REJECTED) {
                $staff->reject($reason);
            }
            return $this->response()->success('审核成功')->refresh();
        } catch (\Exception $exception) {
            return $this->response()->error($exception->getMessage())->refresh();
        }
    }

    public function form(): void
    {
        $staff = Staff::find($this->payload['staff_id']);
        $this->text('name', '姓名')->value($staff->name)->readonly();
        $this->text('mobile', '手机号')->value($staff->mobile)->readOnly();
        $this->text('position', '职位')->value($staff->position)->readOnly();
        $this->text('type', '来源')->value($staff->type_name)->readOnly();
        $this->select('status', '状态')
            ->options([
                Staff::STATUS_APPROVED => '通过',
                Staff::STATUS_REJECTED => '驳回',
            ])
            ->when(Staff::STATUS_REJECTED, function (Form $form) {
                $form->text('reject_reason', '拒绝原因');
            })
            ->required();
    }
}