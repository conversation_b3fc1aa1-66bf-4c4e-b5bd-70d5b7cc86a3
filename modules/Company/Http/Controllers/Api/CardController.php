<?php

namespace Modules\Company\Http\Controllers\Api;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Company\Http\Resources\Api\Card\CardCollection;
use Modules\Company\Http\Resources\Api\Contact\BusinessContactCollection;
use Modules\Company\Models\Business;
use Modules\Company\Models\BusinessBrowseRecord;
use Modules\Company\Models\BusinessContact;
use Modules\Company\Models\Industry;
use Modules\Company\Traits\BusinessTrait;
use Modules\Company\Traits\CardTrait;

class CardController extends ApiController
{
    use CardTrait, BusinessTrait;

    public function index(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'company_id' => ['nullable', 'integer', 'exists:jz_companies,id'],
            'type'       => ['nullable', 'integer'],
        ], [
            'company_id.required_if' => '企业id为4时必添',
            'company_id.integer'     => '企业id必须是数字',
            'company_id.exists'      => '企业不存在',
            'type.integer'           => '类型只能是数字'
        ]);

        $user = $request->kernel->user();

        // 获取分页和过滤条件
        $page      = $request->page ?? 1;
        $pageSize  = $request->pagesize ?? 10;
        $keyword   = $request->keyword ?? '';
        $type      = $request->type ?? 0;
        $companyId = $request->company_id ?? 0;

        // 初始化业务ID数组
        $companyBusinessIds = $personalBusinessIds = [];

        // 根据不同的type获取对应的业务ID
        switch ($type) {
            case 1:
                // 个人人脉
                $businessIds = $personalBusinessIds = $this->findFriendBusinessIds($user->id);
                break;

            case 2:
                // 企业人脉
                if ($companyId) {
                    $businessIds = $this->getCompanySpecificBusinessIds($companyId);
                } else {
                    $companyBusinessIds = $businessIds = $this->getCompanyBusinessIds($user);
                }
                break;

            default:
                // 混合类型
                $data                = $this->getMixedBusinessIds($user);
                $businessIds         = $data['all'];
                $companyBusinessIds  = $data['company'];
                $personalBusinessIds = $data['personal'];
                break;
        }

        // 构建业务查询
        $businessQuery = Business::whereIn('id', $businessIds)
            ->with(['industry'])
            ->when($keyword, function ($query) use ($keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('nickname', 'like', "%{$keyword}%")
                        ->orWhere('company_name', 'like', "%{$keyword}%")
                        ->orWhere('phone', 'like', "%{$keyword}%")
                        ->orWhere('position', 'like', "%{$keyword}%");
                });
            })
            ->latest();

        // 获取分页数据
        $businesses = $businessQuery->paginate($pageSize);
        return $request->kernel->success(new CardCollection($businesses, $personalBusinessIds));
    }

    /**
     * Notes: 名片交换请求
     *
     * @Author: 玄尘
     * @Date: 2025/4/10 13:48
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function exchange(Request $request)
    {
        $keyword  = $request->keyword ?? '';
        $page     = $request->page ?? 1;
        $pageSize = $request->pagesize ?? 10;

        $userId = $request->kernel->id();

        $contacts = BusinessContact::where('is_initiator', 1)
            ->with(['business', 'fromBusiness'])
            ->where('destination_id', $userId)
            ->where('status', 0)
            ->when($keyword, function ($query) use ($keyword) {
                $query->whereHas('fromBusiness', function ($query) use ($keyword) {
                    $query->where('nickname', 'like', '%'.$keyword.'%')
                        ->whereOr('company_name', 'like', '%'.$keyword.'%')
                        ->whereOr('position', 'like', '%'.$keyword.'%');
                });
            })
            ->latest()
            ->paginate($pageSize);

        return $request->kernel->success(new BusinessContactCollection($contacts));
    }

    public function rank(Request $request)
    {
        $page        = $request->page ?? 1;
        $pageSize    = $request->pagesize ?? 10;
        $keyword     = $request->keyword ?? '';
        $province    = $request->province ?? '';
        $city        = $request->city ?? '';
        $area        = $request->area ?? '';
        $industry    = $request->industry ?? '';
        $is_position = $request->is_position ?? '';
        $is_phone    = $request->is_phone ?? '';
        $is_wechat   = $request->is_wechat ?? '';
        $is_email    = $request->is_email ?? '';
        $is_company  = $request->is_company ?? '';
        $is_business = $request->is_business ?? '';

        $user = $request->kernel->user();

        $position = "";
        if ($is_position == 1) {
            $position = $user->jzBusinesses()
                ->where('is_default', 1)
                ->value('position');
        }

        $businessQuery = DB::table((new Business())->getTable().' as b')
            ->leftJoin('users as u', 'b.user_id', '=', 'u.id')
            ->leftJoin((new Industry())->getTable().' as i', 'b.industry_id', '=', 'i.id')
            ->leftJoin('interaction_likes as l', 'b.id', '=', 'l.likeable_id')
            ->leftJoin((new BusinessBrowseRecord())->getTable().' as r', 'b.id', '=', 'r.business_id')
            ->when($is_email, fn($query) => $query->whereNotNull('b.email'))
            ->when($is_phone, fn($query) => $query->whereNotNull('b.phone'))
            ->when($is_wechat, fn($query) => $query->whereNotNull('b.wechat'))
            ->when($is_company, fn($query) => $query->whereNotNull('b.company'))
            ->when($is_business, fn($query) => $query->whereNotNull('b.business'))
            ->when($position, fn($query) => $query->orWhere('b.position', 'like', "%$position%"))
            ->when($province, fn($query) => $query->where('b.province', $province))
            ->when($city, fn($query) => $query->where('b.city', $city))
            ->when($area, fn($query) => $query->where('b.area', $area))
            ->when($industry, fn($query) => $query->where('i.title', 'like', "%$industry%"))
            ->when($keyword, fn($query) => $query->where(function ($q) use ($keyword) {
                $q->where('b.nickname', 'like', "%$keyword%")
                    ->orWhere('b.company_name', 'like', "%$keyword%")
                    ->orWhere('b.phone', 'like', "%$keyword%")
                    ->orWhere('b.position', 'like', "%$keyword%");
            }))
            ->whereNull('b.deleted_at')
            ->selectRaw("b.id, b.user_id, b.position, b.company_name, b.avatar, b.nickname, 
                     (IFNULL(SUM(r.count) * 1, 0) + IFNULL(COUNT(l.id) * 5, 0)) as total_order_count")
            ->groupBy('b.id')
            ->orderByDesc('total_order_count');

        $businesses = $businessQuery->skip(($page - 1) * $pageSize)->take($pageSize)->get();
        $count      = $businesses->count();

        $default  = $user->jzBusinesses()->where('is_default', 1)->first();
        $nickname = $default->nickname ?? '';
        $avatar   = $default->avatar ?? '';

        $fieldList = $businesses->map(function ($business, $index) use ($page, &$rank, $pageSize, $user) {
            $num = ($page - 1) * $pageSize + $index + 1;

            if ($business->user_id == $user->id && $rank == 0) {
                $rank = $num;
            }

            return [
                'num'               => $num,
                'business_id'       => $business->id,
                'nickname'          => $business->nickname,
                'avatar'            => $business->avatar,
                'position'          => $business->position,
                'company_name'      => $business->company_name,
                'total_order_count' => $business->total_order_count
            ];
        });

        $data = [
            'rank'         => $rank ?? 0,
            'nickname'     => $nickname,
            'avatar'       => $avatar,
            'total'        => $count,
            'per_page'     => $pageSize,
            'current_page' => $page,
            'last_page'    => ceil($count / $pageSize),
            'data'         => $fieldList,
        ];

        return $request->kernel->success($data);
    }

    /**
     * Notes: 同意交换
     *
     * @Author: 玄尘
     * @Date: 2025/4/10 13:37
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function agree(Request $request)
    {
        try {
            $request->kernel->validate([
                'business_contact_id' => 'required|integer|exists:jz_company_business_contacts,id',
            ], [
                'business_contact_id.required' => '缺少交换请求id',
                'business_contact_id.integer'  => '交换请求id必须是数字',
                'business_contact_id.exists'   => '交换信息不存在',
            ]);

            $user                = $request->kernel->user();
            $business_contact_id = $request->business_contact_id;
            $contact             = BusinessContact::find($business_contact_id);

            if (! $contact->fromBusiness) {
                throw new ValidatorException("要交换的名片不存在");
            }

            if (! $contact->business) {
                throw new ValidatorException("交换请求不存在");
            }
            $toBusiness   = $contact->business;
            $fromBusiness = $contact->fromBusiness;

            $this->checkBusiness($contact->business, $user, '要交换的名片不存在', '您没有权限操作');

            $contact->update(['status' => 1]);

            BusinessContact::create([
                'user_id'          => $toBusiness->user_id,
                'from_business_id' => $toBusiness->id,
                'to_business_id'   => $fromBusiness->id,
                'destination_id'   => $fromBusiness->user_id,
                'status'           => 1,
                'is_initiator'     => 0,
            ]);

            return $request->kernel->success('操作成功');
        } catch (\Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

}
