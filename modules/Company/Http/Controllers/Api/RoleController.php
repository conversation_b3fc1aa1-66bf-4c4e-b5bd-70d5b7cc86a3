<?php

namespace Modules\Company\Http\Controllers\Api;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Company\Models\DepartmentRole;
use Modules\Company\Models\Role;
use Modules\Company\Models\Staff;
use Modules\User\Models\Department;

class RoleController extends ApiController
{
    public function index(Request $request): JsonResponse
    {
        $user       = $request->kernel->user();
        $company_id = $request->company_id;
        $role_id    = '';
        if ($company_id) {
            $role_id = DepartmentRole::query()
                ->when($company_id, function ($query) use ($company_id, $user) {
                    return $query->where('company_id', $company_id)->where('user_id', $user->id);
                })
                ->oldest('company_role_id')
                ->value('company_role_id');
        }
        $roles = Role::query()
            ->when($role_id, function ($query) use ($role_id) {
                $query->where('id', '>', $role_id);
            })
            ->where('label', "<>", Role::LABEL_ADMINISTRATOR)
            ->oldest()
            ->select('id', 'name')
            ->get();

        return $request->kernel->success($roles);
    }

    /**
     * Notes: 添加职位
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 10:31
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ValidatorException
     */
    public function join(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'staff_id'      => 'required|exists:Modules\Company\Models\Staff,id|integer',
            'department_id' => 'nullable|exists:user_departments,id|integer',
            'role_id'       => 'required|exists:Modules\Company\Models\Role,id|integer',
        ], [
            'staff_id.required'      => '缺少员工id',
            'staff_id.exists'        => '员工不存在',
            'staff_id.integer'       => '员工id只能是数字',
            'department_id.required' => '缺少部门id',
            'department_id.exists'   => '部门不存在',
            'department_id.integer'  => '部门id只能是数字',
            'role_id.required'       => '缺少职位id',
            'role_id.exists'         => '职位不存在',
            'role_id.integer'        => '职位id只能是数字',
        ]);

        $user = $request->kernel->user();

        $staff_id      = $request->staff_id;
        $role_id       = $request->role_id;
        $department_id = $request->department_id;
        $staff         = Staff::find($staff_id);
        $role          = Role::find($role_id);
        $department    = '';
        if ($department_id) {
            $department = Department::find($department_id);
            if (! $department) {
                throw new ValidatorException('部门不存在');
            }
        }

        if (! $user->isManagementRole($staff->company_id)) {
            throw new ValidatorException('您没有权限');
        }
        $role->canJoin($user, $staff, $department);

        $staff->user->companyDepartmentRoles()
            ->updateOrcreate([
                'company_staff_id' => $staff->id,
                'company_id'       => $staff->company_id,
                'department_id'    => $role->getDepartmentId($department_id),
                'company_role_id'  => $role_id,
            ]);
        return $request->kernel->success('分配成功');
    }

    /**
     * Notes: 删除用户职位
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 11:13
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ValidatorException
     */
    public function leave(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'staff_id' => 'required|Modules\Company\Models\Staff,id|integer',
            //            'department_id' => 'required|exists:user_departments,id|integer',
            'role_id'  => 'required|exists:Modules\Company\Models\Role,id|integer',
        ], [
            'staff_id.required'      => '缺少员工id',
            'staff_id.exists'        => '员工不存在',
            'staff_id.integer'       => '员工id只能是数字',
            'department_id.required' => '缺少部门id',
            'department_id.exists'   => '部门不存在',
            'department_id.integer'  => '部门id只能是数字',
            'role_id.required'       => '缺少职位id',
            'role_id.exists'         => '职位不存在',
            'role_id.integer'        => '职位id只能是数字',
        ]);

        $user = $request->kernel->user();

        $staff_id      = $request->staff_id;
        $department_id = $request->department_id;
        $role_id       = $request->role_id;

        $department = Department::find($department_id);

        if (! $user->isManagementRole($department->company_id)) {
            throw new ValidatorException('您没有权限');
        }

        $staff = Staff::find($staff_id);
        $role  = Role::find($role_id);
        $role->canLeave($user, $staff);

        $departmentId = $role->getDepartmentId($department_id);

        $info = $staff->user->companyDepartmentRoles()
            ->where('department_id', $departmentId)
            ->where('company_role_id', $role_id)
            ->first();
        if (! $info) {
            throw new ValidatorException('此人没有此权限');
        }

        $info->delete();

        return $request->kernel->success('移除成功');
    }

}