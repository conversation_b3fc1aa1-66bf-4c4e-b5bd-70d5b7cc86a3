<?php

namespace Modules\Company\Http\Controllers\Api;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Modules\Company\Http\Requests\CompanyAuthRequest;
use Modules\Company\Http\Requests\CompanyUpdateRequest;
use Modules\Company\Http\Requests\WorkAuthRequest;
use Modules\Company\Http\Resources\Api\Company\CompanyBaseResource;
use Modules\Company\Http\Resources\Api\Company\CompanyResource;
use Modules\Company\Models\Business;
use Modules\Company\Models\Certification;
use Modules\Company\Models\Company;
use Modules\Company\Models\DepartmentRole;
use Modules\Company\Models\Role;
use Modules\Company\Models\Staff;
use Modules\Company\Traits\CompanyTraits;

class CompanyController extends ApiController
{
    use CompanyTraits;

    /**
     * Notes: 我加入/管理的公司
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 10:32
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function companies(Request $request)
    {
        $user = $request->kernel->user();

        $companies = Company::query()
            ->whereHas('staffs', fn($query) => $query->where('user_id', $user->id))
            ->where('status', Company::STATUS_PASS)
            ->get();
        return $request->kernel->success(CompanyBaseResource::collection($companies));
    }

    /**
     * Notes: 待认证企业
     *
     * @Author: 玄尘
     * @Date: 2024/12/27 09:57
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function unverifiedCompany(Request $request)
    {
        $user = $request->kernel->user();

        //检测是否实名认证
        $isRealname = $user->realName()->where('is_check', 1)->exists();

        $businesses = $user->jzBusinesses()
            ->whereNull('company_id')
            ->oldest()
            ->get();

        // 使用集合方法优化处理逻辑
        $uniqueBusinesses = $businesses
            ->unique('company_name')
            ->map(function ($business) use ($user, $isRealname) {
                return [
                    'id'           => $business->id,
                    'company_id'   => $business->company_id,
                    'company_name' => $business->company_name,
                    'company_icon' => $business->company_icon,
                    'is_realname'  => $isRealname ? 1 : 0,
                ];
            })
            ->values();
        return $request->kernel->success($uniqueBusinesses);
    }

    public function authentication(Request $request)
    {
        $companyRequest = new CompanyAuthRequest();

        $request->kernel->validate($companyRequest->rules(), $companyRequest->messages());

        $user = $request->kernel->user();

        $enterpriseName = $request->enterpriseName;
        $enterpriseNo   = $request->enterpriseNo;
        $legalName      = $request->legalName;
        $legalIdCard    = $request->legalIdCard;
        $phone          = $request->phone;
        $logo           = $request->logo;
        $business_id    = $request->business_id;
        $license        = $request->license ?? '';

        //检测名片是否存在
        $business = $this->checkBusinessByCreateCompany($business_id, $user);

        //检测用户是否实名
        $realname = $this->checkRealname($user);

        $company = Company::query()
            ->with(['certification'])
            ->whereHas('certification', function ($query) use ($enterpriseNo) {
                $query->where('code', $enterpriseNo);
            })
            ->first();

        try {
            DB::beginTransaction();

            if ($company) {
                if ($company->certification->status == 0) {
                    throw new ValidatorException("企业认证正在审核中");
                }

                if ($company->certification->status == 1) {
                    throw new ValidatorException("该企业已经认证过");
                }
                $companyData = [
                    'user_id' => $user->id,
                    'name'    => $enterpriseName,
                ];
                if ($logo) {
                    $companyData['logo'] = $logo;
                }
                $company->update($companyData);
                $certificationData = [
                    'name'    => $legalName,
                    'id_card' => $legalIdCard,
                    'code'    => $enterpriseNo,
                ];
                if ($license) {
                    $certificationData['license'] = $license;
                }
                $company->certification->update($certificationData);
                DB::commit();

                return $request->kernel->success(true);
            }

            //先检测三要素
            $this->checkUserCertification($user, $legalIdCard, $phone, $legalName);
            //进行企业认证
            $companyData = Company::doCertification($enterpriseName, $legalName, $enterpriseNo);
            $company     = Company::create([
                'user_id' => $user->id,
                'name'    => $enterpriseName,
                'status'  => Company::STATUS_PASS,
            ]);

            $company->info()->create();
            $company->certification()->create([
                'name'    => $legalName,
                'id_card' => $legalIdCard,
                'license' => $license,
                'code'    => $enterpriseNo,
                'source'  => $companyData,
                'status'  => Certification::STATUS_PASS,
            ]);
            $role = Role::where('label', Role::LABEL_ADMINISTRATOR)->first();

            $staff = Staff::create([
                'user_id'     => $user->id,
                'company_id'  => $company->id,
                'business_id' => $business->id,
                'name'        => $legalName,
                'mobile'      => $phone,
                'position'    => $role->name,
                'type'        => 0,
                'status'      => Staff::STATUS_APPROVED
            ]);

            Business::where('company_name', $business->company_name)
                ->update([
                    'company_id'   => $company->id,
                    'company_name' => $enterpriseName,
                ]);

            //增加权限
            $user->companyDepartmentRoles()
                ->create([
                    'company_id'       => $company->id,
                    'company_staff_id' => $staff->id,
                    'department_id'    => 0,
                    'company_role_id'  => $role->id,
                ]);

            DB::commit();
            return $request->kernel->success('认证成功');
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 设置邀请码
     *
     * @Author: 玄尘
     * @Date: 2025/4/11 13:57
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function setCode(Request $request)
    {
        $request->kernel->validate([
            'code'       => ['required', 'min:4', 'max:8'],
            'type'       => 'required|integer',
            'company_id' => 'required|integer|exists:jz_companies,id',
        ], [
            'code.required'       => '邀请码不能为空',
            'code.min'            => '邀请码长度大于 :min',
            'code.max'            => '邀请码长度小于 :max',
            'type.required'       => '类型不能为空',
            'type.integer'        => '类型只能是数字',
            'company_id.required' => '企业ID不能为空',
            'company_id.required' => '企业ID只能是数字',
            'company_id.exists'   => '企业不存在',
        ]);
        $user = $request->kernel->user();

        $company_id = $request->company_id;
        $type       = $request->type;
        $code       = $request->code;

        if (! $user->isManagementRole($company_id)) {
            throw new ValidatorException("权限不足");
        }

        switch ($type) {
            case 1:
                $update['is_open'] = 0;
                break;
            case 2:
                $update['code']     = $code;
                $update['exp_time'] = Carbon::now()->addDays(7)->toDateTimeString();
                $update['is_open']  = 1;
                break;
            case 3:
                $update['code']     = $code;
                $update['exp_time'] = 0;
                $update['is_open']  = 1;
                break;
            default:
                throw new ValidatorException("类型错误");
                break;
        }

        $company = Company::find($company_id);
        $this->checkCompany($company);

        if ($company->info->is_open == 0 && $type == 1) {
            throw new ValidatorException("已关闭邀请码，请勿重复设置");
        }

        $res = $company->info()->update($update);

        if (! $res) {
            throw new ValidatorException("邀请码设置失败");
        }
        return $request->kernel->success(true);
    }

    /**
     * Notes: 在职认证
     *
     * @Author: 玄尘
     * @Date: 2025/4/7 13:33
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     * @throws \Throwable
     */
    public function workAuthentication(Request $request)
    {
        $workAuth = new WorkAuthRequest();
        $request->kernel->validate($workAuth->rules(), $workAuth->messages());

        $user = $request->kernel->user();

        $type         = $request->type;
        $company_name = $request->company_name;
        $nickname     = $request->nickname;
        $position     = $request->position;
        $link         = $request->link;
        $business_id  = $request->business_id;
        $company_id   = $request->company_id;

        //检测名片是否存在
        $business = Business::find($business_id);

        if ($business->user_id != $user->id) {
            throw new ValidatorException("您没有权限");
        }

        $staff = $business->staff;
        if ($staff) {
            switch ($staff->status) {
                case Staff::STATUS_PENDING:
                    throw new ValidatorException("您已提交过请等待审核");
                    break;
                case Staff::STATUS_REJECTED:
                    throw new ValidatorException("您提交的审核被拒绝");
                    break;
                case Staff::STATUS_LEAVE:
                    throw new ValidatorException("您已离职");
                    break;
                case Staff::STATUS_APPROVED:
                    throw new ValidatorException("您已认证过");
                    break;
            }
        }

        $company = Company::find($company_id);

        $this->checkCompany($company);

        $exists = Staff::query()
            ->where('user_id', $user->id)
            ->where('company_id', $company->id)
            ->exists();

        if ($exists) {
            throw new ValidatorException("您已经是员工");
        }

        DB::beginTransaction();
        try {
            switch ($type) {
                case 1://邀请码
                    if ($company->info->is_open != 1) {
                        DB::rollBack();
                        throw new ValidatorException("邀请码认证已经关闭");
                    }

                    if ($company->info->exp_time && Carbon::parse($company->info->exp_time)->lt(Carbon::now())) {
                        DB::rollback();
                        throw new ValidatorException("邀请码已经过期");
                    }

                    if ($company->code != $link) {
                        DB::rollBack();
                        throw new ValidatorException("邀请码错误");
                    }

                    $user->jzStaffs()->create([
                        'business_id' => $business->id,
                        'company_id'  => $company->id,
                        'type'        => 1,
                        'name'        => $nickname,
                        'mobile'      => $business->phone,
                        'position'    => $position,
                        'status'      => Staff::STATUS_APPROVED
                    ]);

                    $business->update([
                        'company_id'   => $company->id,
                        'position'     => $position,
                        'company_name' => $company->name,
                        'company_icon' => $company->logo
                    ]);

                    break;
                case 2:
                case 3:
                    $user->jzStaffs()->create([
                        'business_id' => $business->id,
                        'company_id'  => $company->id,
                        'type'        => 1,
                        'name'        => $nickname,
                        'mobile'      => $business->phone,
                        'position'    => $position,
                        'cover'       => $link,
                        'status'      => Staff::STATUS_PENDING
                    ]);
                    break;
                default:
                    break;
            }

            DB::commit();
            return $request->kernel->success(true);
        } catch (ValidatorException $e) {
            DB::rollBack();
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 企业详情
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 11:15
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function info(Request $request)
    {
        $request->kernel->validate([
            'company_id' => 'required|integer'
        ], [
            'company_id.required' => '企业id不能为空',
            'company_id.integer'  => '企业id只能是数字'
        ]);

        $user = $request->kernel->user();

        $companyId = $request->company_id;

        $company = Company::find($companyId);
        $this->checkCompany($company);

        return $request->kernel->success(new CompanyResource($company));
    }

    public function update(Request $request)
    {
        $updateRequest = new CompanyUpdateRequest();

        $request->kernel->validate($updateRequest->rules(), $updateRequest->messages());

        $user          = $request->kernel->user();
        $companyId     = $request->company_id;
        $business_type = $request->business_type;
        $company_type  = $request->company_type;
        $business      = $request->business;
        $company       = $request->company;
        $logo          = $request->logo;

        $map = [];

        if (! $user->isManagementRole($companyId)) {
            throw new ValidatorException("您没有操作权限");
        }

        $companyModel = Company::find($companyId);

        $this->checkCompany($companyModel);

        $companyData = [];

        $infoData = [
            'business_type' => $business_type,
            'business'      => $business,
            'company_type'  => $company_type,
            'company'       => $company,
        ];

        if ($logo) {
            $companyData['logo'] = $logo;
        }

        if (! array_merge($companyData, $infoData)) {
            throw new ValidatorException("参数缺失");
        }

        $res = $companyModel->info->update($infoData);
        if (! empty($companyData)) {
            $res = $companyModel->update($companyData);
        }
        if (! $res) {
            throw new ValidatorException("操作失败");
        }

        if (isset($companyData['logo'])) {
            $companyModel->businesses()->update([
                'company_icon' => $companyData['logo']
            ]);
        }
        return $request->kernel->success($res);
    }

    public function qrcode(Request $request)
    {
        $request->kernel->validate([
            'code'       => 'required',
            'company_id' => 'required|integer',
        ], [
            'company_id.required' => '请选择要生成邀请码的企业id',
            'company_id.integer'  => '企业id只能是数字',
            'code.required'       => '企业邀请码不能为空',
        ]);

        $user = $request->kernel->user();

        $company_id = $request->company_id;
        $code       = $request->code;

        if (! $user->isManagementRole($company_id)) {
            throw new ValidatorException("权限不足");
        }

        $company = Company::find($company_id);
        if (! $company->info->code) {
            throw new ValidatorException("请先设置企业邀请码");
        }

        if (! $company) {
            throw new ValidatorException("企业不存在");
        }

        try {
            $size       = $request->size ?? '430';
            $envVersion = $request->version ?? 'release';

            $app  = app('wechat.mini');
            $name = md5('company'.$company_id).'.png';
            $path = "share/company/{$envVersion}/{$name}";
            if (! Storage::has($path)) {
                $response = $app->getClient()->postJson('/wxa/getwxacodeunlimit', [
                    'scene'       => http_build_query([
                        'cid'  => $company_id,
                        'code' => $code,
                    ]),
                    'page'        => 'pages/authentication/job',
                    'width'       => $size,
                    'is_hyaline'  => true,
                    'env_version' => $envVersion,
                    'check_path'  => false,
                ]);
                Storage::put($path, $response->getContent());
            }

            return $request->kernel->success([
                'qrcode_url' => Storage::url($path),
                'qrcode'     => "data:image/png;base64,".base64_encode(Storage::get($path))
            ]);
        } catch (Exception $e) {
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 转让企业
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 10:09
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function transference(Request $request)
    {
        $request->kernel->validate([
            'staff_id'   => 'required|integer|exists:jz_company_staff,id',
            'company_id' => 'required|integer|exists:jz_companies,id',
        ], [
            'company_id.required' => '企业ID不能为空',
            'company_id.integer'  => '企业id只能是数字',
            'company_id.exists'   => '企业不存在',
            'staff_id.required'   => '员工id不能为空',
            'staff_id.integer'    => '员工id只能是数字',
            'staff_id.exists'     => '员工不存在',
        ]);

        $user = $request->kernel->user();

        $company_id = $request->company_id;
        $staff_id   = $request->staff_id;

        if (! $user->isAdministrator($company_id)) {
            throw new ValidatorException("没有权限执行转让操作");
        }

        DB::beginTransaction();
        try {
            $company = Company::find($company_id);

            $staff = Staff::find($staff_id);
            if (! $staff) {
                throw new ValidatorException("未找到用户信息");
            }

            if ($staff->status != Staff::STATUS_APPROVED) {
                throw new ValidatorException("用户不可成为管理员");
            }

            $administratorRole = Role::where('label', Role::LABEL_ADMINISTRATOR)->first();
            $user->companyDepartmentRoles()
                ->where('company_id', $company_id)
                ->where('company_role_id', $administratorRole->id)
                ->delete();//删除权限

            //增加权限
            DepartmentRole::create([
                'user_id'         => $staff->user_id,
                'company_id'      => $company_id,
                'company_role_id' => $administratorRole->id,
                'department_id'   => 0,
            ]);

            $company->user_id = $staff->user_id;
            $company->save();

            DB::commit();
            return $request->kernel->success(true);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ValidatorException($e->getMessage());
        }
    }

}