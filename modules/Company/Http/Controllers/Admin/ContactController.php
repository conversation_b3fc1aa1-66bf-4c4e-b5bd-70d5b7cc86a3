<?php

namespace Modules\Company\Http\Controllers\Admin;

use App\Admin\Traits\WithUploads;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Company\Models\BusinessContact;

class ContactController extends AdminController
{
    use WithUploads;

    protected string $title = '名片交换';

    public function grid(): Grid
    {
        return Grid::make(BusinessContact::class, function (Grid $grid) {
            $grid->model()->with(['business', 'fromBusiness', 'destination.info', 'user.info']);
            $grid->disableRowSelector();
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '发起用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
                $filter->equal('destination_id', '目标用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
                $filter->between('created_at', '创建时间')->datetime();
            });

            $grid->column('user', '发起用户')
                ->display(fn() => $this->user->show_all_name);

            $grid->column('destination', '目标用户')
                ->display(fn() => $this->destination->show_all_name);

            $grid->column('from_business', '发起名片')
                ->display(fn() => $this->fromBusiness->show_name);
            $grid->column('to_business', '目标名片')
                ->display(fn() => $this->business->show_name);
            $grid->column('status', '状态')->bool();
            $grid->column('is_initiator', '发起')->bool();
            $grid->column('created_at');
        });
    }

}