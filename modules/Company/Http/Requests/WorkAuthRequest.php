<?php

namespace Modules\Company\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class WorkAuthRequest extends FormRequest
{

    public function rules(): array
    {
        return [
            'type'        => 'required|integer',
            'nickname'    => 'required',
            'position'    => 'required',
            'link'        => 'required',
            'business_id' => 'required|integer|exists:jz_company_business,id',
            'company_id'  => 'required|integer|exists:jz_companies,id',
        ];
    }

    public function messages(): array
    {
        return [
            'company_name.required' => '企业名称不能为空',
            'nickname.required'     => '姓名',
            'type.required'         => '类型不能为空',
            'type.integer'          => '类型只能是数字',
            'position.required'     => '职位不能为空',
            'link.required'         => 'link不能为空',
            'business_id.required'  => '名片id不能为空',
            'business_id.integer'   => '名片id只能是数字',
            'business_id.exists'    => '名片不存在',
            'company_id.required'   => '企业id不能为空',
            'company_id.integer'    => '企业id只能是数字',
            'company_id.exists'     => '企业不存在',
        ];
    }

}