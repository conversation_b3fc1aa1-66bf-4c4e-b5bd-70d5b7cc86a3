<?php

namespace Modules\Company\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\User\Rules\MobileRule;

class BatchAddUserRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'nickname' => 'required',
            //            'company_name' => ['required', 'exists:App\Models\Company,company_name'],
            'phone'    => ['required', new MobileRule(), 'unique:App\Models\User,username'],
            'position' => 'required',
            //            'email'        => 'nullable|email',
            //            'address'    => 'required',
            //            'wiki'       => 'required',
            //            'industry'   => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'nickname.required'     => '昵称不能为空',
            'company_name.required' => '公司名称不能为空',
            'company_name.exists'   => '公司不存在',
            'wechat.required'       => '微信不能为空',
            'phone.required'        => '手机不能为空',
            'phone.unique'          => '手机号已存在',
            'position.required'     => '职位不能为空',
            'email.required'        => '邮箱不能为空',
            'address.required'      => '地址不能为空',
            'wiki.required'         => 'wiki地址不能为空',
            'industry.required'     => '所属行业不能为空',
        ];
    }

}
