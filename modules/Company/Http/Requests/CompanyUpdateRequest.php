<?php

namespace Modules\Company\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CompanyUpdateRequest extends FormRequest
{

    public function rules(): array
    {
        return [
            'company_id'    => 'required|integer',
            'business_type' => 'nullable|integer|in:0,1,2',
            'company_type'  => 'nullable|integer|in:0,1,2',
            //            'business'      => 'required',
            //            'company'      => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'company_id.required'   => '企业id不能为空',
            'company_id.integer'    => '企业id只能是数字',
            'business_type.integer' => '业务介绍类型只能是数字',
            'business_type.in'      => '业务介绍类型参数不对',
            'company_type.integer'  => '企业介绍类型只能是数字',
            'company_type.in'       => '企业介绍类型参数不对'
        ];
    }

}