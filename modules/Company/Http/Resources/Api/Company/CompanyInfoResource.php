<?php

namespace Modules\Company\Http\Resources\Api\Company;

use Illuminate\Http\Resources\Json\JsonResource;

class CompanyInfoResource extends JsonResource
{

    public function toArray($request): array
    {
        return [
            'business_type' => $this->business_type,
            'business'      => $this->business,
            'company'       => $this->company,
            'company_type'  => $this->company_type,
            'is_open'       => $this->is_open,
            'exp_time'      => $this->exp_time,
            'code'          => $this->code,
        ];
    }

}
