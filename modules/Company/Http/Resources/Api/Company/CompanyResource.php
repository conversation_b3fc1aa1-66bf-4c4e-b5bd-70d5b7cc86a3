<?php

namespace Modules\Company\Http\Resources\Api\Company;

use App\Http\Resources\Industry\IndustryResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'count'         => $this->getData(),
            'name'          => $this->name,
            'can'           => $this->getCan($request->kernel->user()),
            'info'          => new CompanyInfoResource($this->info),
            'industry'      => new IndustryResource($this->industry),
            'logo'          => [
                'path' => $this->logo,
                'url'  => $this->logo_url,
            ],
            'certification' => new CertificationResource($this->certification),
            'created_at'    => (string) $this->created_at,
        ];
    }
}