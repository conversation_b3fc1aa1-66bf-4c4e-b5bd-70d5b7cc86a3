<?php

namespace Modules\Company\Http\Resources\Api\Company;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class CompanyStaffCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($staff) {
                return [
                    'id'                 => $staff->id,
                    'position'           => $staff->position,
                    'nickname'           => $staff->user->info->nickname,
                    'phone'              => $staff->user->username,
                    'is_display_network' => $staff->is_display_network,
                ];
            }),
        ], $this->page());
    }
}