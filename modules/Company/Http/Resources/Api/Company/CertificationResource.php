<?php

namespace Modules\Company\Http\Resources\Api\Company;

use Illuminate\Http\Resources\Json\JsonResource;

class CertificationResource extends JsonResource
{

    public function toArray($request): array
    {
        return [
            'name'    => $this->name,
            'id_card' => $this->id_card,
            'code'    => $this->code,
            'license' => [
                'path' => $this->license,
                'url'  => $this->license_url,
            ],
            'status'  => $this->status,
        ];
    }

}
