<?php

namespace Modules\Company\Http\Resources\Api\BusinessBrowseRecord;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class MyRecordCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return new MyRecordResource($item);
            }),
        ], $this->page());
    }
}