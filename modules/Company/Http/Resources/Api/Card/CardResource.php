<?php

namespace Modules\Company\Http\Resources\Api\Card;

use App\Http\Resources\Industry\IndustryResource;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Company\Models\BusinessContact;

class CardResource extends JsonResource
{

    public function toArray($request): array
    {
        $contact = BusinessContact::where('destination_id', $this->user_id)
            ->where('to_business_id', $this->id)
            ->first();
        $user    = $request->kernel->user();

        return [
            'id'           => $this->id,
            'nickname'     => $this->nickname,
            'company_name' => $this->company_name,
            'phone'        => $this->phone,
            'avatar'       => $this->avatar,
            'position'     => $this->position,
            'wechat'       => $this->wechat,
            'email'        => $this->email,
            'address'      => $this->address,
            'industry'     => $this->industry_id ? new IndustryResource($this->industry) : '',
            'is_work'      => $this->is_work,
            'is_company'   => $this->is_company,
            'annex'        => $this->annex ?? '',
            'annex_name'   => $this->annex_name ?? '',
            'join_time'    => (string) $contact->created_at ?? '',
            'is_private'   => $contact && $contact->user_id == $user->id,
            'from'         => $this->from,
        ];
    }

}
