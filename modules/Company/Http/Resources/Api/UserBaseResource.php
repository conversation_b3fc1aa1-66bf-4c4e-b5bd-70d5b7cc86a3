<?php

namespace Modules\Company\Http\Resources\Api;

use App\Http\Resources\Realname\RealnameResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserBaseResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'username'   => $this->username,
            'pass'       => $this->pass,
            'info'       => [
                'nickname' => $this->info->nickname,
                'avatar'   => $this->info->avatar_url,
            ],
            'realname'   => new RealnameResource($this->realName),
            'created_at' => (string) $this->created_at,
        ];
    }
}