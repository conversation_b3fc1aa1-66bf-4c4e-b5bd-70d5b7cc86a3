<?php

namespace Modules\Company;

use App\Contracts\ModuleBootstrap;
use Illuminate\Support\Facades\Artisan;

class Bootstrap implements ModuleBootstrap
{
    protected static string $menuKey = 'company-module';

    public static function install(): void
    {
        Artisan::call('migrate', [
            '--path' => 'modules/Company/Database/Migrations',
        ]);

        Artisan::call('module:seed Company');

        self::createAdminMenu();
    }

    public static function uninstall(): void
    {
//        Artisan::call('migrate:reset', [
//            '--path' => 'modules/Company/Database/Migrations',
//        ]);
        self::deleteAdminMenu();
    }

    public static function upgrade(): void
    {
        self::deleteAdminMenu();
        Artisan::call('migrate', [
            '--path' => 'modules/Company/Database/Migrations',
        ]);
        self::createAdminMenu();
    }

    public static function createAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');

        // 检查菜单是否已存在
        if ($menuModel::where('icon', 'like', '%'.self::$menuKey)->exists()) {
            return;
        }

        $main = $menuModel::create([
            'parent_id' => 0,
            'order'     => 10,
            'title'     => '企业管理--模块',
            'icon'      => 'fa-book '.self::$menuKey,
        ]);

        $main->children()->createMany([
            [
                'order' => 1,
                'title' => '企业列表',
                'icon'  => 'fa-bank '.self::$menuKey,
                'uri'   => 'jz_companies',
            ],
            [
                'order' => 2,
                'title' => '行业列表',
                'icon'  => 'fa-arrows-alt '.self::$menuKey,
                'uri'   => 'jz_companies/industries',
            ],
            [
                'order' => 3,
                'title' => '员工列表',
                'icon'  => 'fa-users '.self::$menuKey,
                'uri'   => 'jz_companies/users',
            ],
            [
                'order' => 4,
                'title' => '职位列表',
                'icon'  => 'fa-cogs '.self::$menuKey,
                'uri'   => 'jz_companies/roles',
            ],
            [
                'order' => 5,
                'title' => '部门列表',
                'icon'  => 'fa-bus '.self::$menuKey,
                'uri'   => 'jz_companies/departments',
            ],
            [
                'order' => 6,
                'title' => '名片列表',
                'icon'  => 'fa-credit-card '.self::$menuKey,
                'uri'   => 'jz_companies/businesses',
            ],
            [
                'order' => 7,
                'title' => '名片交换记录',
                'icon'  => 'fa-arrows-h '.self::$menuKey,
                'uri'   => 'jz_companies/contacts',
            ],
        ]);
    }

    public static function deleteAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');
        $menuModel::where('icon', 'like', '%'.self::$menuKey)->delete();
    }
}