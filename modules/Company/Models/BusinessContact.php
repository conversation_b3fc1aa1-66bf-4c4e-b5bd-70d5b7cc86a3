<?php

namespace Modules\Company\Models;

use App\Models\Model;
use App\Models\User;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessContact extends Model
{
    use BelongsToUser;

    protected $table = 'jz_company_business_contacts';

    protected $casts = [
        'status' => 'integer'
    ];

    const STATUS_NO_LINK                = 0;
    const STATUS_WAITING_THEIR_APPROVAL = 1;
    const STATUS_CONNECTED              = 2;
    const STATUS_WAITING_OUR_APPROVAL   = 3;
    const STATUS_NO_BUSINESS            = 4;
    const STATUS_CHANGE                 = [
        self::STATUS_NO_LINK                => '没有建立链接',
        self::STATUS_WAITING_THEIR_APPROVAL => '等待对方同意',
        self::STATUS_WAITING_OUR_APPROVAL   => '等待我方同意',
        self::STATUS_CONNECTED              => '建立联系',
        self::STATUS_NO_BUSINESS            => '没有名片',
    ];

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'to_business_id', 'id');
    }

    public function fromBusiness(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'from_business_id', 'id');
    }

    public function destination(): BelongsTo
    {
        return $this->belongsTo(User::class, 'destination_id', 'id');
    }

    public static function getChangeStatus($user_id, $business_id, $destination_id, $default_id = ""): int
    {
        // 检查目标方发起的联系请求
        $contactTo = self::query()
            ->where('destination_id', $destination_id)
            ->where('from_business_id', $business_id)
            ->first();
        if ($contactTo) {
            return $contactTo->status === 0 ? self::STATUS_WAITING_OUR_APPROVAL : self::STATUS_CONNECTED;
        }

        $contact = BusinessContact::where('destination_id', $user_id)
            ->where('to_business_id', $business_id)
            ->where('user_id', $destination_id)
            ->first();
        if ($contact) {
            return $contact->status === 0 ? self::STATUS_WAITING_THEIR_APPROVAL : self::STATUS_CONNECTED;
        }

        return self::STATUS_NO_LINK;
    }
}