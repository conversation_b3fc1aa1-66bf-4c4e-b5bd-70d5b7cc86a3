<?php

namespace Modules\Company\Models;

use App\Models\Model;
use App\Models\Realname;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\Company\Models\Traits\BelongsToBusiness;

class BusinessBrowseRecord extends Model
{
    use BelongsToBusiness,
        BelongsToUser;

    protected $table = 'jz_company_business_browse_records';

    public function realname(): HasOne
    {
        return $this->hasOne(Realname::class, 'uid', 'user_id');
    }

    public static function browse($business, $user)
    {
        if ($business->user->isNot($user)) {
            $browse = self::where('user_id', $user->id)
                ->where('business_id', $business->id)
                ->first();

            if (! $browse) {
                self::create([
                    'business_id' => $business->id,
                    'user_id'     => $user->id,
                    'count'       => 1
                ]);
            } else {
                $browse->count += 1;
                $browse->save();
            }
        }
    }

    public static function getMyBusinessBrowseCount($user, $start_time, $end_time): array
    {
        return [
            'browse_today' => BusinessBrowseRecord::query()
                ->whereHas('business', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                })
                ->whereBetween('created_at', [$start_time, $end_time])
                ->count(),
            'browse_total' => BusinessBrowseRecord::query()
                ->whereHas('business', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                })
                ->count(),
            'send_total'   => $user->jzBusinesses()->sum('send_count') ?? 0,
            'change_total' => BusinessContact::query()
                ->where('user_id', $user->id)
                ->where('is_initiator', 0)
                ->where('status', 0)
                ->count(),
            'my_browse'    => BusinessBrowseRecord::where('user_id', $user->id)->count(),
        ];
    }

}