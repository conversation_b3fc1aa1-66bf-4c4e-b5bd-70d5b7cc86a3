<?php

namespace Modules\Company\Models;

use App\Models\Model;
use App\Traits\HasCovers;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Company\Models\Traits\BelongsToCompany;

class CompanyInfo extends Model
{

    use BelongsToCompany,
        HasCovers,
        SoftDeletes;

    protected $table = 'jz_company_infos';
    protected $casts = [
        'source'  => 'array',
        'is_open' => 'integer',
    ];

    const TYPE_ZU    = 0;
    const TYPE_IMG   = 1;
    const TYPE_VIDEO = 2;

    const TYPE_MAP = [
        self::TYPE_ZU    => '组合',
        self::TYPE_IMG   => '图片',
        self::TYPE_VIDEO => '视频'
    ];

    const TYPE_MAP_LABEL = [
        self::TYPE_ZU    => 'info',
        self::TYPE_IMG   => 'pink',
        self::TYPE_VIDEO => 'warning',
    ];

    const IS_OPENS = [
        1 => '开启',
        0 => '关闭'
    ];

    /**
     * Notes: 获取企业介绍
     *
     * @Author: 玄尘
     * @Date: 2024/12/23 09:38
     * @return mixed|null
     */
    public function getCompany($channel = 'system')
    {
        switch ($this->company_type) {
            case self::TYPE_ZU:
                return $this->company;
                break;
            case self::TYPE_IMG:
            case self::TYPE_VIDEO:

                if ($channel != 'system') {
                    return $this->company;
                } else {
                    $data = $company = json_decode($this->company, true);
                    if (count($company)) {
                        $data = [];
                        foreach ($company as $item) {
                            $data[] = ltrim(parse_url($item, PHP_URL_PATH), '/');
                        }
                    }
                    return $data;
                }
                break;
            default:
                return null;
        }
    }

    /**
     * Notes: 获取业务介绍
     *
     * @Author: 玄尘
     * @Date: 2024/12/23 09:38
     * @return mixed|null
     */
    public function getBusiness($channel = 'system'): mixed
    {
        switch ($this->business_type) {
            case self::TYPE_ZU:
                return $this->business ?? '';
                break;
            case self::TYPE_IMG:
            case self::TYPE_VIDEO:
                if ($channel != 'system') {
                    return $this->business;
                } else {
                    $data = $business = json_decode($this->business, true);
                    if (count($business)) {
                        $data = [];
                        foreach ($business as $item) {
                            $data[] = ltrim(parse_url($item, PHP_URL_PATH), '/');
                        }
                    }
                    return $data;
                }
                break;
            default:
                return null;
        }
    }

}