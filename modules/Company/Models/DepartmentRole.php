<?php

namespace Modules\Company\Models;

use App\Traits\BelongsToUser;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Modules\User\Models\Department;

class DepartmentRole extends Pivot
{
    use BelongsToUser,
        HasDateTimeFormatter;

    protected $table = 'jz_company_department_role';

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'company_role_id');
    }

    public function staff(): BelongsTo
    {
        return $this->belongsTo(Staff::class, 'company_staff_id');
    }
}
