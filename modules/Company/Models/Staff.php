<?php

namespace Modules\Company\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Arr;
use Modules\Company\Models\Traits\BelongsToBusiness;
use Modules\Company\Models\Traits\BelongsToCompany;
use Modules\User\Models\Department;
use Modules\User\Models\UserDepartment;

class Staff extends Model
{
    use BelongsToCompany,
        BelongsToBusiness,
        BelongsToUser;

    protected $table = 'jz_company_staff';
    protected $casts = [
        'status' => 'integer'
    ];
    const STATUS_PENDING  = 0; // 待审核
    const STATUS_APPROVED = 1; // 审核通过
    const STATUS_REJECTED = 2; // 审核拒绝
    const STATUS_LEAVE    = 3; // 离职

    const STATUS_MAP   = [
        self::STATUS_PENDING  => '待审核',
        self::STATUS_APPROVED => '在职',
        self::STATUS_REJECTED => '拒绝',
        self::STATUS_LEAVE    => '离职'
    ];
    const STATUS_LABEL = [
        self::STATUS_PENDING  => 'info',
        self::STATUS_APPROVED => 'pink',
        self::STATUS_REJECTED => 'warning',
        self::STATUS_LEAVE    => 'danger'
    ];

    const TYPE_TEXT = [
        0 => '创始人',
        1 => '邀请码加入',
        2 => '工牌加入',
        3 => '名片加入',
        4 => '营业执照加入',
        5 => '系统录入',
    ];

    public function getStatusTextAttribute(): string
    {
        return self::STATUS_MAP[$this->status] ?? '未知';
    }

    /**
     * Notes: 返回员工来源名称
     *
     * @Author: 玄尘
     * @Date: 2024/12/31 15:33
     * @return \Illuminate\Database\Eloquent\Casts\Attribute
     */
    public function typeName(): Attribute
    {
        return new Attribute(
            get: fn() => self::TYPE_TEXT[$this->type] ?? '未知'
        );
    }

    /**
     * Notes: description
     *
     * @Author: 玄尘
     * @Date: 2024/12/31 15:33
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfTypeSearch(Builder $query, int|null $status)
    {
        if ($status) {
            return $query->where('status', $status ?? self::STATUS_PENDING);
        }
    }

    public function getTypeName($type)
    {
        return Arr::get(self::TYPE_TEXT, $type, '其他方式');
    }

    /**
     * Notes: 关联部门
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 08:52
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function departments(): HasManyThrough
    {
        return $this->hasManyThrough(
            Department::class,       // 目标模型：departments
            UserDepartment::class,   // 中间模型：user_department
            'user_id',               // user_department 表的外键（指向 staff.user_id）
            'id',                    // departments 表的主键
            'user_id',               // staff 表的本地键（staff.user_id）
            'department_id'          // user_department 表的外键（指向 departments.id）
        );
    }

    /**
     * Notes: 关联角色/职位
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 08:54
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function roles(): HasManyThrough
    {
        return $this->hasManyThrough(
            Role::class,
            DepartmentRole::class,
            'company_staff_id',
            'id',
            'id',
            'company_role_id'
        );
    }

    public function agree(): true
    {
        $this->status = self::STATUS_APPROVED;
        $this->save();
        return true;
    }

    /**
     * Notes: 驳回
     *
     * @Author: 玄尘
     * @Date: 2024/12/31 14:28
     */
    public function reject($reason = ''): true
    {
        $this->status = self::STATUS_REJECTED;
        if ($reason) {
            $this->reject_reason = $reason;
        }
        $this->save();
        return true;
    }

}