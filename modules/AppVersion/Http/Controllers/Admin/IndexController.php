<?php

namespace Modules\AppVersion\Http\Controllers\Admin;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\AppVersion\Enums\PlatformType;
use Modules\AppVersion\Models\AppVersion;

class IndexController extends AdminController
{
    protected string $title = '版本';

    public function grid(): Grid
    {
        return Grid::make(AppVersion::orderByDesc('id'), function (Grid $grid) {
            $grid->column('platform', '平台');
            $grid->column('application_id', '包名');
            $grid->column('version', '版本号');
            $grid->column('force', '强制更新')->bool();
            $grid->column('publish_at', '发布日期');
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(AppVersion::class, function (Form $form) {
            $form->radio('platform', '平台')
                ->options(PlatformType::PLATFORM_TYPES)
                ->default(PlatformType::ANDROID->value);
            $form->text('application_id', '包名')
                ->required();
            $form->url('download_url', '下载地址')
                ->required()
                ->help('Android 直接填写下载地址，可以放在一些省流量的存储上， IOS填写APP-STORE地址');
            $form->text('version', '版本号')
                ->required();
            $form->list('description', '更新说明')
                ->required();
            $form->switch('force', '强制更新');
            $form->datetime('publish_at', '发布时间')
                ->help('不填写默认为立即发布');
        });
    }
}