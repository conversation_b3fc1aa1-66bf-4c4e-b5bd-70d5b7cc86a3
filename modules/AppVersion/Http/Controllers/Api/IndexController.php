<?php

namespace Modules\AppVersion\Http\Controllers\Api;

use App\Http\Controllers\ApiController;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Carbon;
use Modules\AppVersion\Http\Requests\VersionRequest;
use Modules\AppVersion\Models\AppVersion;

class IndexController extends ApiController
{
    public function index(VersionRequest $request): JsonResponse
    {
        $platform      = $request->safe()->platform;
        $version       = $request->safe()->version;
        $applicationId = $request->safe()->application_id;

        $app = AppVersion::select([
            'version', 'description', 'version', 'force', 'download_url', 'publish_at', 'created_at'
        ])
            ->where('platform', $platform)
            ->where('application_id', $applicationId)
            ->where(function (Builder $query) {
                $query->where('publish_at', '<', Carbon::now())
                    ->orWhereNull('publish_at');
            })
            ->orderByRaw("INET_ATON(SUBSTRING_INDEX(CONCAT(version,'.0.0.0'),'.',4)) DESC")
            ->first();

        $result = ['update' => false];

        if ($app) {
            $force = $app->force;
            if (! $force) {
                $force = AppVersion::where('platform', $platform)
                    ->where('application_id', $applicationId)
                    ->where('force', 1)
                    ->where('version', '>', $version)
                    ->exists();
            }
            $update = version_compare($version, $app->version, '<');
            if ($update) {
                $result = [
                    'update'         => $update,
                    'application_id' => $applicationId,
                    'description'    => $app->description,
                    'version'        => $app->version,
                    'force'          => $force,
                    'download'       => $app->download_url,
                    'publish_at'     => (string) ($app->publish_at ?: $app->created_at),
                ];
            }
        }

        return $this->success($result);
    }
}