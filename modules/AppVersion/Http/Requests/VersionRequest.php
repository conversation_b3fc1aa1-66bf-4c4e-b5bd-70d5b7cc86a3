<?php

namespace Modules\AppVersion\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\Enum;
use Modules\AppVersion\Enums\PlatformType;

class VersionRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'platform'       => [
                'required',
                new Enum(PlatformType::class),
            ],
            'application_id' => 'required',
            'version'        => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'platform.required'       => '平台必填',
            'platform.in'             => '平台有误',
            'application_id.required' => '应用包名必填',
            'version.required'        => '版本号必填',
        ];
    }
}