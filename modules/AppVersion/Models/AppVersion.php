<?php

namespace Modules\AppVersion\Models;

use App\Models\Model;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\AppVersion\Enums\PlatformType;

class AppVersion extends Model
{
    use Cachable,
        SoftDeletes;

    protected $table = 'app_versions';

    protected $casts = [
        'platform'    => PlatformType::class,
        'description' => 'json',
        'force'       => 'boolean',
        'publish_at'  => 'datetime',
    ];
}
