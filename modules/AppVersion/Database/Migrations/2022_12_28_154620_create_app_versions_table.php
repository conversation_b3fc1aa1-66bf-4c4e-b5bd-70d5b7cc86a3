<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\AppVersion\Enums\PlatformType;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('app_versions', function (Blueprint $table) {
            $table->id();
            $table->enum('platform', PlatformType::values())->index();
            $table->string('application_id')->index()->comment('包名');
            $table->string('version', 16)->index()->comment('版本号');
            $table->boolean('force')->default(0)->comment('强制更新');
            $table->json('description')->nullable()->comment('升级说明');
            $table->string('download_url')->nullable()->comment('下载地址');
            $table->timestamp('publish_at')->nullable('发布时间');
            $table->timestamps();
            $table->softDeletes()
                ->index();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('app_versions');
    }
};
