<?php

namespace Modules\AppVersion\Enums;

use App\Traits\EnumMethods;

enum PlatformType: string
{
    use EnumMethods;

    case ANDROID     = 'android';
    case IOS         = 'ios';
    case ANDROID_PAD = 'apad';
    case IPAD        = 'ipad';
    case WMP         = 'wmp';

    public const PLATFORM_TYPES = [
        self::ANDROID->value     => '安卓平台',
        self::IOS->value         => 'IOS平台',
        self::ANDROID_PAD->value => '安卓平板',
        self::IPAD->value        => 'IPAD平板',
        self::WMP->value         => '微信小程序',
    ];

    public function toString(): string
    {
        return self::PLATFORM_TYPES[$this->value];
    }
}