<?php

namespace Modules\Cms\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class CategoryRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'parent_id' => 'nullable|integer',
            'name'      => 'nullable|string',
        ];
    }

    public function messages(): array
    {
        return [
            'parent_id.integer' => '上级分类必须是数字',
            'name.name'         => '分类名称必须是字符串',
        ];
    }
}