<?php

namespace Modules\Cms\Http\Controllers\Admin;

use App\Admin\Actions\Restore;
use App\Admin\Traits\WithUploads;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Cms\Models\Category;

class CategoryController extends AdminController
{
    use WithUploads;

    protected string $title = '分类';

    public function grid(): Grid
    {
        return Grid::make(Category::ordered(), function (Grid $grid) {
            $grid->showBatchDelete();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(Category::class));
                }
            });

            $grid->quickSearch(['name'])
                ->placeholder('分类名称');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->onlyTrashed();

                $filter->like('name', '分类名称');
                $filter->equal('status', '状态')
                    ->radio([
                        0 => '禁用',
                        1 => '启用',
                    ]);
                $filter->between('created_at', '添加时间')
                    ->datetime();
            });

            $grid->column('id');
            $grid->column('name', '分类名称')
                ->tree();
            $grid->column('status', '状态')
                ->switch();
            $grid->column('is_index', '首页展示')
                ->switch();
            $grid->column('order', '排序')
                ->orderable();
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(Category::class, function (Form $form) {
            $form->select('parent_id', '上级分类')
                ->default(0)
                ->options(Category::selectOptions())
                ->required();
            $form->text('name', '分类名称')
                ->required();
            $this->cover($form)
                ->width(4);
            $form->textarea('description', '分类简介');
            $form->switch('status', '状态');
            $form->switch('is_index', '首页显示');
            $form->number('order', '排序')
                ->required();
        });
    }
}
