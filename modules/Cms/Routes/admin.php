<?php

use Illuminate\Support\Facades\Route;
use Modules\Cms\Http\Controllers\Admin\CategoryController;
use Modules\Cms\Http\Controllers\Admin\ContentController;
use Modules\Cms\Http\Controllers\Admin\DashboardController;
use Modules\Cms\Http\Controllers\Admin\MaterialController;
use Modules\Cms\Http\Controllers\Admin\PageController;
use Modules\Cms\Http\Controllers\Admin\TagController;

Route::get('', [DashboardController::class, 'index']);

Route::resource('categories', CategoryController::class)->except(['show']);
Route::resource('contents', ContentController::class)->except(['show']);
Route::resource('materials', MaterialController::class)->except(['show']);
Route::get('tags/ajax', [TagController::class, 'ajax'])->name('tags.ajax');
Route::resource('tags', TagController::class)->except(['show']);
Route::resource('pages', PageController::class)->except(['show']);
