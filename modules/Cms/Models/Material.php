<?php

namespace Modules\Cms\Models;

use App\Models\Model;
use App\Traits\HasCovers;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Support\Facades\Storage;

class Material extends Model
{
    use Cachable,
        HasCovers;

    protected $table = 'cms_materials';

    protected static function boot(): void
    {
        parent::boot();

        self::deleted(function ($model) {
            Storage::delete($model->cover);
        });
    }
}
