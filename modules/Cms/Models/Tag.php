<?php

namespace Modules\Cms\Models;

use App\Models\Model;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Tag extends Model
{
    use Cachable,
        SoftDeletes;

    protected $table = 'cms_tags';

    protected static function boot(): void
    {
        parent::boot();

        self::deleted(function ($model) {
            Taggable::where('tag_id', $model->id)->delete();
        });
    }

    public function contents(): BelongsToMany
    {
        return $this->belongsToMany(Content::class, 'cms_taggable');
    }
}
