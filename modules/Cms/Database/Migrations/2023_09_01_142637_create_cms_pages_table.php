<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cms_pages', function (Blueprint $table) {
            $table->id();
            $table->string('slug')
                ->index()
                ->nullable()
                ->comment('唯一的别名');
            $table->string('title');
            $table->string('sub_title')
                ->nullable()
                ->comment('副标题');
            $table->string('description')
                ->nullable();
            $table->string('cover')
                ->nullable();
            $table->json('pictures')
                ->nullable();
            $table->longText('content');
            $table->json('attachments')
                ->nullable();
            $table->easyStatus();
            $table->unsignedInteger('clicks')
                ->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cms_pages');
    }
};
