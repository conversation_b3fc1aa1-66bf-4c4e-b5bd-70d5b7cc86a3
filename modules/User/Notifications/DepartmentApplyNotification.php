<?php

namespace Modules\User\Notifications;

use App\Enums\ApplyStatus;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\DatabaseMessage;
use Modules\User\Models\DepartmentApply;

class DepartmentApplyNotification extends BaseNotification
{
    public function __construct(protected DepartmentApply $apply)
    {
    }

    public static function getTitle(): string
    {
        return '申请创建部门结果';
    }

    /**
     * @throws \Exception
     */
    public function toDatabase(Model $notifiable): array|DatabaseMessage
    {
        switch ($this->apply->status) {
            case ApplyStatus::PASS:
                return new DatabaseMessage('您申请的【{name}】部门已经审核通过', [
                    'name' => $this->apply->name,
                ]);
            case ApplyStatus::REJECT:
                return new DatabaseMessage('您申请的【%s】部门已被拒绝，拒绝原因为：%s', [
                    'name'   => $this->apply->name,
                    'reason' => $this->apply->reason,
                ]);
            case ApplyStatus::INIT:
                throw new Exception('To be implemented');
        }
    }
}