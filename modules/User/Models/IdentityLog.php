<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\User\Enums\IdentityChannel;

class IdentityLog extends Model
{
    use BelongsToUser;

    protected $table = 'user_identity_logs';

    protected $casts = [
        'source'  => 'json',
        'channel' => IdentityChannel::class,
    ];

    public function before(): BelongsTo
    {
        return $this->belongsTo(Identity::class, 'before')
            ->withDefault([
                'name' => '无',
            ])
            ->withTrashed();
    }

    public function after(): BelongsTo
    {
        return $this->belongsTo(Identity::class, 'after')
            ->withDefault([
                'name' => '无',
            ])
            ->withTrashed();
    }
}
