<?php

namespace Modules\User\Models;

use App\Traits\BelongsToUser;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class UserIdentity extends Pivot
{
    use BelongsToUser,
        HasDateTimeFormatter;

    public $incrementing = true;

    protected $table = 'user_identity';

    protected $guarded = [];

    public function identity(): BelongsTo
    {
        return $this->belongsTo(Identity::class);
    }

    /**
     * Notes   : 格式化身份编号
     *
     * @Date   : 2023/1/2 22:27
     * <AUTHOR> <Jason.C>
     * @return string
     */
    public function getSerialNoAttribute(): string
    {
        if ($this->identity && $this->identity->serial_open) {
            $prefix = $this->identity->serial_prefix;
            $places = $this->identity->serial_places;
            return $prefix.str_pad($this->serial, $places, "0", STR_PAD_LEFT);
        } else {
            return '';
        }
    }

    /**
     * Notes   : 获取最新的身份编号
     *
     * @Date   : 2023/1/2 22:28
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Models\Identity  $identity
     * @return int
     */
    public static function getNewestSerialNo(Identity $identity): int
    {
        $reserve = $identity->serial_reserve;
        $current = UserIdentity::where('identity_id', $identity->getKey())->max('serial');

        return max($reserve, $current) + 1;
    }
}