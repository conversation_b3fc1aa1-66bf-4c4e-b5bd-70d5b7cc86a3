<?php

namespace Modules\User\Models;

use App\Models\Model;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use InvalidArgumentException;
use Modules\User\Extensions\SmsMessage;
use Overtrue\EasySms\Exceptions\NoGatewayAvailableException;
use RuntimeException;

class Sms extends Model
{
    public    $incrementing = false;
    protected $table        = 'user_sms';
    protected $keyType      = 'string';

    protected $casts = [
        'used' => 'bool',
    ];

    /**
     * Notes   : 发送短信
     *
     * @Date   : 2023/7/14 11:08
     * <AUTHOR> <Jason.C>
     * @param  string  $mobile  手机号
     * @param  \Modules\User\Extensions\SmsMessage  $message
     * @return string
     * @throws \Exception
     */
    public static function send(string $mobile, SmsMessage $message): string
    {
        if ($mobile == '17601015814') {
            config(['user.sms.debug' => true]);
        }
        if (config('user.sms.debug')) {
            $gateway = 'debug';
        } else {
            try {
                $result  = app('user.sms')->send($mobile, $message);
                $gateway = Arr::get(Arr::first($result),
                    'gateway') ?? 'unknown';
            } catch (Exception $exception) {
                match (true) {
                    $exception instanceof NoGatewayAvailableException => throw new RuntimeException($exception->getMessage(),
                        500),
                    $exception instanceof InvalidArgumentException => throw new RuntimeException('参数有误',
                        500),
                    default => throw $exception,
                };
            }
        }

        return Sms::create([
            'channel' => $message->getChannel(),
            'mobile'  => $mobile,
            'gateway' => $gateway,
            'content' => $message->getCode(),
        ])->getKey();
    }

    protected static function boot(): void
    {
        parent::boot();

        self::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }
}
