<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_identities', function (Blueprint $table) {
            $table->string('apple_goods_id')->nullable()->comment('苹果商品ID')->after('card');
        });
    }

    public function down(): void
    {
        Schema::table('user_identities', function (Blueprint $table) {
            $table->dropColumn('apple_goods_id');
        });
    }
};
