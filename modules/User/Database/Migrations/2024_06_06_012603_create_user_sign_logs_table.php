<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_sign_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('sign_id');
            $table->unsignedInteger('rule_id');
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('coupon_id')->nullable()->comment('目标ID');
            $table->unsignedInteger('number')->default(0)->comment('优惠券ID');
            $table->unsignedDecimal('score')->default(0)->comment('积分');
            $table->timestamps();
            $table->index(['rule_id']);
            $table->index(['user_id']);
            $table->index(['user_id', 'rule_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_sign_logs');
    }
};
