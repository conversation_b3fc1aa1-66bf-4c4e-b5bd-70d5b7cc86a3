<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\User\Enums\IdentityOrderStatus;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_identity_orders', function (Blueprint $table) {
            $table->comment('身份订阅订单');
            $table->id();
            $table->string('no', 32)
                ->index();
            $table->user();
            $table->unsignedBigInteger('identity_id');
            $table->unsignedBigInteger('qty')
                ->comment('订阅数量');
            $table->decimal('amount', 10)
                ->comment('订单总金额');
            $table->enum('status', IdentityOrderStatus::values())
                ->default(IdentityOrderStatus::UNPAY->value)
                ->comment('支付状态');
            $table->timestamps();
            $table->index(['user_id', 'identity_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_identity_orders');
    }
};
