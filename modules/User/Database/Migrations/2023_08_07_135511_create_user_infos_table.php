<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\User\Enums\Gender;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_infos', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')
                ->primary();
            $table->string('nickname')
                ->nullable();
            $table->string('avatar')
                ->nullable();
            $table->enum('gender', Gender::values())
                ->default(Gender::SECRET->value);
            $table->text('description')
                ->nullable();
            $table->date('birthday')
                ->comment('生日')
                ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_infos');
    }
};
