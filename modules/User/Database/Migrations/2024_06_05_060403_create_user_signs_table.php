<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_signs', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->timestamp('sign_at')->comment('签到时间');
            $table->unsignedInteger('continuous_day')->default(0)->comment('连续天数');
            $table->text('message')->nullable();
            $table->index('user_id');
            $table->index('sign_at');
            $table->index(['user_id', 'sign_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_signs');
    }
};
