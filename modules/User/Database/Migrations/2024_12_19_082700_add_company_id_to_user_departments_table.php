<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_departments', function (Blueprint $table) {
            $table->unsignedBigInteger('company_id')->nullable()->after('parent_id')->index();
        });
    }

    public function down(): void
    {
        Schema::table('user_departments', function (Blueprint $table) {
        });
    }
};
