<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_login_logs', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->string('name')
                ->nullable()
                ->comment('登录设备');
            $table->string('device_id')
                ->nullable()
                ->comment('设备ID');
            $table->unsignedInteger('ip')
                ->default(0)
                ->comment('登录IP');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_login_logs');
    }
};
