<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_relations', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')
                ->primary();
            $table->unsignedBigInteger('parent_id')
                ->index()
                ->default(0)
                ->comment('上级用户ID');
            $table->tinyInteger('layer')
                ->default(1)
                ->comment('所在层级');
            $table->text('line')
                ->nullable()
                ->comment('血缘线');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_relations');
    }
};
