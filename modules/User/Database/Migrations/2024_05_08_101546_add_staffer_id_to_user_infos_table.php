<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_infos', function (Blueprint $table) {
            $table->unsignedBigInteger('staffer_id')
                ->nullable()
                ->index()
                ->comment('店员或者店长邀请id')
                ->after('user_id');
        });
    }

    public function down(): void
    {
        Schema::table('user_infos', function (Blueprint $table) {
            $table->dropColumn('staffer_id');
        });
    }
};
