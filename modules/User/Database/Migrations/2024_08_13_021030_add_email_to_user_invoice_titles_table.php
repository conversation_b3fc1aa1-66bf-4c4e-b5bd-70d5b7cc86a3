<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_invoice_titles', function (Blueprint $table) {
            $table->string('email')->nullable()->comment('Email地址');
        });
    }

    public function down(): void
    {
        Schema::table('user_invoice_titles', function (Blueprint $table) {
        });
    }
};
