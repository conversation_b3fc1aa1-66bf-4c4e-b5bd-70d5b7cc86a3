<?php

namespace Modules\User\Listeners;

use App\Models\Module;
use Modules\User\Events\DepartmentJoinCreated;
use Modules\User\Notifications\NewJoinNotification;

class DepartmentJoinCreatedListener extends UserBaseListener
{
    public function handle(DepartmentJoinCreated $event): void
    {
        if (Module::isEnabled('Notification')) {
            $users = $event->join->department->users()->permission('GET@api/user/departments/joins/list')->get();

            foreach ($users as $user) {
                $user->notify(new NewJoinNotification($event->join));
            }
        }
    }
}