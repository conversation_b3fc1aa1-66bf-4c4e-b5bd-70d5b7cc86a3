<?php

namespace Modules\User\Listeners;

use App\Events\UserCreatedEvent;
use Modules\User\Enums\IdentityChannel;
use Modules\User\Models\Identity;

class UserCreatedListener
{
    public function handle(UserCreatedEvent $event): void
    {
        $user = $event->user;

        $user->info()
            ->create([
                'nickname' => '用户'.sprintf('%04d', $user->id),
                'avatar'   => 'avatar/'.mt_rand(100, 174).'.jpg',
            ]);
        // 设置用户关联关系
        $user->relation()->create(['parent_id' => 0, 'layer' => 1, 'line' => '0,']);
        // 如果配置的上级，不是 0 ，修改顶点
        $parentId = config('user.DEFAULT_PARENT_ID');
        if ($parentId) {
            $user->relation->changeParent($parentId);
        }

        $identity = Identity::where('is_default', true)->first();
        $identity?->entry($user, IdentityChannel::REG);
    }
}