<?php

namespace Modules\User\Listeners;

use Exception;
use Modules\User\Events\Authenticated;

class LoginListener
{
    /**
     * @throws Exception
     */
    public function handle(Authenticated $event): void
    {
        $brandName  = app('user.device')->getBrandName();
        $brandModel = app('user.device')->getModel();
        $name       = sprintf('%s-%s', $brandName ?: 'Unknown', $brandModel ?: 'unknown');
        $deviceId   = request()->header('X-Device-Id');

        $device = $event->user->loginDevices()
            ->where('device_id', $deviceId)
            ->first();

        if (config('user.NEW_DEVICE') && ! $device) {
            throw new Exception('新设备登录', 416);
        }

        if (! $device) {
            $event->user->loginDevices()->create([
                'name'      => $name,
                'device_id' => $deviceId,
            ]);
        } else {
            $device->update([
                'updated_at' => now(),
            ]);
        }

        if (config('user.TOKEN_AUTO_REVOKE')) {
            $event->user->tokens()->where('name', $event->name)->delete();
        }
    }
}
