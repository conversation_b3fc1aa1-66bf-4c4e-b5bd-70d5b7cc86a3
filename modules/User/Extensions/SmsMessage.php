<?php

namespace Modules\User\Extensions;

use Overtrue\EasySms\Message;

abstract class SmsMessage extends Message
{
    protected string $code;

    public function __construct()
    {
        $length = config('user.sms.code_length');
        if (config('user.sms.debug')) {
            $this->code = str_repeat('0', $length);
        } else {
            $this->code = sprintf("%0{$length}d", rand(1, pow(10, $length) - 1));
        }

        parent::__construct();
    }

    /**
     * Notes   : 获取短信发送的频道，标记是在哪里用的
     *
     * @Date   : 2023/7/14 13:21
     * <AUTHOR> <Jason.C>
     * @return string
     */
    abstract public function getChannel(): string;

    /**
     * Notes   : 获取验证码
     *
     * @Date   : 2023/7/14 13:21
     * <AUTHOR> <Jason.C>
     * @return string
     */
    public function getCode(): string
    {
        return $this->code;
    }
}