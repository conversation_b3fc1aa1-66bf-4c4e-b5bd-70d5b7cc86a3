<?php

use Overtrue\EasySms\Strategies\OrderStrategy;

return [
    'debug'       => config('user.SMS_DEBUG', true),
    // 短信验证码长度
    'code_length' => config('user.SMS_LENGTH', 4),
    // channel 与 template_id
    'channel_map' => [
        'DEFAULT'  => config('user.SMS_TEMPLATE_DEFAULT'),
        'LOGIN'    => config('user.SMS_TEMPLATE_LOGIN'),
        'REGISTER' => config('user.SMS_TEMPLATE_REGISTER'),
        'FORGOT'   => config('user.SMS_TEMPLATE_FORGOT'),
        'PAYMENT'  => config('user.SMS_TEMPLATE_PAYMENT'),
    ],
    // HTTP 请求的超时时间（秒）
    'timeout'     => 5.0,
    // 默认发送配置
    'default'     => [
        // 网关调用策略，默认：顺序调用
        'strategy' => OrderStrategy::class,
        // 默认可用的发送网关
        'gateways' => [
            'aliyun',
        ],
    ],
    // 可用的网关配置
    'gateways'    => [
        'errorlog'     => [
            'file' => storage_path('logs/easy-sms.log'),
        ],
        // 阿里云 短信内容使用 template + data
        'aliyun'       => [
            'access_key_id'     => config('user.SMS_ALIYUN_ACCESS_KEY_ID'),
            'access_key_secret' => config('user.SMS_ALIYUN_ACCESS_KEY_SECRET'),
            'sign_name'         => config('user.SMS_ALIYUN_SIGN_NAME'),
        ],
        // 阿里云Rest 短信内容使用 template + data
        'aliyunrest'   => [
            'app_key'        => '',
            'app_secret_key' => '',
            'sign_name'      => '',
        ],
        // 阿里云国际 短信内容使用 template + data
        'aliyunintl'   => [
            'access_key_id'     => '',
            'access_key_secret' => '',
            'sign_name'         => '',
        ],
        // 云片 短信内容使用 content
        'yunpian'      => [
            'api_key'   => '',
            'signature' => '【默认签名】', // 内容中无签名时使用
        ],
        // Submail 短信内容使用 data
        'submail'      => [
            'app_id'  => '',
            'app_key' => '',
            'project' => '', // 默认 project，可在发送时 data 中指定
        ],
        // 螺丝帽 短信内容使用 content
        'luosimao'     => [
            'api_key' => '',
        ],
        // 容联云通讯 短信内容使用 template + data
        'yuntongxun'   => [
            'app_id'         => '',
            'account_sid'    => '',
            'account_token'  => '',
            'is_sub_account' => false,
        ],
        // 互亿无线 短信内容使用 content
        'huyi'         => [
            'api_id'    => '',
            'api_key'   => '',
            'signature' => '',
        ],
        // 聚合数据 短信内容使用 template + data
        'juhe'         => [
            'app_key' => '',
        ],
        // SendCloud 短信内容使用 template + data
        'sendcloud'    => [
            'sms_user'  => '',
            'sms_key'   => '',
            'timestamp' => false, // 是否启用时间戳
        ],
        // 百度云 短信内容使用 template + data
        'baidu'        => [
            'ak'        => '',
            'sk'        => '',
            'invoke_id' => '',
            'domain'    => '',
        ],
        // 华信短信平台 短信内容使用 content
        'huaxin'       => [
            'user_id'  => '',
            'password' => '',
            'account'  => '',
            'ip'       => '',
            'ext_no'   => '',
        ],
        // 融云 短信分为两大类，验证类和通知类短信。 发送验证类短信使用 template + data
        'rongcloud'    => [
            'app_key'    => '',
            'app_secret' => '',
        ],
        // 天毅无线 短信内容使用 content
        'tianyiwuxian' => [
            'username' => '', //用户名
            'password' => '', //密码
            'gwid'     => '', //网关ID
        ],
        // twilio 短信使用 content 发送对象需要 使用 + 添加区号
        'twilio'       => [
            'account_sid' => '', // sid
            'from'        => '', // 发送的号码 可以在控制台购买
            'token'       => '', // apitoken
        ],
        // tiniyo 短信使用 content 发送对象需要 使用 + 添加区号
        'tiniyo'       => [
            'account_sid' => '', // auth_id from https://tiniyo.com
            'from'        => '', // 发送的号码 可以在控制台购买
            'token'       => '', // auth_secret from https://tiniyo.com
        ],
        // 腾讯云 SMS 短信内容使用 template + data
        'qcloud'       => [
            'sdk_app_id' => '', // 短信应用的 SDK APP ID
            'secret_id'  => '', // SECRET ID
            'secret_key' => '', // SECRET KEY
            'sign_name'  => '腾讯CoDesign', // 短信签名
        ],
        // 阿凡达数据 短信内容使用 template + data
        'avatardata'   => [
            'app_key' => '', // APP KEY
        ],
        // 华为云 SMS 短信内容使用 template + data
        'huawei'       => [
            'endpoint'   => '', // APP接入地址
            'app_key'    => '', // APP KEY
            'app_secret' => '', // APP SECRET
            'from'       => [
                'default' => '**********', // 默认使用签名通道号
                'custom'  => 'csms12345', // 其他签名通道号 可以在 data 中定义 from 来指定
                'abc'     => 'csms67890', // 其他签名通道号
            ],
            'callback'   => '', // 短信状态回调地址
        ],
        // 网易云信 短信内容使用 template + data
        'yunxin'       => [
            'app_key'     => '',
            'app_secret'  => '',
            'code_length' => 4, // 随机验证码长度，范围 4～10，默认为 4
            'need_up'     => false, // 是否需要支持短信上行
        ],
        // 云之讯 短信内容使用 template + data
        'yunzhixun'    => [
            'sid'    => '',
            'token'  => '',
            'app_id' => '',
        ],
        // 凯信通 短信内容使用 content
        'kingtto'      => [
            'userid'   => '',
            'account'  => '',
            'password' => '',
        ],
        // 七牛云 短信内容使用 template + data
        'qiniu'        => [
            'secret_key' => '',
            'access_key' => '',
        ],
        // Ucloud 短信使用 template + data
        'ucloud'       => [
            'private_key' => '',    //私钥
            'public_key'  => '',    //公钥
            'sig_content' => '',    // 短信签名,
            'project_id'  => '',    //项目ID,子账号才需要该参数
        ],
        // 短信宝 短信使用 content
        'smsbao'       => [
            'user'     => '',    //账号
            'password' => '',   //密码
        ],
        // 摩杜云 短信使用 template + data
        'moduyun'      => [
            'accesskey' => '',  //必填 ACCESS KEY
            'secretkey' => '',  //必填 SECRET KEY
            'signId'    => '',  //选填 短信签名，如果使用默认签名，该字段可缺省
            'type'      => 0,   //选填 0:普通短信;1:营销短信
        ],
        // 融合云（助通）短信使用 template + data
        'rongheyun'    => [
            'username'  => '',  //必填 用户名
            'password'  => '',  //必填 密码
            'signature' => '',  //必填 已报备的签名
        ],
        // 蜘蛛云 短信使用 template + data
        'zzyun'        => [
            'user_id'   => '',    //必填 会员ID
            'secret'    => '',     //必填 接口密钥
            'sign_name' => '',   //必填 短信签名
        ],
        // 融合云信 短信使用 template + data
        'maap'         => [
            'cpcode' => '',    //必填 商户编码
            'key'    => '',     //必填 接口密钥
            'excode' => '',   //选填 扩展名
        ],
        // 天瑞云 短信内容使用 template + data
        'tinree'       => [
            'accesskey' => '', // 平台分配给用户的accesskey
            'secret'    => '', // 平台分配给用户的secret
            'sign'      => '', // 平台上申请的接口短信签名或者签名ID
        ],
        // 火山引擎 短信内容使用 template + data
        'volcengine'   => [
            'access_key_id'     => '', // 平台分配给用户的access_key_id
            'access_key_secret' => '', // 平台分配给用户的access_key_secret
            'region_id'         => 'cn-north-1', // 国内节点 cn-north-1，国外节点 ap-singapore-1，不填或填错，默认使用国内节点
            'sign_name'         => '', // 平台上申请的接口短信签名或者签名ID，可不填，发送短信时data中指定
            'sms_account'       => '', // 消息组账号,火山短信页面右上角，短信应用括号中的字符串，可不填，发送短信时data中指定
        ],
    ],
];
