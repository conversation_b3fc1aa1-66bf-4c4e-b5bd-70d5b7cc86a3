<?php

namespace Modules\User\Http\Renderable;

use App\Models\User;
use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Table;

class UserAccountTable extends LazyRenderable
{
    public function render(): string
    {
        $user = User::find($this->payload['key']);

        $user->initManyAccount([
            'usd',
            'balance',
            'coin',
        ]);

        $accountName = [
            'usd'     => '美元',
            'balance' => '人民币',
            'coin'    => 'BiToken',
        ];
        $accounts    = [
            'usd'     => $user->getCenterAccountAll('usd'),
            'balance' => $user->getCenterAccountAll('balance'),
            'coin'    => $user->getCenterAccountAll('coin'),
        ];
        $data        = [];

        foreach ($accounts as $account) {
            $data[] = [
                $accountName[$account['type']],
                $account['total'],
                $account['balance'] ?? 0,
                $account['freeze'],
            ];
        }

        return Table::make(['名称', '余额', '可用', '冻结'], $data)->render();
    }
}