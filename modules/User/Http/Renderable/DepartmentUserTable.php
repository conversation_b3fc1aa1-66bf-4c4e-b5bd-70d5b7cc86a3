<?php

namespace Modules\User\Http\Renderable;

use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use Modules\User\Models\UserDepartment;

class DepartmentUserTable extends LazyRenderable
{
    public function grid(): Grid
    {
        $departmentId = $this->payload['department_id'];
        $model        = UserDepartment::where('department_id', $departmentId)
            ->with(['user.info']);

        return Grid::make($model, function (Grid $grid) {
            $grid->quickSearch(['id', 'user.username', 'user.info.nickname']);
            $grid->disableActions();
            $grid->disableRowSelector();

            $grid->column('user.username', '用户名');
            $grid->column('user.info.nickname', '昵称');
            $grid->column('角色')
                ->display(fn() => $this->user->roles->pluck('name'))
                ->label();
            $grid->column('position', '头衔');
            $grid->column('created_at')
                ->dateFormat();
        });
    }
}