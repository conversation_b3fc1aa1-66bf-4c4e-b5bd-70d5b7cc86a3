<?php

namespace Modules\User\Http\Forms;

use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Modules\User\Models\UserIdentity;

class UpdateIdentitySerialForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $serial = $input['serial'];
        $info   = UserIdentity::find($this->payload['id']);

        $info->serial = $serial;
        $info->save();

        return $this->response()->success('编号更新成功')->refresh();
    }

    public function form(): void
    {
        $info = UserIdentity::find($this->payload['id']);
        $max  = UserIdentity::where('identity_id', $info->identity_id)->max('serial');

        $this->text('serial', '新会员编号')
            ->rules([
                'required',
                'integer',
                'min:1',
                "max:$max",
                "unique:\Modules\User\Models\UserIdentity,serial,$info->identity_id,identity_id",
            ])
            ->required()
            ->help('当前编号不包含前缀和编号长度，新编号不能大于当前最大编号');
        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }

    public function default(): array
    {
        $info = UserIdentity::find($this->payload['id']);

        return [
            'serial' => $info->serial,
        ];
    }

    protected function authorize($user): bool
    {
        $info = UserIdentity::find($this->payload['id']);

        if (! $info->identity->can_subscribe || ! $info->identity->serial_open) {
            return false;
        }

        return true;
    }
}