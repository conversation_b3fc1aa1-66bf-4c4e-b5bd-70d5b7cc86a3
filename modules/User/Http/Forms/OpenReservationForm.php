<?php

namespace Modules\User\Http\Forms;

use App\Models\User;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Exception;

class OpenReservationForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $user = User::find($this->payload['userId']);

        try {
            $user->reservation()->updateOrCreate([
                'user_id' => $user->id,
            ], [
                'name'     => $input['name'],
                'username' => $input['username'],
                'mobile'   => $input['mobile'],
                'address'  => $input['address'],
                'status'   => $input['status'],
            ]);
            return $this->response()->success('调整预约信息成功')->refresh();
        } catch (Exception $exception) {
            return $this->response()->error($exception->getMessage());
        }
    }

    public function form(): void
    {
        $this->text('name', '名称')
            ->required()
            ->rules(['max:50'], ['max' => '名称最多:max个字符']);
        $this->text('username', '联系人')
            ->required()
            ->rules(['max:10'], ['max' => '联系人最多:max个字符']);
        $this->mobile('mobile', '联系方式')
            ->required()
            ->rules(['max:11'], ['max' => '联系方式最多:max个字符']);
        $this->text('address', '详细地址')
            ->required()
            ->rules(['max:200'], ['max' => '详细地址最多:max个字符']);
        $this->switch('status', '是否可用')->default(1);
    }

    public function default(): array
    {
        $user = User::find($this->payload['userId']);

        return [
            'name'     => $user->reservation->name ?? '',
            'username' => $user->reservation->username ?? '',
            'mobile'   => $user->reservation->mobile ?? '',
            'address'  => $user->reservation->address ?? '',
            'status'   => $user->reservation->status ?? 1,
        ];
    }
}