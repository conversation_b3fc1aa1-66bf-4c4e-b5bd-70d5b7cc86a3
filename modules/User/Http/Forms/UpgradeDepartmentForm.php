<?php

namespace Modules\User\Http\Forms;

use App\Models\User;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Exception;
use Modules\User\Models\Department;

class UpgradeDepartmentForm extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * @throws \Exception
     */
    public function handle(array $input): JsonResponse
    {
        if (! $input['department_id']) {
            return $this->response()->error('部门必须选择');
        }
        $user       = User::find($this->payload['userId']);
        $department = Department::find($input['department_id']);

        if (config('user.CAN_HAS_MANY_DEPARTMENT')) {
            foreach ($department as $item) {
                if (! $item->allow_user) {
                    throw new Exception(sprintf('部门[%s]不允许有直接用户', $item->name));
                }
            }
        } elseif (! $department->allow_user) {
            return $this->response()->error(sprintf('[%s]不允许有直接用户', $department->name));
        }
        $user->departments()->syncWithPivotValues($input['department_id'], ['position' => $input['position']]);

        return $this->response()->success('用户部门调整成功')->refresh();
    }

    public function form(): void
    {
        if (config('user.CAN_HAS_MANY_DEPARTMENT')) {
            $this->multipleSelect('department_id', '所处部门')
                ->options(Department::selectOptions(null, '请选择部门...'))
                ->required();
        } else {
            $this->select('department_id', '所处部门')
                ->required()
                ->options(Department::selectOptions(null, '请选择部门...'));
            $this->text('position', '头衔')
                ->required();
        }
        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }

    public function default(): array
    {
        $user = User::find($this->payload['userId']);

        if (config('user.CAN_HAS_MANY_DEPARTMENT')) {
            return [
                'department_id' => $user->departments->pluck('id'),
            ];
        } else {
            $deps = $user->departments()->first();

            return [
                'department_id' => $deps?->id,
                'position'      => $deps?->pivot->position,
            ];
        }
    }
}