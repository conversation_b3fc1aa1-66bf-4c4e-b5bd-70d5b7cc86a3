<?php

namespace Modules\User\Http\Forms;

use App\Enums\ApplyStatus;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Modules\User\Models\Department;
use Modules\User\Models\DepartmentApply;

class DepartmentAuditForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $apply = DepartmentApply::find($this->payload['apply_id']);

        $res = match ($input['result']) {
            ApplyStatus::PASS->value => $apply->doPass(Admin::user(), $input['parent_id'], $input['code']),
            ApplyStatus::REJECT->value => $apply->doReject(Admin::user(), $input['reject_reason']),
        };

        if ($res) {
            return $this->response()->success('操作成功')->refresh();
        } else {
            return $this->response()->error('操作失败');
        }
    }

    public function form(): void
    {
        $this->text('user', '申请用户')
            ->readOnly();
        $this->text('name', '部门名称')
            ->readOnly();
        $this->text('type', '申请类型')
            ->readOnly();
        $this->textarea('apply_text', '申请说明')
            ->readOnly();
        $this->text('created_at', '申请时间')
            ->readOnly();
        $this->radio('result', '审核结果')
            ->options([
                ApplyStatus::PASS->value   => '审核通过',
                ApplyStatus::REJECT->value => '驳回申请',
            ])
            ->default(ApplyStatus::PASS->value)
            ->required()
            ->when(ApplyStatus::REJECT->value, function () {
                $this->textarea('reject_reason', '驳回原因');
            })
            ->when(ApplyStatus::PASS->value, function () {
                $this->select('parent_id', '上级部门')
                    ->options(Department::selectOptions(null, '请选择部门...'));
                $this->text('code', '部门编号')
                    ->rules('unique:Modules\User\Models\Department,code');
            });
        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }

    public function default(): array
    {
        $apply = DepartmentApply::find($this->payload['apply_id']);

        return [
            'user'       => $apply->user->showName,
            'name'       => $apply->name,
            'type'       => $apply->type,
            'apply_text' => $apply->apply_text,
            'created_at' => $apply->created_at,
        ];
    }
}