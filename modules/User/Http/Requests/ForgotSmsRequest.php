<?php

namespace Modules\User\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Modules\User\Rules\CanSmsRule;
use Modules\User\Rules\MobileRule;

class ForgotSmsRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'mobile' => [
                'required',
                'exists:users,username',
                'bail',
                new MobileRule(),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'mobile.required' => '手机号必须填写',
            'mobile.exists'   => '账号不存在',
        ];
    }
}