<?php

namespace Modules\User\Http\Requests\Lover;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\User\Enums\LoveTag;
use Modules\User\Rules\MobileRule;

class UpdateTagRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'tag' => [
                'required',
                'bail',
                Rule::in(LoveTag::values()),
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'tag.required'       => '请选择标签',
            'tag.in'             => '标签不正确',
        ];
    }
}
