<?php

namespace Modules\User\Http\Requests\Auth;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\Password;
use Modules\User\Rules\MobileRule;

class AutoLoginRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'username' => [
                'required',
                'bail',
                new MobileRule(),
            ],
            'password' => [
                'nullable',
                'bail',
                config('user.STRONG_PASSWORD') ? Password::min(8)->mixedCase()->numbers() : Password::min(6),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'username.required' => '手机号码必须填写',
            'username.exists'   => '账户不存在',
        ];
    }
}
