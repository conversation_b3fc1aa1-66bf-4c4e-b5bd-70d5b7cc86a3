<?php

namespace Modules\User\Http\Actions\Identity;

use Dcat\Admin\Grid\RowAction;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Models\Payment;
use Modules\User\Models\IdentityOrder;

class OrderPaidAction extends RowAction
{
    protected string $title = '确认支付';

    public function handle()
    {
        $order   = IdentityOrder::find($this->getKey());
        $payment = Payment::createPayment($order->user,
            $order,
            Gateway::SYSTEM->value,
            Channel::SYSTEM->value);
        $payment->paid(now());
        //        $order->paid();
        return $this->response()->success('支付成功')->refresh();
    }

    public function confirm()
    {
        return ' 确认支付？';
    }
}