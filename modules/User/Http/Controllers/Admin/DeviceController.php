<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\User\Models\LoginDevice;

class DeviceController extends AdminController
{
    protected string $title = '登录设备';

    public function grid(): Grid
    {
        return Grid::make(LoginDevice::with(['user.info'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableBatchActions();
            $grid->disableRowSelector();
            $grid->disableActions();

            $grid->quickSearch(['id', 'user.username', 'user.info.nickname', 'name', 'device_id', 'hash'])
                ->placeholder('ID/用户名/用户昵称/设备名称/设备识别码/设备哈希');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->equal('name', '设备名称');
                $filter->equal('device_id', '设备识别码');
                $filter->equal('id_card_no', '设备哈希');
                $filter->equal('disable', '禁止登录')
                    ->radio([
                        0 => '否',
                        1 => '是'
                    ]);
                $filter->between('created_at', '首次登录时间')
                    ->datetime();
                $filter->between('updated_at', '最后登录时间')
                    ->datetime();
            });

            $grid->column('id');
            $grid->column('登录用户')
                ->display(fn() => $this->user->showName);
            $grid->column('name', '设备名称');
            $grid->column('device_id', '设备识别码');
            $grid->column('hash', '设备哈希');
            $grid->column('disable', '禁止登录')
                ->bool();
            $grid->column('created_at', '首次登录时间');
            $grid->column('updated_at', '最后登录时间');
        });
    }
}