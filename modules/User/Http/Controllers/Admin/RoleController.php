<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\User\Http\Renderable\PermissionTable;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleController extends AdminController
{
    protected string $title = '角色';

    public function grid(): Grid
    {
        return Grid::make(Role::withCount(['users', 'permissions'])->latest(), function (Grid $grid) {
            $grid->quickSearch(['name'])
                ->placeholder('角色名称');

            $grid->column('id');
            $grid->column('name', '角色名称');
            $grid->column('users_count', '用户量');
            $grid->column('permissions_count', '权限量');
            $grid->column('created_at')
                ->dateFormat();
        });
    }

    public function form(): Form
    {
        return Form::make(Role::with(['permissions']), function (Form $form) {
            $form->text('name', '角色名称')
                ->required();

//            $form->multipleSelectTable('permissions', '角色权限')
//                ->title('角色权限')
//                ->from(PermissionTable::make())
//                ->options(function ($v) {
//                    if (! $v) {
//                        return [];
//                    }
//                    return Permission::find($v)->pluck('name', 'id');
//                })
//                ->customFormat(function ($paths) {
//                    return array_column($paths, 'id');
//                });
        });
    }

    public function ajax(Request $request): LengthAwarePaginator
    {
        $q = $request->get('q');

        return Role::where('name', 'like', "%$q%")
            ->paginate(10, ['id', 'name as text']);
    }
}