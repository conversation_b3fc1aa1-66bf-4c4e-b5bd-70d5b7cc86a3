<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\User\Http\Actions\User\InitPermissionTable;
use Spatie\Permission\Models\Permission;

class PermissionController extends AdminController
{
    protected string $title = '权限';

    public function grid(): Grid
    {
        return Grid::make(Permission::latest(), function (Grid $grid) {
            $grid->disableBatchDelete(false);

            $grid->tools([new InitPermissionTable]);

            $grid->quickSearch(['name', 'desc']);

            $grid->column('desc', '功能')
                ->editable();
            $grid->column('name', '路由');
            $grid->column('created_at')
                ->dateFormat();
        });
    }

    public function form(): Form
    {
        return Form::make(Permission::class, function (Form $form) {
            $form->text('desc', '功能')
                ->required();
            $form->text('name', '路由')
                ->required();
        });
    }
}