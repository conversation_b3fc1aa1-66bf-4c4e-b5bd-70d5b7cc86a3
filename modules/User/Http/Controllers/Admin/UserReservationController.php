<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\User\Models\UserReservation;

class UserReservationController extends AdminController
{
    protected string $title = '预约商用户管理';

    public function grid(): Grid
    {
        return Grid::make(UserReservation::with(['user'])->orderByDesc('created_at'), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户账号');
                $filter->like('name', '名称');
                $filter->like('contacts', '联系人');
                $filter->like('mobile', '联系方式');
                $filter->equal('status', '状态')->radio([
                    ''  => '全部',
                    '1' => '启用',
                    '0' => '禁用',
                ]);
            });
            $grid->column('user.username', '用户账号')->display(fn() => $this->user->showAllName);
            $grid->column('name', '名称');
            $grid->column('contacts', '联系人');
            $grid->column('mobile', '联系方式');
            $grid->column('status', '状态')->switch();
        });
    }

    public function form(): Form
    {
        return Form::make(new UserReservation(), function (Form $form) {
            $form->switch('status');
        });
    }

}