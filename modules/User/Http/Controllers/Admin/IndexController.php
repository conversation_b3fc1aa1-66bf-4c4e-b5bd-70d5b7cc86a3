<?php

namespace Modules\User\Http\Controllers\Admin;

use App\Admin\Traits\WithUploads;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\Password;
use Modules\User\Enums\Gender;
use Modules\User\Http\Actions\Identity\UpgradeIdentity;
use Modules\User\Http\Actions\User\BatchLock;
use Modules\User\Http\Actions\User\BatchUnLock;
use Modules\User\Http\Actions\User\Lock;
use Modules\User\Http\Actions\User\UnLock;
use Modules\User\Http\Actions\User\UpgradeParent;
use Modules\User\Models\Identity;
use Modules\User\Rules\MobileRule;

class IndexController extends AdminController
{
    use WithUploads;

    protected string $title = '用户';

    public function grid(): Grid
    {
        $model = User::with(['info', 'relation.parent.info', 'identities', 'wechat'])->latest();

        return Grid::make($model, function (Grid $grid) {
            $grid->disableDeleteButton();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->append(new UpgradeIdentity);
                $actions->append(new UpgradeParent);
//                $actions->append(new UpgradeDepartment);
//                $actions->append(new UpgradeRole);
//                $actions->append(new OpenReservation());

                $actions->append(new Grid\Tools\ActionDivider());
                if ($actions->row->is_lock) {
                    $actions->append(new UnLock);
                } else {
                    $actions->append(new Lock);
                }
            });

            $grid->batchActions([new BatchLock, new BatchUnLock]);
            $grid->quickSearch(['id', 'username', 'info.nickname'])
                ->placeholder('ID/手机号码/用户昵称');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('username', '手机号码');
                $filter->like('info.nickname', '用户昵称');
                $filter->like('email', '邮箱');
                $filter->like('center_key', '用户中心KEY');
                $filter->equal('is_lock', '锁定状态')->select([
                    0 => '正常',
                    1 => '锁定',
                ]);
                $filter->like('relation.parent.username', '上级用户');
                $filter->equal('identities.id', '用户身份')
                    ->select(Identity::pluck('name', 'id'));
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->column('id', 'UID');
            $grid->column('info.avatar', '头像')->image('', 40);
            $grid->column('username', '账号');
            $grid->column('info.nickname', '用户昵称');
            $grid->column('relation.parent.info.nickname', '上级用户')
                ->display(fn(
                ) => ($this->relation->parent->info->nickname ?? '').': '.($this->relation->parent->username ?? ''))
                ->label('info');
            $grid->column('identities', '身份')
                ->display(function () {
                    return $this->identities->map(function ($identity) {
                        if ($identity->serial_open) {
                            return $identity->name.': '.$identity->pivot->serial_no.'</br>';
                        } else {
                            return $identity->name;
                        }
                    });
                })
                ->label();
//            $grid->column('departments', '部门')
//                ->display(function () {
//                    return $this->departments->map(function ($department) {
//                        return $department->name.': '.$department->pivot->position;
//                    });
//                })
//                ->label('danger');

            $grid->column('is_lock', '锁定')
                ->bool();
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(User::with(['info']), function (Form $form) {
            $form->column(8, function (Form $form) {
                if ($form->isCreating()) {
                    $form->text('username', '手机号码')
                        ->required()
                        ->maxLength(11)
                        ->creationRules([
                            'required',
                            'size:11',
                            new MobileRule(),
                            'unique:App\Models\User',
                        ]);
                } else {
                    $form->text('username', '手机号码')
                        ->required()
                        ->readOnly();
                }
                if (config('user.STRONG_PASSWORD')) {
                    $form->password('password')
                        ->creationRules([
                            'required',
                            Password::min(8)->mixedCase()->numbers(),
                        ])
                        ->updateRules([
                            'nullable',
                            Password::min(8)->mixedCase()->numbers(),
                        ])
                        ->help('8位 大写+小写字母+数字');
                } else {
                    $form->password('password')
                        ->creationRules([
                            'required',
                            'min:6,max:20',
                        ])
                        ->updateRules('nullable|min:6,max:20');
                }

                $form->divider('用户信息');

                $form->text('info.nickname', '昵称')
                    ->required();
//                $form->text('email', '邮箱');
                $form->textarea('info.description', '简介');
                $form->radio('info.gender', '性别')
                    ->options(Gender::GENDER_MAP)
                    ->default(Gender::SECRET->value);
                $this->cover($form, 'info.avatar', '头像');
                $form->date('info.birthday', '出生日期');
            });
        });
    }

    public function ajax(Request $request): array
    {
        $q = $request->get('q');

        $users = User::where('username', 'like', "%$q%")
            ->orWhere('id', $q)
            ->orWhereHas('info', function ($query) use ($q) {
                $query->where('nickname', 'like', "%$q%");
            })
            ->paginate(10)
            ->map(function (User $user) {
                return [
                    'id'   => $user->getKey(),
                    'text' => $user->showName,
                ];
            });

        return [
            'data' => $users,
        ];
    }
}
