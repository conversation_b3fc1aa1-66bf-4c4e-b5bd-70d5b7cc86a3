<?php

namespace Modules\User\Http\Controllers\Api\Department;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\User\Http\Requests\DepartmentApplyRequest;
use Modules\User\Http\Resources\DepartmentApplyCollection;
use Modules\User\Models\DepartmentApply;

class ApplyController extends ApiController
{
    /**
     * Notes   : 我的申请
     *
     * @Date   : 2023/9/7 13:05
     * <AUTHOR> <Jason.C>
     */
    public function index(): JsonResponse
    {
        $list = DepartmentApply::ofUser(Api::user())
            ->select(['id', 'name', 'apply_text', 'apply_status', 'reject_reason', 'created_at'])
            ->paginate();

        return $this->success(new DepartmentApplyCollection($list));
    }

    /**
     * Notes   : 保存申请
     *
     * @Date   : 2023/9/7 13:18
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Http\Requests\DepartmentApplyRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(DepartmentApplyRequest $request): JsonResponse
    {
        DepartmentApply::create([
            'user_id'    => Api::id(),
            'name'       => $request->safe()->name,
            'type'       => $request->safe()->type,
            'apply_text' => $request->safe()->apply_text,
        ]);

        return $this->success('申请提交成功，请等待审核。');
    }
}