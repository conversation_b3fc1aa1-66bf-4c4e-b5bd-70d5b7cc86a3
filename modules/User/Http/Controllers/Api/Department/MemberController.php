<?php

namespace Modules\User\Http\Controllers\Api\Department;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use App\Models\User;
use Modules\User\Http\Resources\DepartmentUserCollection;
use Modules\User\Models\Department;

class MemberController extends ApiController
{
    public function index(Department $department)
    {
        $depIds = Api::user()->departments->pluck('id')->toArray();
        if (! in_array($department->getKey(), $depIds)) {
            return $this->failed('无权操作');
        }

        return $this->success(new DepartmentUserCollection($department->users()->paginate()));
    }

    public function remove(Department $department, User $user)
    {
        $depIds = Api::user()->departments->pluck('id')->toArray();
        if (! in_array($department->getKey(), $depIds)) {
            return $this->failed('无权操作');
        }

        $department->users()->detach($user);
        return $this->success();
    }
}