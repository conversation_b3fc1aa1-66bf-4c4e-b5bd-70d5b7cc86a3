<?php

namespace Modules\User\Http\Controllers\Api\User;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\Request;

class VipCashController extends ApiController
{
    /**
     *  - 'develop': 开发版;
     *  - 'trial': 体验版;
     *  - 'release': 正式版;
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Api::user();
        return $this->success([
            'can'      => false,
            'user_id'  => $user->id,
            'message'  => '请前往绑定您的会员信息.',
            'union_id' => $user->getUnionId(),
            'version'  => 'trial',
        ]);
    }
}
