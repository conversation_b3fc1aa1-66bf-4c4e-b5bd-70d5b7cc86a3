<?php

namespace Modules\User\Http\Controllers\Api\Auth;

use App\Http\Controllers\ApiController;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\User\Events\Authenticated;
use Modules\User\Http\Resources\LoginResource;
use Vinkla\Hashids\Facades\Hashids;

class AuthController extends ApiController
{
    /**
     * Notes   : 登录后续操作，返回token，如果要有其他的返回数据，可以在此处修改
     *
     * @Date   : 2023/8/10 09:41
     * <AUTHOR> <Jason.C>
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\JsonResponse
     */
    protected function afterLogin(User $user): JsonResponse
    {
        Authenticated::dispatch($user, config('user.TOKEN_NAME'));

        $token = $user->createToken(config('user.TOKEN_NAME'));

        return $this->success(new LoginResource($token, $user->wasRecentlyCreated, $user->identities));
    }

    /**
     * Notes: 解码推荐人
     *
     * @Author: 玄尘
     * @Date: 2023/8/30 18:10
     * @param  \Illuminate\Http\Request  $request
     * @return int
     * @throws \Exception
     */
    protected function parseParentIdFromRequest(Request $request): int
    {
        $code = $request->invite;

        if (! $code) {
            return 0;
        }

        $invite = Hashids::connection('code')->decode($code);

        if (empty($invite)) {
            throw  new Exception('邀请码不正确');
        }

        $parentId   = $invite[0];
        $parentUser = User::find($parentId);

        if (! $parentUser) {
            throw new Exception('推荐人不存在');
        }

        return $parentId;
    }
}