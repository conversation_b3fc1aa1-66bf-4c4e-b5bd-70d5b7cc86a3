<?php

namespace Modules\User\Http\Controllers\Api\Auth;

use App\Models\User;
use App\Packages\XinHuaERP\XinHuaERP;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Modules\User\Http\Requests\Auth\AutoLoginRequest;

class AutoController extends AuthController
{
    /**
     * Notes   : 使用【用户名、密码】注册或登录
     *
     * @Date   : 2023/8/10 10:17
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Http\Requests\Auth\AutoLoginRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function password(AutoLoginRequest $request): JsonResponse
    {
        $username = $request->safe()->username;
        $password = $request->safe()->password;

        $user = User::where('username', $username)->first();

        if ($user) {
            if ($user->is_lock) {
                return $this->failed('用户被禁止登录');
            }
            if (Hash::check($password, $user->password)) {
                return $this->afterLogin($user);
            } else {
                return $this->failed('密码不正确');
            }
        } else {
            $user = User::create([
                'username' => $username,
                'password' => $password,
                'is_lock'  => false,
            ]);
            return $this->afterLogin($user);
        }
    }

    /**
     * Notes   : 使用【短信验证码】注册或登录
     *
     * @Date   : 2023/8/10 10:18
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Http\Requests\Auth\AutoLoginRequest  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function sms(AutoLoginRequest $request): JsonResponse
    {
        $username = $request->safe()->username;
        $password = $request->password;

        $user = User::where('username', $username)->first();
        if (! $user) {
            $user = User::create([
                'username' => $username,
                'password' => $password,
            ]);
        }
        if ($user->is_lock) {
            return $this->failed('用户被禁止登录');
        }

        $parentId = $this->parseParentIdFromRequest($request);

        if ($user->wasRecentlyCreated && $parentId > 0 && $parentId != $user->id) {
            $user->relation->changeParent($parentId);
        } else {
            if ($unionId = $user->getUnionId()) {
                XinHuaERP::user()->addVisitor($unionId);
            }
        }
        return $this->afterLogin($user);
    }
}