<?php

namespace Modules\User\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Modules\User\Models\Sms;

class CheckSms
{
    /**
     * @throws \Exception
     */
    public function handle(Request $request, Closure $next, string $channel = '')
    {
        $username = $request->username;
        $smsId    = $request->sms_id;
        $verify   = $request->verify;

        $sms = Sms::where('id', $smsId)->where('channel', $channel)->first();

        if (! $sms || $sms->used) {
            throw new Exception(lecho('The verification code is incorrect'), 422);
        }

        if ($sms->mobile != $username) {
            throw new Exception('验证码不正确.', 422);
        }

        if (now()->diffInMinutes($sms->created_at) > config('user.SMS_EXPIRE_TIME', 5)) {
            throw new Exception(lecho('Verification has expired'), 422);
        }

        if ($sms->content !== $verify) {
            throw new Exception(lecho('The verification code is incorrect'), 422);
        }
        $sms->used = ! config('user.SMS_DEBUG');
        $sms->save();
        return $next($request);
    }
}
