<?php

namespace Modules\User\Http\Resources;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\JsonResource;

class LoginResource extends JsonResource
{
    public function __construct($resource, protected bool $isNew, protected Collection $identities)
    {
        parent::__construct($resource);
    }

    public function toArray($request): array
    {
        return [
            'token'       => $this->resource->plainTextToken,
            'type'        => 'Bearer',
            'is_new'      => $this->isNew,
            'identity_id' => $this->identities->first()?->id,
        ];
    }
}
