<?php

namespace Modules\User\Http\Resources;

use App\Http\Resources\BaseCollection;
use Illuminate\Http\Request;

class UserVipCardCollection extends BaseCollection
{
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return [
                    'card_id'    => $item->id,
                    'no'         => $item->no,
                    'name'       => $item->batch->name,
                    'amount'     => $item->amount,
                    'identity'   => $item->identityModel?->name,
                    'updated_at' => $item->updated_at->toDateTimeString(),
                ];
            }),
            'page' => $this->page(),
        ];
    }
}