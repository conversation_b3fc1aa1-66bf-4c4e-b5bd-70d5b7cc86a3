<?php

namespace Modules\User\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DepartmentBaseResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'department_id' => $this->id,
            'name'          => $this->name,
            'created_at'    => (string) $this->created_at,
        ];
    }
}