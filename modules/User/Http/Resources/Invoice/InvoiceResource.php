<?php

namespace Modules\User\Http\Resources\Invoice;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InvoiceResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'invoice_id'   => $this->id,
            'channel'      => [
                'key'   => $this->channel,
                'value' => $this->channel_text,
            ],
            'type'         => [
                'key'   => $this->type,
                'value' => $this->type_text,
            ],
            'title'        => $this->title,
            'email'        => $this->email,
            'no'           => $this->no,
            'account_bank' => $this->account_bank,
            'account_no'   => $this->account_no,
            'reg_mobile'   => $this->reg_mobile,
            'province_id'  => $this->province_id,
            'province'     => $this->province?->name,
            'city_id'      => $this->city_id,
            'city'         => $this->city?->name,
            'district_id'  => $this->district_id,
            'district'     => $this->district?->name,
            'full_address' => $this->full_address,
            'address'      => $this->address,
            'default'      => $this->default,
        ];
    }
}
