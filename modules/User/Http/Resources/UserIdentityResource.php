<?php

namespace Modules\User\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserIdentityResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'identity_id' => $this->id,
            'name'        => $this->name,
            'description' => $this->description,
            'cover'       => $this->cover_url,
            'started_at'  => (string) $this->pivot->started_at,
            'ended_at'    => (string) $this->pivot->ended_at,
            'created_at'  => (string) $this->pivot->created_at,
            'serial'      => $this->pivot->serial_no,
            'is_unique'   => $this->is_unique,
        ];
    }
}