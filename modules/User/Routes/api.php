<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Modules\User\Http\Controllers\Api\Auth\AutoController;
use Modules\User\Http\Controllers\Api\Auth\ForgotController;
use Modules\User\Http\Controllers\Api\Auth\LoginController;
use Modules\User\Http\Controllers\Api\Auth\RegisterController;
use Modules\User\Http\Controllers\Api\Auth\SmsController;
use Modules\User\Http\Controllers\Api\Department\ApplyController;
use Modules\User\Http\Controllers\Api\Department\DepartmentController;
use Modules\User\Http\Controllers\Api\Department\JoinController;
use Modules\User\Http\Controllers\Api\Department\MemberController;
use Modules\User\Http\Controllers\Api\Department\PermissionController;
use Modules\User\Http\Controllers\Api\Identity\IdentityController;
use Modules\User\Http\Controllers\Api\Invoice\InvoiceController;
use Modules\User\Http\Controllers\Api\Safety\DeviceController;
use Modules\User\Http\Controllers\Api\Sign\SignController;
use Modules\User\Http\Controllers\Api\User\CardController;
use Modules\User\Http\Controllers\Api\User\InfoController;
use Modules\User\Http\Controllers\Api\User\InviteController;
use Modules\User\Http\Controllers\Api\User\LoverController;
use Modules\User\Http\Controllers\Api\User\OcrController;
use Modules\User\Http\Controllers\Api\User\PasswordController;
use Modules\User\Http\Controllers\Api\User\VipCashController;

Route::group([
    'as'        => 'auth.',
    'namespace' => 'Auth',
    'prefix'    => 'auth',
], function (Router $router) {
    # 用户名密码登录
    $router->post('login', [LoginController::class, 'password']);
    $router->post('login/sms', [LoginController::class, 'sms'])->middleware('check-sms:LOGIN');
    # 账号密码注册
    $router->post('register', [RegisterController::class, 'password']);
    $router->post('register/sms', [RegisterController::class, 'sms'])->middleware('check-sms:REGISTER');
    # 注册和登录都可以
    $router->post('auto', [AutoController::class, 'password']);
    $router->post('auto/sms', [AutoController::class, 'sms'])->middleware('check-sms:DEFAULT');
    # 发送短信
    $router->post('sms', [SmsController::class, 'index'])->middleware('throttle:sms')->name('sms');
    $router->post('forgot/sms', [ForgotController::class, 'index'])->middleware('throttle:sms');
    $router->post('forgot/password', [ForgotController::class, 'password'])->middleware('check-sms:FORGOT');
});

Route::group([
    'middleware' => 'auth:sanctum',
    'namespace'  => 'Sign',
    'prefix'     => 'sign',
], function (Router $router) {
    $router->get('index', [SignController::class, 'index']);
    $router->post('sign', [SignController::class, 'sign']);
});

Route::group([
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->post('logoff/sms', [InfoController::class, 'logSms'])->middleware('throttle:sms');
    $router->post('logoff', [InfoController::class, 'logOff'])->middleware('check-forgot-sms:DEFAULT');
    $router->get('safe', [InfoController::class, 'safe']);

    $router->get('invoice/init', [InvoiceController::class, 'init']);
    $router->get('invoice/lists', [InvoiceController::class, 'lists']);
    $router->apiResource('invoice', InvoiceController::class);
});

Route::group([
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    # 身份证识别
    $router->post('ocr/id_card', [OcrController::class, 'idCard']);
    # 邀请码，绑定
    $router->get('vip/cash', [VipCashController::class, 'index']);
    $router->get('invite', [InviteController::class, 'index']);
    $router->post('bind', [InviteController::class, 'bind']);
    $router->get('parent', [InviteController::class, 'parent']);
    $router->get('slaves', [InviteController::class, 'slaves']);
});

Route::group([
    'middleware' => 'auth:sanctum',
    'prefix'     => 'safety',
], function (Router $router) {
    $router->get('devices', [DeviceController::class, 'devices']);
    $router->delete('devices/{device}', [DeviceController::class, 'delete']);
    $router->get('logs', [DeviceController::class, 'logs']);
});

Route::group([
    'middleware' => 'auth:sanctum',
    'prefix'     => 'identities',
], function (Router $router) {
    $router->get('', [IdentityController::class, 'index']);
    $router->get('{identity}', [IdentityController::class, 'show']);
    $router->post('{identity}/subscribe', [IdentityController::class, 'subscribe']);
});

Route::group([
    'middleware' => 'auth:sanctum',
    'prefix'     => 'departments',
], function (Router $router) {
    $router->get('permissions', [PermissionController::class, 'index']);

    $router->get('applies', [ApplyController::class, 'index']);
    $router->post('applies', [ApplyController::class, 'store']);

    # 获取加入部门的申请
    $router->get('joins', [JoinController::class, 'index']);
    $router->post('joins', [JoinController::class, 'store']);

    $router->get('joins/list', [JoinController::class, 'list'])->middleware(['check-permission']);
    $router->put('joins/{join}', [JoinController::class, 'audit'])->middleware(['check-permission']);
    # 人员管理
    $router->get('{department}/members', [MemberController::class, 'index'])->middleware(['check-permission']);
    $router->delete('{department}/members/{user}',
        [MemberController::class, 'remove'])->middleware(['check-permission']);

    $router->get('', [DepartmentController::class, 'index']);
    $router->get('{department}', [DepartmentController::class, 'show']);
    $router->put('{department}', [DepartmentController::class, 'update'])->middleware(['check-permission']);
});

Route::group([
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->get('info', [InfoController::class, 'index']);
    $router->get('pc', [InfoController::class, 'pc']);
    $router->get('pc_user_other', [InfoController::class, 'pcUserOther']);
    $router->get('setting/init', [InfoController::class, 'settingInit']);
    $router->put('info', [InfoController::class, 'update']);
});

Route::group([
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->post('setting/forgot_sms', [PasswordController::class, 'sms']);
    $router->put('setting/password', [PasswordController::class, 'login'])->middleware('check-forgot-sms:FORGOT');
    $router->put('setting/security', [PasswordController::class, 'security'])->middleware('check-forgot-sms:FORGOT');
});

Route::group([
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->post('card/activate/send', [CardController::class, 'activateSend']);
    $router->post('card/activate', [CardController::class, 'activate']);
    $router->post('card/result', [CardController::class, 'result']);
    $router->get('card/lists', [CardController::class, 'lists']);

    $router->post('card/temp', [CardController::class, 'temp']);
});

Route::group([
    'prefix'     => 'lover',
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->get('tags', [LoverController::class, 'tags']);
    $router->get('{lover}', [LoverController::class, 'show']);

    $router->get('', [LoverController::class, 'index']);
    $router->post('', [LoverController::class, 'store'])->middleware('check-sms:DEFAULT');
    $router->post('{lover}/tag', [LoverController::class, 'updateTag']);
    $router->delete('{lover}', [LoverController::class, 'remove']);
});
