<?php

namespace Modules\User\Rules;

use App\Facades\Api;
use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Vinkla\Hashids\Facades\Hashids;

class BindInviteCodeRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (strlen($value) == 8) {
            $code = Hashids::connection('invite')->decode($value);
            if (blank($code)) {
                $fail('邀请码不正确');
                return;
            }
            if (Api::id() == $code[0]) {
                $fail('不能绑定自己的邀请码');
                return;
            }
            if (! User::where('id', $code[0])->exists()) {
                $fail('邀请码不正确');
            }
        } elseif (strlen($value) == 11) {
            $parent = User::where('username', $value)->first();
            if (! $parent) {
                $fail('邀请码不正确');
                return;
            }
            if (Api::id() == $parent->id) {
                $fail('不能绑定自己的邀请码');
            }
        }
    }
}