<?php

namespace Modules\Interaction\Rules;

use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Database\Eloquent\Relations\Relation;

class TargetRule implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $alias = $this->data['target_type'];
        $class = Relation::getMorphedModel($alias);

        if (! class_exists($class)) {
            $fail('对象模型不存在');
            return;
        }

        if (! $class::where('id', $value)->exists()) {
            $fail('对象数据不存在');
        }
    }
}