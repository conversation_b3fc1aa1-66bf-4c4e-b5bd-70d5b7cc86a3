<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Interaction\Enums\CommentStatus;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('interaction_comments', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->morphs('commentable');
            $table->decimal('star')
                ->default(0)
                ->comment('星级评价');
            $table->pictures();
            $table->text('content')
                ->nullable()
                ->comment('评论内容');
            $table->enum('status', CommentStatus::values())
                ->default(CommentStatus::HIDDEN->value)
                ->comment('显示状态');
            $table->timestamps();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('interaction_comments');
    }
};
