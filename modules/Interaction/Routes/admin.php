<?php

use Illuminate\Support\Facades\Route;
use Modules\Interaction\Http\Controllers\Admin\CommentController;
use Modules\Interaction\Http\Controllers\Admin\FavoriteController;
use Modules\Interaction\Http\Controllers\Admin\FollowController;
use Modules\Interaction\Http\Controllers\Admin\SubscribeController;

Route::get('comments', [CommentController::class, 'index'])->name('comments.index');
Route::delete('comments/{comment}', [CommentController::class, 'destroy']);
Route::get('favorites', [FavoriteController::class, 'index']);
Route::get('subscribes', [SubscribeController::class, 'index']);
Route::get('follows', [FollowController::class, 'index']);
