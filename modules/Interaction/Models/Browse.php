<?php

namespace Modules\Interaction\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Browse extends Model
{
    use BelongsToUser;

    protected $table = 'interaction_browses';

    public function browsable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeWithType(Builder $query, string $type): Builder
    {
        return $query->where('favoriteable_type', app($type)->getMorphClass());
    }

    public function scopeWithTarget(Builder $query, array $target): Builder
    {
        return $query->where('favoriteable_type', $target['target_type'])
            ->where('favoriteable_id', $target['target_id']);
    }

    public function scopeOfItem(Builder $query, Model $item): Builder
    {
        return $query->where('favoriteable_type', $item->getMorphClass())
            ->where('favoriteable_id', $item->getKey());
    }
}
