<?php

namespace Modules\Interaction\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;

class CommentStep extends Model
{
    use BelongsToUser;

    protected $table = 'interaction_comment_steps';

    protected static function boot(): void
    {
        parent::boot();
    }

    public function scopeWithTarget(Builder $query, Model $model): Builder
    {
        return $query->where('target_type', $model->getMorphClass())
            ->where('target_id', $model->getKey());
    }
}
