<?php

namespace Modules\Interaction\Models;

use App\Jobs\Interaction\FavoriteJob;
use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Favorite extends Model
{
    use BelongsToUser, SoftDeletes;

    protected $table = 'interaction_favorites';

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub
        self::created(function (Model $model) {
        });
    }

    public function favoriteable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeWithType(Builder $query, string $type): Builder
    {
        return $query->where('favoriteable_type', app($type)->getMorphClass());
    }

    public function scopeWithTarget(Builder $query, array $target): Builder
    {
        return $query->where('favoriteable_type', $target['target_type'])
            ->where('favoriteable_id', $target['target_id']);
    }

    public function scopeOfItem(Builder $query, Model $item): Builder
    {
        return $query->where('favoriteable_type', $item->getMorphClass())
            ->where('favoriteable_id', $item->getKey());
    }
}
