<?php

namespace Modules\Interaction\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Modules\Interaction\Enums\CommentStatus;
use Modules\Interaction\Models\Traits\CommentLikeAbleTrait;
use Modules\User\Models\UserInfo;

class Comment extends Model
{
    use BelongsToUser,
        HasCovers,
        CommentLikeAbleTrait;

    protected $table = 'interaction_comments';

    protected $casts = [
        'status' => CommentStatus::class,
    ];

    protected static function boot(): void
    {
        parent::boot();

        self::created(function ($model) {
        });
    }

    public function userInfo(): BelongsTo
    {
        return $this->belongsTo(UserInfo::class, 'user_id', 'user_id');
    }

    public function children(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    public function commentable(): MorphTo
    {
        return $this->morphTo()->withTrashed();
    }

    public function scopeWithType(Builder $query, string $type): Builder
    {
        return $query->where('commentable_type', app($type)->getMorphClass());
    }

    public function scopeWithTarget(Builder $query, array $target): Builder
    {
        return $query->where('commentable_type', app($target['target_type'])->getMorphClass())
            ->where('commentable_id', $target['target_id']);
    }
}
