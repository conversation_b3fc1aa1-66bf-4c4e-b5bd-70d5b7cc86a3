<?php

namespace Modules\Interaction\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InteractionUserResource extends JsonResource
{
    public function toArray(Request $request):array
    {
        return [
            'user_id'  => $this->id,
            'username' => $this->username,
            'nickname' => $this->info?->nickname,
            'avatar'   => $this->info?->avatar_url,
        ];
    }
}
