<?php

namespace Modules\Interaction\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LikeResource extends JsonResource
{
    public function __construct($resource, protected bool $showTarget = true, protected bool $showUser = true)
    {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        return [
            'like_id'    => $this->id,
            'target'     => $this->when($this->showTarget, [
                'type'  => $this->likeable_type,
                'id'    => $this->likeable_id,
                'title' => method_exists($this->likeable, 'getTitle') ? $this->likeable->getTitle() : '',
            ]),
            'user'       => $this->when($this->showUser, new InteractionUserResource($this->user)),
            'created_at' => (string) $this->created_at,
        ];
    }
}
