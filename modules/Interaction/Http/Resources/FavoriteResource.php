<?php

namespace Modules\Interaction\Http\Resources;

use App\Models\ZoneGoods;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FavoriteResource extends JsonResource
{
    public function __construct($resource, protected bool $showTarget = true, protected bool $showUser = true)
    {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        return [
            'favorite_id' => $this->id,
            'target'      => $this->when($this->showTarget, [
                'type'  => $this->favoriteable_type,
                'title' => method_exists($this->favoriteable, 'getTitle') ? $this->favoriteable->getTitle() : '',
                'cover' => method_exists($this->favoriteable, 'getCover') ? $this->favoriteable->getCover() : '',
                'price' => method_exists($this->favoriteable, 'getPrice') ? $this->favoriteable->getPrice() : '0.00',
            ]),
            'user'        => $this->when($this->showUser, new InteractionUserResource($this->user)),
            'created_at'  => (string) $this->created_at,
        ];
    }
}