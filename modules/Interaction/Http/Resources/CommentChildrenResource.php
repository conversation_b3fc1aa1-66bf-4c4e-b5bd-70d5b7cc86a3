<?php

namespace Modules\Interaction\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CommentChildrenResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'user'       => [
                'user_id'  => $this->user_id,
                'nickname' => $this->userInfo?->nickname,
                'avatar'   => $this->userInfo?->avatar_url,
            ],
            'content'    => $this->content,
            'pictures'   => $this->picture_urls,
            'created_at' => (string) $this->created_at,
        ];
    }
}