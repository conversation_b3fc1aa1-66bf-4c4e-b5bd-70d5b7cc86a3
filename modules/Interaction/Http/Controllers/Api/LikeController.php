<?php

namespace Modules\Interaction\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\Interaction\Http\Requests\InteractionRequest;
use Modules\Interaction\Http\Requests\LikeRequest;
use Modules\Interaction\Http\Resources\LikeCollection;
use Modules\Interaction\Models\Like;

class LikeController extends ApiController
{
    public function index(): JsonResponse
    {
        $list = Like::ofUser(Api::user())->latest()->paginate();

        return $this->success(new LikeCollection($list, true, false));
    }

    public function store(LikeRequest $request): JsonResponse
    {
        $data = $request->safe()->merge(['user_id' => Api::id()]);
        $like = Like::where($data->all())->first();

        if ($like) {
            $like->delete();
            return $this->success('DELETED');
        } else {
            Like::create($data->all());
            return $this->success('CREATED');
        }
    }

    public function show(InteractionRequest $request): JsonResponse
    {
        $result = Like::withTarget($request->safe()->only(['target_type', 'target_id']))
            ->ofUser(Api::user())
            ->exists();

        return $this->success([
            'is_liked' => $result,
        ]);
    }

    public function destroy(Like $like): JsonResponse
    {
        $this->checkPermission($like);
        $like->delete();
        return $this->success();
    }
}