<?php

namespace Modules\Interaction\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\Interaction\Http\Requests\CommentRequest;
use Modules\Interaction\Http\Resources\CommentCollection;
use Modules\Interaction\Models\Comment;

class CommentController extends ApiController
{
    /**
     * Notes   : 我的评论列表
     *
     * @Date   : 2023/9/18 17:15
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        $list = Comment::ofUser(Api::user())->latest()->paginate();

        return $this->success(new CommentCollection($list, true, false));
    }

    /**
     * Notes   : 提交评论
     *
     * @Date   : 2023/9/18 17:15
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Interaction\Http\Requests\CommentRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CommentRequest $request): JsonResponse
    {
        $data = $request->safe()->merge(['user_id' => Api::id()]);
        Comment::create($data->all());

        return $this->success('');
    }

    /**
     * Notes   : 删除我的评论
     *
     * @Date   : 2023/9/18 17:15
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Interaction\Models\Comment  $comment
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function destroy(Comment $comment): JsonResponse
    {
        $this->checkPermission($comment);
        $comment->delete();

        return $this->success();
    }
}