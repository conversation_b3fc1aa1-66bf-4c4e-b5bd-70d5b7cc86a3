<?php

namespace Modules\Interaction\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\Interaction\Http\Requests\FollowRequest;
use Modules\Interaction\Http\Requests\InteractionRequest;
use Modules\Interaction\Http\Resources\FollowCollection;
use Modules\Interaction\Models\Follow;

class FollowController extends ApiController
{
    public function index(): JsonResponse
    {
        $list = Follow::ofUser(Api::user())->latest()->paginate();

        return $this->success(new FollowCollection($list, true, false));
    }

    public function approved()
    {
        $list = Follow::ofUser(Api::user())->whereNotNull('accepted_at')->latest()->paginate();

        return $this->success(new FollowCollection($list, true, false));
    }

    public function notApproved()
    {
        $list = Follow::ofUser(Api::user())->whereNull('accepted_at')->latest()->paginate();

        return $this->success(new FollowCollection($list, true, false));
    }

    public function store(FollowRequest $request): JsonResponse
    {
        $data   = $request->safe()->merge(['user_id' => Api::id()]);
        $follow = Follow::where($data->all())->first();

        if ($follow) {
            $follow->delete();
            return $this->success('DELETED');
        } else {
            Follow::create($data->all());
            return $this->success('CREATED');
        }
    }

    public function show(InteractionRequest $request): JsonResponse
    {
        $result = Follow::withTarget($request->safe()->only(['target_type', 'target_id']))
            ->ofUser(Api::user())
            ->first();

        return $this->success([
            'is_followed' => (bool) $result,
            'accepted_at' => $result?->accepted_at,
        ]);
    }

    public function destroy(Follow $follow): JsonResponse
    {
        $this->checkPermission($follow);
        $follow->delete();
        return $this->success();
    }
}