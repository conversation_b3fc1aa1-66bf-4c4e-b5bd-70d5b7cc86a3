<?php

namespace Modules\Interaction\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Interaction\Models\Subscribe;

class SubscribeController extends AdminController
{
    protected string $title = '浏览记录';

    public function grid(): Grid
    {
        return Grid::make(Subscribe::with(['user.info', 'subscribable'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableBatchDelete();
            $grid->disableEditButton();
            $grid->disableActions();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');
            });

            $grid->column('用户')
                ->display(fn() => $this->user->showName);
            $grid->column('图片')
                ->display(function () {
                    if (method_exists($this->subscribable, 'getCover')) {
                        return $this->subscribable->getCover();
                    }
                })->image('',100);
            $grid->column('标题')
                ->display(function () {
                    if (method_exists($this->subscribable, 'getTitle')) {
                        return $this->subscribable->getTitle();
                    } else {
                        return sprintf('%s:%s', $this->subscribable_type, $this->subscribable_id);
                    }
                });

            $grid->column('created_at', '浏览时间');
        });
    }

}