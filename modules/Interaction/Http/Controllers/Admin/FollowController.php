<?php

namespace Modules\Interaction\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Interaction\Models\Follow;

class FollowController extends AdminController
{
    protected string $title = '关注';

    public function grid(): Grid
    {
        return Grid::make(Follow::with(['user.info', 'followable.info'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableBatchDelete(false);
            $grid->disableEditButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '关注方')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
                $filter->equal('followable_id', '关注对象')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
            });

            $grid->column('关注方')
                ->display(fn() => $this->user->showName);
            $grid->column('关注对象')
                ->display(fn() => $this->followable->showName);
            $grid->column('是否双向关注')
                ->display(function () {
                    return $this->isReverseFollowing() ? '是' : '否';
                });
            $grid->column('created_at', '关注时间');
        });
    }

}