<?php

namespace Modules\Interaction\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use App\Rules\FileExistsRule;
use Modules\Interaction\Rules\CommentRule;

class CommentRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'commentable_type' => 'required|alpha',
            'commentable_id'   => [
                'required',
                'bail',
                new CommentRule,
            ],
            'star'             => [
                'sometimes',
                'nullable',
                'bail',
                'decimal:0,2',
                'min:0',
                'max:5',
            ],
            'pictures'         => [
                'sometimes',
                'bail',
                'nullable',
                'array',
            ],
            'pictures.*'       => [
                'required',
                'bail',
                new FileExistsRule('图片文件不存在'),
            ],
            'content'          => 'required|bail|min:5|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'commentable_type.required' => '评论对象必须',
            'commentable_id.required'   => '评论对象必须',
            'star.decimal'              => '评分只能是数字',
            'star.min'                  => '评分不能低于:min',
            'star.max'                  => '评分不能大于:max',
            'pictures.array'            => '评论图片格式不正确',
            'pictures.*.required'       => '评论图片必须',
            'content.required'          => '评论内容必须',
            'content.min'               => '评论内容最短:min个字符',
            'content.max'               => '评论内容最长:max个字符',
        ];
    }
}
