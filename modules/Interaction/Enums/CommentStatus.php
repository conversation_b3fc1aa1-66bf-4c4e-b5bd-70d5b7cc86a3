<?php

namespace Modules\Interaction\Enums;

use App\Traits\EnumMethods;

enum CommentStatus: string
{
    use EnumMethods;

    case NORMAL = 'n';
    case HIDDEN = 'h';
    case FAILED = 'f';
    case DELETE = 'd';

    const STATUS_MAP = [
        self::NORMAL->value => '显示',
        self::HIDDEN->value => '审核中',
        self::FAILED->value => '不通过',
        self::DELETE->value => '已删除',
    ];

    const LABEL_MAP = [
        self::NORMAL->value => 'success',
        self::HIDDEN->value => 'info',
        self::FAILED->value => 'danger',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}