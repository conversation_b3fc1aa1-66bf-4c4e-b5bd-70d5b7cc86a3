<?php

namespace Modules\Notification\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Notifications\DatabaseNotification;
use Modules\Notification\Http\Resources\NotificationCollection;
use Modules\Notification\Http\Resources\NotificationGroupResource;
use Modules\Notification\Http\Resources\NotificationResource;
use Exception;
class NotificationController extends ApiController
{
    public function group()
    {
        $group = DatabaseNotification::whereMorphedTo('notifiable', Api::user())
            ->select('type')
            ->distinct()
            ->get();

        return $this->success(NotificationGroupResource::collection($group));
    }

    /**
     * Notes   : 消息列表
     *
     * @Date   : 2023/8/10 10:35
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $user     = Api::user();
        $resource = $user->notifications()
            ->select(['id', 'type', 'data', 'read_at', 'created_at'])
            ->when($request->type, function (Builder $builder, $type) {
                $builder->where('type', $type);
            })
            ->paginate($request->limit);

        return $this->success(new NotificationCollection($resource));
    }

    /**
     * Notes   : 消息详情
     *
     * @Date   : 2023/10/25 10:03
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Notifications\DatabaseNotification  $notification
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function show(DatabaseNotification $notification)
    {
        $this->checkPermission($notification);
        $notification->markAsRead();

        return $this->success(new NotificationResource($notification));
    }

    protected function checkPermission(Model $model, string $message = '数据不存在')
    {
        if ($model->notifiable->isNot(Api::user())) {
            throw new Exception($message, 404);
        }
    }

    /**
     * Notes   : 消息数量
     *
     * @Date   : 2023/5/10 17:36
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function count(Request $request): JsonResponse
    {
        $user = Api::user();

        return $this->success([
            'total'  => $user->notifications()
                ->when($request->type, function (Builder $builder, $type) {
                    $builder->where('type', $type);
                })->count(),
            'unread' => $user->unreadNotifications()
                ->when($request->type, function (Builder $builder, $type) {
                    $builder->where('type', $type);
                })->count(),
        ]);
    }

    /**
     * Notes   : 标记一条已读
     *
     * @Date   : 2023/5/10 17:38
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Notifications\DatabaseNotification  $notification
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function markAsRead(DatabaseNotification $notification): JsonResponse
    {
        $this->checkPermission($notification);
        $notification->markAsRead();

        return $this->success();
    }

    /**
     * Notes   : 标记全部已读
     *
     * @Date   : 2023/5/10 17:38
     * <AUTHOR> <Jason.C>
     */
    public function maskAllAsRead(Request $request): JsonResponse
    {
        $user          = Api::user();
        $notifications = $user->unreadNotifications()
            ->when($request->type, function (Builder $builder, $type) {
                $builder->where('type', $type);
            })
            ->get();
        $notifications->each->markAsRead();

        return $this->success();
    }

    /**
     * Notes   : 删除全部已读消息
     *
     * @Date   : 2024/1/16 10:00
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteAllRead(Request $request): JsonResponse
    {
        $user = Api::user();
        $user->readNotifications()
            ->when($request->type, function (Builder $builder, $type) {
                $builder->where('type', $type);
            })
            ->delete();

        return $this->success();
    }

    /**
     * Notes   : 删除消息
     *
     * @Date   : 2023/10/25 10:09
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Notifications\DatabaseNotification  $notification
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function destroy(DatabaseNotification $notification)
    {
        $this->checkPermission($notification);
        $notification->delete();

        return $this->success();
    }
}
