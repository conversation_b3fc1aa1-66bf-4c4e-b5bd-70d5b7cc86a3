<?php

namespace Modules\Notification\Http\Controllers\Admin;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Notifications\DatabaseNotification;

class NotificationController extends AdminController
{
    protected string $title = '消息通知';

    public function grid(): Grid
    {
        return Grid::make(DatabaseNotification::latest(), function (Grid $grid) {
            $grid->disableBatchDelete(false);
            $grid->disableCreateButton();

            $grid->filter(function (Grid\Filter $filter) {
                $types = DatabaseNotification::select('type')
                    ->distinct()
                    ->pluck('type');

                $array = [];
                foreach ($types as $type) {
                    $array[$type] = $type::getTitle();
                }

                $filter->equal('type', '通知类型')
                    ->select($array);

                $filter->between('read_at', '阅读时间')
                    ->datetime();
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit();
            });

            $grid->column('通知用户')
                ->display(fn() => $this->notifiable?->showName);
            $grid->column('通知类型')
                ->display(function () {
                    if (method_exists($this->type, 'getTitle')) {
                        return $this->type::getTitle();
                    } else {
                        return $this->type;
                    }
                });
            $grid->column('data.content', '消息内容');
            $grid->column('read_at')
                ->dateFormat();
            $grid->column('created_at')
                ->dateFormat();
        });
    }

    public function form(): Form
    {
        return Form::make(DatabaseNotification::class, function (Form $form) {
        });
    }
}