<?php

namespace Modules\Notification\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'notification_id' => $this->resource->id,
            'title'           => $this->when(
                method_exists($this->resource->type, 'getTitle'),
                $this->resource->type::getTitle(),
                $this->resource->type
            ),
            'data'            => $this->when(
                method_exists($this->resource->type, 'getData'),
                $this->resource->type::getData($this->resource),
                $this->resource->data
            ) ?: (object) [],
            'read'            => $this->resource->read(),
            'read_at'         => (string) $this->resource->read_at,
            'created_at'      => (string) $this->resource->created_at,
            'diff_for_humans' => $this->resource->created_at->diffForHumans(),
        ];
    }
}
