<?php

namespace Modules\Notification\Http\Resources;

use App\Facades\Api;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Notifications\DatabaseNotification;

class NotificationGroupResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $model  = DatabaseNotification::whereMorphedTo('notifiable', Api::user())
            ->where('type', $this->type);
        $newest = $model->whereNull('read_at')->first();
        return [
            'title'  => method_exists($this->type, 'getTitle') ? $this->type::getTitle() : '',
            'group'  => $this->type,
            'total'  => $model->count(),
            'unread' => $model->whereNull('read_at')->count(),
            'newest' => $this->when(! is_null($newest), new NotificationResource($newest), null),
        ];
    }
}