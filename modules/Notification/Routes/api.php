<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Modules\Notification\Http\Controllers\Api\NotificationController;

Route::group([
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->get('group', [NotificationController::class, 'group']);
    # 我的通知
    $router->get('notifications', [NotificationController::class, 'index']);
    $router->get('notifications/count', [NotificationController::class, 'count']);
    $router->get('notifications/{notification}', [NotificationController::class, 'show']);
    # 标记已读
    $router->post('notifications/{notification}', [NotificationController::class, 'markAsRead']);
    $router->post('notifications', [NotificationController::class, 'maskAllAsRead']);
    $router->delete('notifications/{notification}', [NotificationController::class, 'destroy']);
    # 删除全部已读
    $router->delete('notifications', [NotificationController::class, 'deleteAllRead']);
});
