<?php

namespace Modules\Notification\Channels;

use App\Packages\ImPush\Push\Client;
use Illuminate\Notifications\Notification;
use Modules\Notification\Messages\IMMessage;

/**
 * im推送
 *
 */
class IMChannel
{
    protected Client $client;

    public function __construct(Client $client)
    {
        $this->client = $client;
    }

    public function send($notifiable, Notification $notification)
    {
        $message = $notification->toIM($notifiable);

        if ($message instanceof IMMessage) {
            $this->sendToIM($message);
        }
    }

    protected function sendToIM(IMMessage $message)
    {
        $userIds = $message->getUserIds();
        
        if (empty($userIds)) {
            // 全部发送
            $this->client->push(
                $message->getContent(),
                $message->getTitle()
            );
        } else {
            // 指定用户发送
            $this->client->batchPush(
                $userIds,
                $message->getContent(),
                $message->getTitle()
            );
        }
    }
} 