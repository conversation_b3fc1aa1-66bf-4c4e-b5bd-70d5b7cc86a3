<?php

namespace Modules\Notification;

use App\Contracts\ModuleBootstrap;
use Illuminate\Support\Facades\Artisan;

class Bootstrap implements ModuleBootstrap
{
    protected static string $menuKey = 'notification-module';

    public static function install(): void
    {
        Artisan::call('migrate', [
            '--path' => 'modules/Notification/Database/Migrations',
        ]);
        # 载入菜单
        self::createAdminMenu();
    }

    public static function uninstall(): void
    {
        Artisan::call('migrate:reset', [
            '--path' => 'modules/Notification/Database/Migrations',
        ]);
        # 删除菜单
        self::deleteAdminMenu();
    }

    public static function upgrade(): void
    {
    }

    public static function createAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');

        $main = $menuModel::create([
            'parent_id' => 0,
            'order'     => 89,
            'title'     => '通知中心',
            'icon'      => 'fa-bell-o '.self::$menuKey,
        ]);

        $main->children()->createMany([
            [
                'order' => 1,
                'title' => '数据看板',
                'icon'  => 'fa-dashboard '.self::$menuKey,
                'uri'   => 'notification/',
            ],
            [
                'order' => 2,
                'title' => '消息列表',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'notification/notifications',
            ],
        ]);
    }

    public static function deleteAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');
        $menuModel::where('icon', 'like', '%'.self::$menuKey)->delete();
    }
}