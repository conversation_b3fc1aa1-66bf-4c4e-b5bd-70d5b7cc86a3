# 文生音频

适用于通过输入提示词，生成一段音频（支持生成音效和背景音乐 BGM）

```
POST https://api.vidu.cn/ent/v2/text2audio
```

## 请求头

| 字段          | 值                   | 描述                               |
| :------------ | :------------------- | :--------------------------------- |
| Content-Type  | application/json     | 数据交换格式                       |
| Authorization | Token {your api key} | 将 {your api key} 替换为您的 token |

## 请求体

| 参数名       | 类型   | 必填 | 描述                                                         |
| :----------- | :----- | :--- | :----------------------------------------------------------- |
| model        | String | 是   | 模型名称 可选值：audio1.0                                    |
| prompt       | String | 是   | 文本提示词 用于生成音频的描述。字符长度不能超过 1500 个字符  |
| duration     | Float  | 可选 | 音频时长 默认 10，可选范围：2～10 秒内                       |
| seed         | Int    | 可选 | 随机种子 随机种子，若不传或传0则自动生成随机数，传固定值生成确定性结果 |
| callback_url | String | 可选 | Callback 协议 需要您在创建任务时主动设置 callback_url，请求方法为 POST，当视频生成任务有状态变化时，Vidu 将向此地址发送包含任务最新状态的回调请求。回调请求内容结构与查询任务API的返回体一致 回调返回的"status"包括以下状态： - processing 任务处理中 - success 任务完成（如发送失败，回调三次） - failed 任务失败（如发送失败，回调三次） |

```
curl -X POST -H "Authorization: Token {your_api_key}" -H "Content-Type: application/json" -d '
{
    "model": "audio1.0"
    "prompt": "清晨的鸟叫声",
    "duration": "10",
    "seed": "0"
}' https://api.vidu.cn/ent/v2/text2audio
```

## 响应体

| 字段       | 类型   | 描述                                                         |
| :--------- | :----- | :----------------------------------------------------------- |
| task_id    | String | Vidu 生成的任务ID                                            |
| state      | String | 处理状态 可选值： -created 创建成功 -queueing 任务排队中 -processing 任务处理中 -success 任务成功 -failed 任务失败 |
| model      | String | 本次调用的模型名称                                           |
| prompt     | String | 本次调用的提示词参数                                         |
| duration   | Int    | 本次调用的视频时长参数                                       |
| seed       | Int    | 本次调用的随机种子参数                                       |
| created_at | String | 任务创建时间                                                 |

```
{
  "task_id": "your_task_id_here",
  "state": "created",
  "model": "audio1.0",
  "prompt": "清晨的鸟叫声",
  "duration": 10,
  "seed": random_number,
  "created_at": "2025-01-01T15:41:31.968916Z"
}
```